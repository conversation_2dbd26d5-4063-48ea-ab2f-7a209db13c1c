{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../dashboard/type.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../Shared/layout/layout.component\";\nfunction FournisseurComponent_div_46_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de Marque est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_46_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de marque doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtemplate(1, FournisseurComponent_div_46_div_1_Template, 2, 0, \"div\", 62);\n    i0.ɵɵtemplate(2, FournisseurComponent_div_46_div_2_Template, 2, 0, \"div\", 62);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(45);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"minlength\"]);\n  }\n}\nfunction FournisseurComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Nom de Fournisseur est requis (min 3 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"\\nEmail est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de t\\u00E9l\\u00E9phone est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \" Adesse est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FournisseurComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r7.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.notification.message, \" \");\n  }\n}\nfunction FournisseurComponent_tr_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 64)(2, \"div\")(3, \"h5\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 66);\n    i0.ɵɵtext(6, \"Fournisseur\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"td\", 64)(8, \"span\", 67);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 64)(11, \"span\", 67);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"td\", 64)(14, \"span\", 68);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 64)(17, \"span\", 66);\n    i0.ɵɵtext(18, \"12/01/2024\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"td\", 69)(20, \"div\", 70)(21, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function FournisseurComponent_tr_104_Template_button_click_21_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const fournisseur_r11 = restoredCtx.$implicit;\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.updateFournisseur(fournisseur_r11));\n    });\n    i0.ɵɵtext(22, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function FournisseurComponent_tr_104_Template_button_click_23_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r13);\n      const fournisseur_r11 = restoredCtx.$implicit;\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.deleteFournisseur(fournisseur_r11.idFournisseur));\n    });\n    i0.ɵɵtext(24, \" Supprimer \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const fournisseur_r11 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(fournisseur_r11.nomFournisseur);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(fournisseur_r11.emailFournisseur);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(fournisseur_r11.telephoneFournisseur);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", fournisseur_r11.adresseFournisseur);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r11.adresseFournisseur, \" \");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class FournisseurComponent {\n  constructor(http, authservice, fb) {\n    this.http = http;\n    this.authservice = authservice;\n    this.fb = fb;\n    this.fournisseurs = [];\n    this.isModalOpen = false;\n    this.submitted = false;\n    this.fournisseur1 = {\n      idFournisseur: 0,\n      nomFournisseur: '',\n      adresseFournisseur: '',\n      emailFournisseur: '',\n      telephoneFournisseur: '',\n      equipements: []\n    };\n    // Notification system\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n  }\n  ngOnInit() {\n    this.fournisseurForm = this.fb.group({\n      nomFournisseur: ['', [Validators.required, Validators.minLength(3)]],\n      adresseFournisseur: ['', [Validators.required, Validators.minLength(4)]],\n      emailFournisseur: ['', [Validators.required, Validators.email]],\n      telephoneFournisseur: ['', [Validators.required, Validators.minLength(8)]]\n    });\n    this.GetAllFournisseur();\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n    this.resetForm();\n  }\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  onRegister() {\n    this.submitted = true;\n    if (this.fournisseurForm.invalid) {\n      this.fournisseurForm.markAllAsTouched(); // pour afficher les erreurs\n      return; // empêche la soumission si formulaire invalide\n    }\n\n    this.authservice.addFournisseur(this.fournisseurForm.value).subscribe({\n      next: response => {\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Fournisseur ajouté avec succès !');\n        this.fournisseurs.push(response);\n        this.closeModal();\n        this.fournisseurForm.reset();\n        this.submitted = false;\n        window.scrollTo({\n          top: 0,\n          behavior: 'smooth'\n        });\n      },\n      error: error => {\n        console.error('Registration failed:', error);\n        alert('Échec de l’enregistrement');\n      }\n    });\n  }\n  GetAllFournisseur() {\n    return this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n      console.log(\"Types reçus : \", JSON.stringify(this.fournisseurs, null, 2));\n    });\n  }\n  // Méthode pour supprimer un fournisseur\n  deleteFournisseur(id) {\n    if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {\n      // Supposons que le service a une méthode deleteFournisseur\n      // Si elle n'existe pas, vous devrez l'ajouter au service\n      this.authservice.deleteFournisseur(id).subscribe({\n        next: () => {\n          this.showNotification('success', 'Fournisseur supprimé avec succès !');\n          this.fournisseurs = this.fournisseurs.filter(f => f.idFournisseur !== id);\n        },\n        error: error => {\n          console.error('Delete failed:', error);\n          this.showNotification('error', 'Échec de la suppression du fournisseur.');\n        }\n      });\n    }\n  }\n  updateFournisseur(fournisseur) {\n    this.fournisseur1 = {\n      ...fournisseur\n    };\n    this.fournisseurForm.patchValue({\n      nomFournisseur: fournisseur.nomFournisseur,\n      adresseFournisseur: fournisseur.adresseFournisseur,\n      emailFournisseur: fournisseur.emailFournisseur,\n      telephoneFournisseur: fournisseur.telephoneFournisseur\n    });\n    this.openModal();\n  }\n  // Method for handling template-driven form update\n  onUpdateClick(form) {\n    if (form.valid) {\n      const updatedFournisseur = {\n        ...this.fournisseur1\n      };\n      this.authservice.updateFournisseur(updatedFournisseur).subscribe({\n        next: response => {\n          console.log('Fournisseur updated successfully', response);\n          this.showNotification('success', 'Fournisseur modifié avec succès !');\n          const index = this.fournisseurs.findIndex(f => f.idFournisseur === this.fournisseur1.idFournisseur);\n          if (index !== -1) {\n            this.fournisseurs[index] = response;\n          }\n          // Close the update modal (you might need to add a separate flag for this)\n          this.closeModal();\n          window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n          });\n        },\n        error: error => {\n          console.error('Update failed:', error);\n          this.showNotification('error', 'Échec de la modification du fournisseur.');\n        }\n      });\n    } else {\n      console.log('Form is invalid');\n    }\n  }\n  // Method to handle form submission for both add and update\n  onSubmit() {\n    this.submitted = true;\n    if (this.fournisseurForm.invalid) {\n      this.fournisseurForm.markAllAsTouched();\n      return;\n    }\n    if (this.fournisseur1.idFournisseur === 0) {\n      // Add new fournisseur\n      this.onRegister();\n    } else {\n      // Update existing fournisseur\n      const updatedFournisseur = {\n        ...this.fournisseur1,\n        ...this.fournisseurForm.value\n      };\n      this.authservice.updateFournisseur(updatedFournisseur).subscribe({\n        next: response => {\n          console.log('Fournisseur updated successfully', response);\n          this.showNotification('success', 'Fournisseur modifié avec succès !');\n          // Update the fournisseur in the local array\n          const index = this.fournisseurs.findIndex(f => f.idFournisseur === this.fournisseur1.idFournisseur);\n          if (index !== -1) {\n            this.fournisseurs[index] = response;\n          }\n          this.closeModal();\n          this.resetForm();\n          window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n          });\n        },\n        error: error => {\n          console.error('Update failed:', error);\n          this.showNotification('error', 'Échec de la modification du fournisseur.');\n        }\n      });\n    }\n  }\n  // Reset form and fournisseur1 object\n  resetForm() {\n    this.fournisseurForm.reset();\n    this.submitted = false;\n    this.fournisseur1 = {\n      idFournisseur: 0,\n      nomFournisseur: '',\n      adresseFournisseur: '',\n      emailFournisseur: '',\n      telephoneFournisseur: '',\n      equipements: []\n    };\n  }\n  static {\n    this.ɵfac = function FournisseurComponent_Factory(t) {\n      return new (t || FournisseurComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.TypeService), i0.ɵɵdirectiveInject(i3.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FournisseurComponent,\n      selectors: [[\"app-fournisseur\"]],\n      decls: 116,\n      vars: 12,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\", 2, \"width\", \"200px\", 3, \"click\"], [1, \"icon\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un type...\"], [1, \"icon-search\"], [\"id\", \"updateModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"updateModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [1, \"modal-content\", \"shadow\", \"rounded-4\"], [\"id\", \"updateModalLabel\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [\"updateForm\", \"ngForm\"], [1, \"mb-4\"], [\"for\", \"nomFournisseur\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"type\", \"text\", \"id\", \"nomFournisseur\", \"name\", \"nomFournisseur\", \"required\", \"\", \"minlength\", \"3\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nomMarque\", \"ngModel\"], [\"style\", \"color:red\", 4, \"ngIf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"px-4\", 3, \"click\"], [1, \"bg-light\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"for\", \"numSerie\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"nomFournisseur\", \"type\", \"text\", \"formControlName\", \"nomFournisseur\", \"placeholder\", \"Nom du Fournisseur\", 1, \"form-inputp\"], [\"for\", \"emailFournisseur\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"emailFournisseur\", \"type\", \"text\", \"formControlName\", \"emailFournisseur\", \"placeholder\", \"<EMAIL>\", 1, \"form-inputp\"], [\"for\", \"telephoneFournisseur\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"telephoneFournisseur\", \"type\", \"text\", \"formControlName\", \"telephoneFournisseur\", \"placeholder\", \"012345678\", 1, \"form-inputp\"], [\"for\", \"adresseFournisseur\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"adresseFournisseur\", \"type\", \"text\", \"formControlName\", \"adresseFournisseur\", \"placeholder\", \"Adresse\", 1, \"form-inputp\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-2\", \"table-large\"], [\"scope\", \"col\", 1, \"px-3\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-3\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [2, \"color\", \"red\"], [4, \"ngIf\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-3\"], [1, \"fw-bold\", \"mb-1\", \"fs-3\"], [1, \"fw-normal\", \"text-muted\", \"fs-4\"], [1, \"fw-normal\", \"fs-4\"], [1, \"fw-normal\", \"text-truncate\", \"fs-4\", 2, \"max-width\", \"300px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-3\", \"text-end\"], [1, \"d-flex\", \"justify-content-end\", \"gap-2\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-outline-primary\", \"btn-action\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-outline-danger\", \"btn-action\", 3, \"click\"]],\n      template: function FournisseurComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r15 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents fournisseurs \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function FournisseurComponent_Template_button_click_24_listener() {\n            return ctx.openModal();\n          });\n          i0.ɵɵelementStart(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouveau fournisseur \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14);\n          i0.ɵɵelement(30, \"input\", 15)(31, \"span\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 17)(33, \"div\", 18)(34, \"div\", 19)(35, \"h5\", 20);\n          i0.ɵɵtext(36, \"\\uD83D\\uDCDD Modifier les informations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"button\", 21);\n          i0.ɵɵelementStart(38, \"div\", 22)(39, \"form\", null, 23)(41, \"div\", 24)(42, \"label\", 25);\n          i0.ɵɵtext(43, \"Nom du type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"input\", 26, 27);\n          i0.ɵɵlistener(\"ngModelChange\", function FournisseurComponent_Template_input_ngModelChange_44_listener($event) {\n            return ctx.fournisseur1.nomFournisseur = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(46, FournisseurComponent_div_46_Template, 3, 2, \"div\", 28);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(47, \"div\", 29)(48, \"button\", 30);\n          i0.ɵɵtext(49, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function FournisseurComponent_Template_button_click_50_listener() {\n            i0.ɵɵrestoreView(_r15);\n            const _r0 = i0.ɵɵreference(40);\n            return i0.ɵɵresetView(ctx.onUpdateClick(_r0));\n          });\n          i0.ɵɵtext(51, \" \\uD83D\\uDCBE Sauvegarder \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(52, \"body\", 32)(53, \"div\", 33);\n          i0.ɵɵlistener(\"click\", function FournisseurComponent_Template_div_click_53_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(54, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function FournisseurComponent_Template_div_click_54_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(55, \"span\", 35);\n          i0.ɵɵlistener(\"click\", function FournisseurComponent_Template_span_click_55_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(56, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"h3\", 36);\n          i0.ɵɵtext(58, \"Ajouter un nouveau fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"form\", 37);\n          i0.ɵɵlistener(\"ngSubmit\", function FournisseurComponent_Template_form_ngSubmit_59_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(60, \"br\")(61, \"br\");\n          i0.ɵɵelementStart(62, \"label\", 38);\n          i0.ɵɵtext(63, \"Nom Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 39);\n          i0.ɵɵtemplate(65, FournisseurComponent_div_65_Template, 2, 0, \"div\", 28);\n          i0.ɵɵelementStart(66, \"label\", 40);\n          i0.ɵɵtext(67, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"input\", 41);\n          i0.ɵɵtemplate(69, FournisseurComponent_div_69_Template, 2, 0, \"div\", 28);\n          i0.ɵɵelementStart(70, \"label\", 42);\n          i0.ɵɵtext(71, \"Telephone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(72, \"input\", 43);\n          i0.ɵɵtemplate(73, FournisseurComponent_div_73_Template, 2, 0, \"div\", 28);\n          i0.ɵɵelementStart(74, \"label\", 44);\n          i0.ɵɵtext(75, \"Adresse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(76, \"textarea\", 45);\n          i0.ɵɵtemplate(77, FournisseurComponent_div_77_Template, 2, 0, \"div\", 28);\n          i0.ɵɵelement(78, \"br\");\n          i0.ɵɵelementStart(79, \"button\", 46);\n          i0.ɵɵtext(80, \" Enregistrer \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(81, FournisseurComponent_div_81_Template, 2, 2, \"div\", 47);\n          i0.ɵɵelementStart(82, \"div\", 7)(83, \"div\", 48)(84, \"div\", 49)(85, \"div\", 50)(86, \"div\", 51)(87, \"div\", 52)(88, \"table\", 53)(89, \"thead\")(90, \"tr\")(91, \"th\", 54);\n          i0.ɵɵtext(92, \"Nom du Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"th\", 54);\n          i0.ɵɵtext(94, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"th\", 54);\n          i0.ɵɵtext(96, \"T\\u00E9l\\u00E9phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"th\", 54);\n          i0.ɵɵtext(98, \"Adresse\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"th\", 54);\n          i0.ɵɵtext(100, \"Depuis\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"th\", 55);\n          i0.ɵɵtext(102, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(103, \"tbody\");\n          i0.ɵɵtemplate(104, FournisseurComponent_tr_104_Template, 25, 5, \"tr\", 56);\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵelement(105, \"div\", 48);\n          i0.ɵɵelementStart(106, \"div\", 57)(107, \"p\", 58);\n          i0.ɵɵtext(108, \"Design and Developed by \");\n          i0.ɵɵelementStart(109, \"a\", 59);\n          i0.ɵɵtext(110, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(111, \" Distributed by \");\n          i0.ɵɵelementStart(112, \"a\", 60);\n          i0.ɵɵtext(113, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n          i0.ɵɵelementStart(114, \"p\");\n          i0.ɵɵtext(115, \"fournisseur works!\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(45);\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_6_0;\n          let tmp_7_0;\n          i0.ɵɵadvance(44);\n          i0.ɵɵproperty(\"ngModel\", ctx.fournisseur1.nomFournisseur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.isModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.fournisseurForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.fournisseurForm.get(\"nomFournisseur\")) == null ? null : tmp_4_0.invalid) && (((tmp_4_0 = ctx.fournisseurForm.get(\"nomFournisseur\")) == null ? null : tmp_4_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.fournisseurForm.get(\"emailFournisseur\")) == null ? null : tmp_5_0.invalid) && (((tmp_5_0 = ctx.fournisseurForm.get(\"emailFournisseur\")) == null ? null : tmp_5_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.fournisseurForm.get(\"telephoneFournisseur\")) == null ? null : tmp_6_0.invalid) && (((tmp_6_0 = ctx.fournisseurForm.get(\"telephoneFournisseur\")) == null ? null : tmp_6_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.fournisseurForm.get(\"adresseFournisseur\")) == null ? null : tmp_7_0.invalid) && (((tmp_7_0 = ctx.fournisseurForm.get(\"adresseFournisseur\")) == null ? null : tmp_7_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(23);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.NgModel, i3.NgForm, i3.FormGroupDirective, i3.FormControlName, i5.LayoutComponent],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #0051ff; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #d1d5db;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  font-family: 'Inter', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  background-color: #f6f7fb;\\n  padding: 30px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-left: -30px;\\n  width: 1300px; \\n\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  width: 570px; \\n\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n}\\n\\n.icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background-color: #e7f8ed;\\n  padding: 6px;\\n  border-radius: 8px;\\n}\\n\\n.info[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #777;\\n}\\n\\n.status[_ngcontent-%COMP%] {\\n  background-color: #e0f7e9;\\n  color: #28a745;\\n  font-size: 12px;\\n  padding: 3px 10px;\\n  border-radius: 12px;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.details[_ngcontent-%COMP%] {\\n  margin-top: -12px; \\n\\n}\\n\\n.details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-top: 13px; \\n\\n  color: #666666;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 20px;\\n  margin-top: 15px;\\n}\\n\\n.view[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n    width: 500px; \\n\\n  height: 37px;\\n  padding: 10px 24px;\\n  border: 1px solid #ccc;\\n  border-radius: 10px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  background-color: #fff;\\n  color: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n\\n}\\n\\n.edit[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n    width: 500px; \\n\\n  height: 37px;\\n  padding: 10px 24px;\\n  border: 1px solid #ccc;\\n  border-radius: 10px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  background-color: #fff;\\n  color: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n  \\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n\\n\\n.varient-table[_ngcontent-%COMP%] {\\n  border-collapse: separate;\\n  border-spacing: 0;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  padding: 1rem 0.5rem;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #eee;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.5rem;\\n  vertical-align: middle;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.75rem;\\n  border-radius: 0.375rem;\\n}\\n\\n.text-truncate[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border: 2px solid #e9ecef;\\n}\\n\\n\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   img[src*=\\\"mail.JPG\\\"][_ngcontent-%COMP%], .varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   img[src*=\\\"phone.JPG\\\"][_ngcontent-%COMP%], .varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   img[src*=\\\"map.JPG\\\"][_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  border: 1px solid #dee2e6;\\n}\\n\\n\\n\\n.container-fluid[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  padding: 0;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.table-large[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n}\\n\\n.table-large[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1rem;\\n  font-size: 1rem;\\n  font-weight: 700;\\n}\\n\\n.table-large[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1.5rem 1rem;\\n  font-size: 1rem;\\n}\\n\\n.table-large[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  margin-bottom: 0.25rem;\\n}\\n\\n\\n\\n.btn-action[_ngcontent-%COMP%] {\\n  padding: 0.5rem 1rem;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n  border-radius: 0.5rem;\\n  min-width: 80px;\\n}\\n\\n.btn-action[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\\n}\\n\\n\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  height: 80px;\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .table-large[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .table-large[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .table-large[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n    padding: 1rem 0.75rem;\\n  }\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "FournisseurComponent_div_46_div_1_Template", "FournisseurComponent_div_46_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "ctx_r7", "notification", "type", "ɵɵtextInterpolate1", "message", "ɵɵlistener", "FournisseurComponent_tr_104_Template_button_click_21_listener", "restoredCtx", "ɵɵrestoreView", "_r13", "fournisseur_r11", "$implicit", "ctx_r12", "ɵɵnextContext", "ɵɵresetView", "updateFournisseur", "FournisseurComponent_tr_104_Template_button_click_23_listener", "ctx_r14", "deleteFournisseur", "idFournisseur", "ɵɵtextInterpolate", "nomFournisseur", "emailFournisseur", "telephoneFournisseur", "<PERSON>ress<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FournisseurComponent", "constructor", "http", "authservice", "fb", "fournisseurs", "isModalOpen", "submitted", "fournisseur1", "equipements", "show", "ngOnInit", "fournisseurForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "GetAllFournisseur", "closeOnOutsideClick", "event", "target", "classList", "contains", "closeModal", "openModal", "resetForm", "showNotification", "setTimeout", "hideNotification", "onRegister", "invalid", "mark<PERSON>llAsTouched", "addFournisseur", "value", "subscribe", "next", "response", "console", "log", "push", "reset", "window", "scrollTo", "top", "behavior", "error", "alert", "getallFournisseur", "data", "JSON", "stringify", "id", "confirm", "filter", "f", "<PERSON><PERSON><PERSON><PERSON>", "patchValue", "onUpdateClick", "form", "valid", "updatedFournisseur", "index", "findIndex", "onSubmit", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "TypeService", "i3", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "FournisseurComponent_Template", "rf", "ctx", "ɵɵelement", "FournisseurComponent_Template_button_click_24_listener", "FournisseurComponent_Template_input_ngModelChange_44_listener", "$event", "FournisseurComponent_div_46_Template", "FournisseurComponent_Template_button_click_50_listener", "_r15", "_r0", "ɵɵreference", "FournisseurComponent_Template_div_click_53_listener", "FournisseurComponent_Template_div_click_54_listener", "stopPropagation", "FournisseurComponent_Template_span_click_55_listener", "FournisseurComponent_Template_form_ngSubmit_59_listener", "FournisseurComponent_div_65_Template", "FournisseurComponent_div_69_Template", "FournisseurComponent_div_73_Template", "FournisseurComponent_div_77_Template", "FournisseurComponent_div_81_Template", "FournisseurComponent_tr_104_Template", "touched", "ɵɵpureFunction1", "_c0", "tmp_4_0", "get", "tmp_5_0", "tmp_6_0", "tmp_7_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\fournisseur\\fournisseur.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\fournisseur\\fournisseur.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Component, OnInit } from '@angular/core';\r\nimport { TypeService } from '../dashboard/type.service';\r\nimport { Fournisseur } from './Fournisseur';\r\nimport { Observable } from 'rxjs';\r\nimport { FormBuilder, FormGroup,Validators } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-fournisseur',\r\n  templateUrl: './fournisseur.component.html',\r\n  styleUrls: ['./fournisseur.component.css']\r\n})\r\nexport class FournisseurComponent implements OnInit {\r\n\r\n\r\n\r\nconstructor(private http: HttpClient,private authservice:TypeService,  private fb: FormBuilder) { }\r\n\r\nfournisseurs:Fournisseur[]=[];\r\nisModalOpen = false;\r\nfournisseurForm!:FormGroup\r\nsubmitted = false;\r\nfournisseur1:Fournisseur={\r\n  idFournisseur: 0,\r\n  nomFournisseur: '',\r\n  adresseFournisseur: '',\r\n  emailFournisseur: '',\r\n  telephoneFournisseur: '',\r\n  equipements:[]\r\n}\r\n\r\n// Notification system\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\nngOnInit(): void {\r\n  \r\nthis.fournisseurForm = this.fb.group({\r\n  nomFournisseur: ['', [Validators.required, Validators.minLength(3)]],\r\n  adresseFournisseur: ['', [Validators.required, Validators.minLength(4)]],\r\n  emailFournisseur: ['', [Validators.required, Validators.email]],\r\n  telephoneFournisseur: ['', [Validators.required, Validators.minLength(8)]],\r\n});\r\n\r\nthis.GetAllFournisseur();\r\n\r\n\r\n\r\n\r\n}\r\n  closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\n  openModal() {\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n    this.resetForm();\r\n  }\r\n\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n\r\n\r\n\r\n  onRegister(): void {\r\n  this.submitted = true;\r\n  \r\n  if (this.fournisseurForm.invalid) {\r\n    this.fournisseurForm.markAllAsTouched(); // pour afficher les erreurs\r\n    return; // empêche la soumission si formulaire invalide\r\n  }\r\n\r\n \r\n  this.authservice.addFournisseur(this.fournisseurForm.value).subscribe({\r\n    next: (response) => {\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Fournisseur ajouté avec succès !');\r\n      this.fournisseurs.push(response);\r\n      this.closeModal();\r\n      this.fournisseurForm.reset();\r\n      this.submitted = false;\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      alert('Échec de l’enregistrement');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n\r\nGetAllFournisseur() {\r\n\r\n\r\n  return this.authservice.getallFournisseur().subscribe(data => {\r\n    this.fournisseurs = data;\r\n    console.log(\"Types reçus : \", JSON.stringify(this.fournisseurs, null, 2));\r\n\r\n  });\r\n\r\n}\r\n\r\n// Méthode pour supprimer un fournisseur\r\ndeleteFournisseur(id: number) {\r\n  if (confirm('Êtes-vous sûr de vouloir supprimer ce fournisseur ?')) {\r\n    // Supposons que le service a une méthode deleteFournisseur\r\n    // Si elle n'existe pas, vous devrez l'ajouter au service\r\n    this.authservice.deleteFournisseur(id).subscribe({\r\n      next: () => {\r\n        this.showNotification('success', 'Fournisseur supprimé avec succès !');\r\n        this.fournisseurs = this.fournisseurs.filter(f => f.idFournisseur !== id);\r\n      },\r\n      error: (error: any) => {\r\n        console.error('Delete failed:', error);\r\n        this.showNotification('error', 'Échec de la suppression du fournisseur.');\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n\r\nupdateFournisseur(fournisseur: Fournisseur) {\r\n\r\n  this.fournisseur1 = { ...fournisseur };\r\n\r\n  this.fournisseurForm.patchValue({\r\n    nomFournisseur: fournisseur.nomFournisseur,\r\n    adresseFournisseur: fournisseur.adresseFournisseur,\r\n    emailFournisseur: fournisseur.emailFournisseur,\r\n    telephoneFournisseur: fournisseur.telephoneFournisseur\r\n  });\r\n\r\n  this.openModal();\r\n}\r\n\r\n// Method for handling template-driven form update\r\nonUpdateClick(form: any) {\r\n  if (form.valid) {\r\n    const updatedFournisseur = {\r\n      ...this.fournisseur1\r\n    };\r\n\r\n    this.authservice.updateFournisseur(updatedFournisseur).subscribe({\r\n      next: (response: any) => {\r\n        console.log('Fournisseur updated successfully', response);\r\n        this.showNotification('success', 'Fournisseur modifié avec succès !');\r\n\r\n\r\n        const index = this.fournisseurs.findIndex(f => f.idFournisseur === this.fournisseur1.idFournisseur);\r\n        if (index !== -1) {\r\n          this.fournisseurs[index] = response;\r\n        }\r\n\r\n        // Close the update modal (you might need to add a separate flag for this)\r\n        this.closeModal();\r\n        window.scrollTo({ top: 0, behavior: 'smooth' });\r\n      },\r\n      error: (error) => {\r\n        console.error('Update failed:', error);\r\n        this.showNotification('error', 'Échec de la modification du fournisseur.');\r\n      }\r\n    });\r\n  } else {\r\n    console.log('Form is invalid');\r\n  }\r\n}\r\n\r\n// Method to handle form submission for both add and update\r\nonSubmit(): void {\r\n  this.submitted = true;\r\n\r\n  if (this.fournisseurForm.invalid) {\r\n    this.fournisseurForm.markAllAsTouched();\r\n    return;\r\n  }\r\n\r\n  if (this.fournisseur1.idFournisseur === 0) {\r\n    // Add new fournisseur\r\n    this.onRegister();\r\n  } else {\r\n    // Update existing fournisseur\r\n    const updatedFournisseur = {\r\n      ...this.fournisseur1,\r\n      ...this.fournisseurForm.value\r\n    };\r\n\r\n    this.authservice.updateFournisseur(updatedFournisseur).subscribe({\r\n      next: (response: any) => {\r\n        console.log('Fournisseur updated successfully', response);\r\n        this.showNotification('success', 'Fournisseur modifié avec succès !');\r\n\r\n        // Update the fournisseur in the local array\r\n        const index = this.fournisseurs.findIndex(f => f.idFournisseur === this.fournisseur1.idFournisseur);\r\n        if (index !== -1) {\r\n          this.fournisseurs[index] = response;\r\n        }\r\n\r\n        this.closeModal();\r\n        this.resetForm();\r\n        window.scrollTo({ top: 0, behavior: 'smooth' });\r\n      },\r\n      error: (error) => {\r\n        console.error('Update failed:', error);\r\n        this.showNotification('error', 'Échec de la modification du fournisseur.');\r\n      }\r\n    });\r\n  }\r\n}\r\n\r\n// Reset form and fournisseur1 object\r\nresetForm(): void {\r\n  this.fournisseurForm.reset();\r\n  this.submitted = false;\r\n  this.fournisseur1 = {\r\n    idFournisseur: 0,\r\n    nomFournisseur: '',\r\n    adresseFournisseur: '',\r\n    emailFournisseur: '',\r\n    telephoneFournisseur: '',\r\n    equipements: []\r\n  };\r\n}\r\n\r\n}", "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\r\n  \r\n</head>\r\n\r\n<body>\r\n  <!--  Body Wrapper -->\r\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\r\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\r\n\r\n    <!--  App Topstrip -->\r\n    \r\n    <!-- Sidebar Start -->\r\n <app-layout> </app-layout>\r\n\r\n    <!--  Sidebar End -->\r\n    <!--  Main wrapper -->\r\n    <div class=\"body-wrapper\">\r\n      <!--  Header Start -->\r\n      <header class=\"app-header\">\r\n\r\n      </header>\r\n      <!--  Header End -->\r\n      <div class=\"body-wrapper-inner\">\r\n        <div class=\"container-fluid\">\r\n                <div class=\"welcome-header\">\r\n  <h1>Bienvenue dans votre espace</h1>\r\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\r\n\r\n</div>\r\n<!-- Bouton pour ouvrir la modale -->\r\n\r\n<div class=\"header-container\">\r\n  <div class=\"header-text\">\r\n    <h2>Fournisseur</h2>\r\n    <p>Gérez les différents fournisseurs\r\n\r\n</p>\r\n  </div>\r\n<button class=\"add-user-btn\" style=\"width: 200px;\" (click)=\"openModal()\">\r\n  <span class=\"icon\">+</span>Nouveau fournisseur\r\n\r\n</button>\r\n</div>\r\n<div class=\"search-wrapper\">\r\n  <div class=\"custom-search\">\r\n    <input type=\"text\" placeholder=\"Rechercher un type...\" />\r\n    <span class=\"icon-search\"></span>\r\n  </div>\r\n</div>\r\n<!-- Modal -->\r\n\r\n\r\n\r\n\r\n<div class=\"modal fade\" id=\"updateModal\" tabindex=\"-1\" aria-labelledby=\"updateModalLabel\" aria-hidden=\"true\" data-bs-backdrop=\"false\">\r\n  <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n    <div class=\"modal-content shadow rounded-4\">\r\n      \r\n      <h5 id=\"updateModalLabel\">📝 Modifier les informations</h5>\r\n      <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n\r\n      <div class=\"modal-body\">\r\n        <form #updateForm=\"ngForm\">\r\n          <!-- Nom du type -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"nomFournisseur\" class=\"form-label fw-semibold fs-5\">Nom du type</label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              id=\"nomFournisseur\"\r\n              name=\"nomFournisseur\"\r\n              [(ngModel)]=\"fournisseur1.nomFournisseur\"\r\n              #nomMarque=\"ngModel\"\r\n              required\r\n              minlength=\"3\"\r\n            />\r\n            <div *ngIf=\"nomMarque.invalid && nomMarque.touched\" style=\"color:red\">\r\n              <div *ngIf=\"nomMarque.errors?.['required']\">Le nom de Marque est requis</div>\r\n              <div *ngIf=\"nomMarque.errors?.['minlength']\">Le nom de marque doit contenir au moins 2 caractères</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Types disponibles -->\r\n          \r\n        </form>\r\n      </div>\r\n\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">\r\n          Annuler\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-success px-4\" (click)=\"onUpdateClick(updateForm)\">\r\n          💾 Sauvegarder\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n<body class=\"bg-light\">\r\n\r\n\r\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\" >Ajouter un nouveau fournisseur</h3>\r\n<form [formGroup]=\"fournisseurForm\" (ngSubmit)=\"onRegister()\" novalidate>\r\n  <br><br>\r\n\r\n  <!-- 🧩 Model -->\r\n\r\n\r\n\r\n\r\n\r\n  <label for=\"numSerie\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Nom Fournisseur</label>\r\n  <input\r\n    class=\"form-inputp\"\r\n    id=\"nomFournisseur\"\r\n    type=\"text\"\r\n    formControlName=\"nomFournisseur\"\r\n    placeholder=\"Nom du Fournisseur\"\r\n  />\r\n  <div *ngIf=\"fournisseurForm.get('nomFournisseur')?.invalid && (fournisseurForm.get('nomFournisseur')?.touched || submitted)\" style=\"color:red\">\r\n     Nom de Fournisseur est requis (min 3 caractères)\r\n  </div>\r\n     <label for=\"emailFournisseur\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Email</label>\r\n  <input\r\n    class=\"form-inputp\"\r\n    id=\"emailFournisseur\"\r\n    type=\"text\"\r\n    formControlName=\"emailFournisseur\"\r\n    placeholder=\"<EMAIL>\"\r\n  />\r\n  <div *ngIf=\"fournisseurForm.get('emailFournisseur')?.invalid && (fournisseurForm.get('emailFournisseur')?.touched || submitted)\" style=\"color:red\">\r\nEmail est requis \r\n  </div>\r\n\r\n\r\n\r\n     <label for=\"telephoneFournisseur\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Telephone</label>\r\n  <input\r\n    class=\"form-inputp\"\r\n    id=\"telephoneFournisseur\"\r\n    type=\"text\"\r\n    formControlName=\"telephoneFournisseur\"\r\n    placeholder=\"012345678\"\r\n  />\r\n  <div *ngIf=\"fournisseurForm.get('telephoneFournisseur')?.invalid && (fournisseurForm.get('telephoneFournisseur')?.touched || submitted)\" style=\"color:red\">\r\n    Le numéro de téléphone est requis (min 4 caractères)\r\n  </div>\r\n  \r\n  <label for=\"adresseFournisseur\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Adresse</label>\r\n  <textarea\r\n    class=\"form-inputp\"\r\n    id=\"adresseFournisseur\"\r\n    type=\"text\"\r\n    formControlName=\"adresseFournisseur\"\r\n    placeholder=\"Adresse\"\r\n  ></textarea>\r\n  <div *ngIf=\"fournisseurForm.get('adresseFournisseur')?.invalid && (fournisseurForm.get('adresseFournisseur')?.touched || submitted)\" style=\"color:red\">\r\n    Adesse est requis (min 4 caractères)\r\n  </div>\r\n\r\n  \r\n\r\n  <br />\r\n  <button type=\"submit\"  class=\"btn btn-primary\">\r\n    Enregistrer\r\n  </button>\r\n</form>\r\n\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n     <!-- Simple Notification Bar -->\r\n     <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n       {{ notification.message }}\r\n     </div>\r\n\r\n     <!-- Tableau des fournisseurs -->\r\n     <div class=\"container-fluid\">\r\n       <div class=\"row\">\r\n         <div class=\"col-12\">\r\n           <div class=\"card\">\r\n             <div class=\"card-body\">\r\n               <div class=\"table-responsive mt-4\">\r\n                 <table class=\"table mb-0 text-nowrap varient-table align-middle fs-2 table-large\">\r\n                   <thead>\r\n                     <tr>\r\n                       <th scope=\"col\" class=\"px-3 text-muted\">Nom du Fournisseur</th>\r\n                       <th scope=\"col\" class=\"px-3 text-muted\">Email</th>\r\n                       <th scope=\"col\" class=\"px-3 text-muted\">Téléphone</th>\r\n                       <th scope=\"col\" class=\"px-3 text-muted\">Adresse</th>\r\n                       <th scope=\"col\" class=\"px-3 text-muted\">Depuis</th>\r\n                       <th scope=\"col\" class=\"px-3 text-muted text-end\">Actions</th>\r\n                     </tr>\r\n                   </thead>\r\n                   <tbody>\r\n                     <tr *ngFor=\"let fournisseur of fournisseurs\">\r\n                       <!-- Nom du fournisseur -->\r\n                       <td class=\"px-3\">\r\n                         <div>\r\n                           <h5 class=\"fw-bold mb-1 fs-3\">{{ fournisseur.nomFournisseur }}</h5>\r\n                           <span class=\"fw-normal text-muted fs-4\">Fournisseur</span>\r\n                         </div>\r\n                       </td>\r\n\r\n                       <!-- Email -->\r\n                       <td class=\"px-3\">\r\n                         <span class=\"fw-normal fs-4\">{{ fournisseur.emailFournisseur }}</span>\r\n                       </td>\r\n\r\n                       <!-- Téléphone -->\r\n                       <td class=\"px-3\">\r\n                         <span class=\"fw-normal fs-4\">{{ fournisseur.telephoneFournisseur }}</span>\r\n                       </td>\r\n\r\n                       <!-- Adresse -->\r\n                       <td class=\"px-3\">\r\n                         <span class=\"fw-normal text-truncate fs-4\"\r\n                               style=\"max-width: 300px; display: inline-block;\"\r\n                               [title]=\"fournisseur.adresseFournisseur\">\r\n                           {{ fournisseur.adresseFournisseur }}\r\n                         </span>\r\n                       </td>\r\n\r\n                       <!-- Date depuis -->\r\n                       <td class=\"px-3\">\r\n                         <span class=\"fw-normal text-muted fs-4\">12/01/2024</span>\r\n                       </td>\r\n\r\n                       <!-- Actions -->\r\n                       <td class=\"px-3 text-end\">\r\n                         <div class=\"d-flex justify-content-end gap-2\">\r\n                           <button\r\n                             class=\"btn btn-outline-primary btn-action\"\r\n                             (click)=\"updateFournisseur(fournisseur)\"\r\n                             title=\"Modifier\">\r\n                             Modifier\r\n                           </button>\r\n                           <button\r\n                             class=\"btn btn-outline-danger btn-action\"\r\n                             (click)=\"deleteFournisseur(fournisseur.idFournisseur)\"\r\n                             title=\"Supprimer\">\r\n                             Supprimer\r\n                           </button>\r\n                         </div>\r\n                       </td>\r\n                     </tr>\r\n                   </tbody>\r\n                 </table>\r\n               </div>\r\n             </div>\r\n           </div>\r\n         </div>\r\n       </div>\r\n     </div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n</body>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<!-- MODAL -->\r\n\r\n\r\n\r\n\r\n          <!--  Row 1 -->\r\n          <div class=\"row\">\r\n           \r\n\r\n\r\n\r\n\r\n       \r\n          </div>\r\n          <div class=\"py-6 px-6 text-center\">\r\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\r\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\r\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\r\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\r\n  <script src=\"./assets/js/app.min.js\"></script>\r\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\r\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\r\n  <script src=\"./assets/js/dashboard.js\"></script>\r\n  <!-- solar icons -->\r\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\r\n</body>\r\n\r\n</html><p>fournisseur works!</p>\r\n"], "mappings": "AAKA,SAAgCA,UAAU,QAAQ,gBAAgB;;;;;;;;;IC8EpDC,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC7EH,EAAA,CAAAC,cAAA,UAA6C;IAAAD,EAAA,CAAAE,MAAA,gEAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,UAAA,IAAAC,0CAAA,kBAA6E;IAC7EL,EAAA,CAAAI,UAAA,IAAAE,0CAAA,kBAAuG;IACzGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,cAAqC;;;;;IAgDvDV,EAAA,CAAAC,cAAA,cAA+I;IAC5ID,EAAA,CAAAE,MAAA,8DACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IASNH,EAAA,CAAAC,cAAA,cAAmJ;IACrJD,EAAA,CAAAE,MAAA,0BACE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYNH,EAAA,CAAAC,cAAA,cAA2J;IACzJD,EAAA,CAAAE,MAAA,iFACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAUNH,EAAA,CAAAC,cAAA,cAAuJ;IACrJD,EAAA,CAAAE,MAAA,kDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAqBHH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAQ,UAAA,YAAAG,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFb,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAH,MAAA,CAAAC,YAAA,CAAAG,OAAA,MACF;;;;;;IAqBgBf,EAAA,CAAAC,cAAA,SAA6C;IAITD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnEH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAK9DH,EAAA,CAAAC,cAAA,aAAiB;IACcD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIxEH,EAAA,CAAAC,cAAA,cAAiB;IACcD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI5EH,EAAA,CAAAC,cAAA,cAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAiB;IACyBD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI3DH,EAAA,CAAAC,cAAA,cAA0B;IAIpBD,EAAA,CAAAgB,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,WAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,iBAAA,CAAAL,eAAA,CAA8B;IAAA,EAAC;IAExCrB,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGoB;IADlBD,EAAA,CAAAgB,UAAA,mBAAAW,8DAAA;MAAA,MAAAT,WAAA,GAAAlB,EAAA,CAAAmB,aAAA,CAAAC,IAAA;MAAA,MAAAC,eAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,OAAA,GAAA5B,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAG,OAAA,CAAAC,iBAAA,CAAAR,eAAA,CAAAS,aAAA,CAA4C;IAAA,EAAC;IAEtD9B,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA3CqBH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA+B,iBAAA,CAAAV,eAAA,CAAAW,cAAA,CAAgC;IAOnChC,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAA+B,iBAAA,CAAAV,eAAA,CAAAY,gBAAA,CAAkC;IAKlCjC,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAA+B,iBAAA,CAAAV,eAAA,CAAAa,oBAAA,CAAsC;IAO7DlC,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,UAAAa,eAAA,CAAAc,kBAAA,CAAwC;IAC5CnC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAc,kBAAA,MAAAO,eAAA,CAAAc,kBAAA,MACF;;;;;;;;ADpOzB,OAAM,MAAOC,oBAAoB;EAIjCC,YAAoBC,IAAgB,EAASC,WAAuB,EAAWC,EAAe;IAA1E,KAAAF,IAAI,GAAJA,IAAI;IAAqB,KAAAC,WAAW,GAAXA,WAAW;IAAuB,KAAAC,EAAE,GAAFA,EAAE;IAEjF,KAAAC,YAAY,GAAe,EAAE;IAC7B,KAAAC,WAAW,GAAG,KAAK;IAEnB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAa;MACvBd,aAAa,EAAE,CAAC;MAChBE,cAAc,EAAE,EAAE;MAClBG,kBAAkB,EAAE,EAAE;MACtBF,gBAAgB,EAAE,EAAE;MACpBC,oBAAoB,EAAE,EAAE;MACxBW,WAAW,EAAC;KACb;IAED;IACA,KAAAjC,YAAY,GAAG;MACbkC,IAAI,EAAE,KAAK;MACXjC,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;EApBiG;EAqBlGgC,QAAQA,CAAA;IAER,IAAI,CAACC,eAAe,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MACnCjB,cAAc,EAAE,CAAC,EAAE,EAAE,CAACjC,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACoD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACpEhB,kBAAkB,EAAE,CAAC,EAAE,EAAE,CAACpC,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACoD,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACxElB,gBAAgB,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACqD,KAAK,CAAC,CAAC;MAC/DlB,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACmD,QAAQ,EAAEnD,UAAU,CAACoD,SAAS,CAAC,CAAC,CAAC,CAAC;KAC1E,CAAC;IAEF,IAAI,CAACE,iBAAiB,EAAE;EAKxB;EACEC,mBAAmBA,CAACC,KAAiB;IACnC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACC,UAAU,EAAE;;EAErB;EAEAC,SAASA,CAAA;IACP,IAAI,CAAClB,WAAW,GAAG,IAAI;EACzB;EAEAiB,UAAUA,CAAA;IACR,IAAI,CAACjB,WAAW,GAAG,KAAK;IACxB,IAAI,CAACmB,SAAS,EAAE;EAClB;EAEAC,gBAAgBA,CAACjD,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBkC,IAAI,EAAE,IAAI;MACVjC,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAEDgD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACpD,YAAY,CAACkC,IAAI,GAAG,KAAK;EAChC;EAIAmB,UAAUA,CAAA;IACV,IAAI,CAACtB,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACK,eAAe,CAACkB,OAAO,EAAE;MAChC,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,EAAE,CAAC,CAAC;MACzC,OAAO,CAAC;;;IAIV,IAAI,CAAC5B,WAAW,CAAC6B,cAAc,CAAC,IAAI,CAACpB,eAAe,CAACqB,KAAK,CAAC,CAACC,SAAS,CAAC;MACpEC,IAAI,EAAGC,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;QACrD,IAAI,CAACV,gBAAgB,CAAC,SAAS,EAAE,kCAAkC,CAAC;QACpE,IAAI,CAACrB,YAAY,CAACkC,IAAI,CAACH,QAAQ,CAAC;QAChC,IAAI,CAACb,UAAU,EAAE;QACjB,IAAI,CAACX,eAAe,CAAC4B,KAAK,EAAE;QAC5B,IAAI,CAACjC,SAAS,GAAG,KAAK;QACtBkC,MAAM,CAACC,QAAQ,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;MACjD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfR,OAAO,CAACQ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CC,KAAK,CAAC,2BAA2B,CAAC;MACpC;KACD,CAAC;EACJ;EAIA7B,iBAAiBA,CAAA;IAGf,OAAO,IAAI,CAACd,WAAW,CAAC4C,iBAAiB,EAAE,CAACb,SAAS,CAACc,IAAI,IAAG;MAC3D,IAAI,CAAC3C,YAAY,GAAG2C,IAAI;MACxBX,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEW,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7C,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAE3E,CAAC,CAAC;EAEJ;EAEA;EACAZ,iBAAiBA,CAAC0D,EAAU;IAC1B,IAAIC,OAAO,CAAC,qDAAqD,CAAC,EAAE;MAClE;MACA;MACA,IAAI,CAACjD,WAAW,CAACV,iBAAiB,CAAC0D,EAAE,CAAC,CAACjB,SAAS,CAAC;QAC/CC,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAACT,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;UACtE,IAAI,CAACrB,YAAY,GAAG,IAAI,CAACA,YAAY,CAACgD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC5D,aAAa,KAAKyD,EAAE,CAAC;QAC3E,CAAC;QACDN,KAAK,EAAGA,KAAU,IAAI;UACpBR,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtC,IAAI,CAACnB,gBAAgB,CAAC,OAAO,EAAE,yCAAyC,CAAC;QAC3E;OACD,CAAC;;EAEN;EAGApC,iBAAiBA,CAACiE,WAAwB;IAExC,IAAI,CAAC/C,YAAY,GAAG;MAAE,GAAG+C;IAAW,CAAE;IAEtC,IAAI,CAAC3C,eAAe,CAAC4C,UAAU,CAAC;MAC9B5D,cAAc,EAAE2D,WAAW,CAAC3D,cAAc;MAC1CG,kBAAkB,EAAEwD,WAAW,CAACxD,kBAAkB;MAClDF,gBAAgB,EAAE0D,WAAW,CAAC1D,gBAAgB;MAC9CC,oBAAoB,EAAEyD,WAAW,CAACzD;KACnC,CAAC;IAEF,IAAI,CAAC0B,SAAS,EAAE;EAClB;EAEA;EACAiC,aAAaA,CAACC,IAAS;IACrB,IAAIA,IAAI,CAACC,KAAK,EAAE;MACd,MAAMC,kBAAkB,GAAG;QACzB,GAAG,IAAI,CAACpD;OACT;MAED,IAAI,CAACL,WAAW,CAACb,iBAAiB,CAACsE,kBAAkB,CAAC,CAAC1B,SAAS,CAAC;QAC/DC,IAAI,EAAGC,QAAa,IAAI;UACtBC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEF,QAAQ,CAAC;UACzD,IAAI,CAACV,gBAAgB,CAAC,SAAS,EAAE,mCAAmC,CAAC;UAGrE,MAAMmC,KAAK,GAAG,IAAI,CAACxD,YAAY,CAACyD,SAAS,CAACR,CAAC,IAAIA,CAAC,CAAC5D,aAAa,KAAK,IAAI,CAACc,YAAY,CAACd,aAAa,CAAC;UACnG,IAAImE,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACxD,YAAY,CAACwD,KAAK,CAAC,GAAGzB,QAAQ;;UAGrC;UACA,IAAI,CAACb,UAAU,EAAE;UACjBkB,MAAM,CAACC,QAAQ,CAAC;YAAEC,GAAG,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAQ,CAAE,CAAC;QACjD,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfR,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtC,IAAI,CAACnB,gBAAgB,CAAC,OAAO,EAAE,0CAA0C,CAAC;QAC5E;OACD,CAAC;KACH,MAAM;MACLW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;;EAElC;EAEA;EACAyB,QAAQA,CAAA;IACN,IAAI,CAACxD,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACK,eAAe,CAACkB,OAAO,EAAE;MAChC,IAAI,CAAClB,eAAe,CAACmB,gBAAgB,EAAE;MACvC;;IAGF,IAAI,IAAI,CAACvB,YAAY,CAACd,aAAa,KAAK,CAAC,EAAE;MACzC;MACA,IAAI,CAACmC,UAAU,EAAE;KAClB,MAAM;MACL;MACA,MAAM+B,kBAAkB,GAAG;QACzB,GAAG,IAAI,CAACpD,YAAY;QACpB,GAAG,IAAI,CAACI,eAAe,CAACqB;OACzB;MAED,IAAI,CAAC9B,WAAW,CAACb,iBAAiB,CAACsE,kBAAkB,CAAC,CAAC1B,SAAS,CAAC;QAC/DC,IAAI,EAAGC,QAAa,IAAI;UACtBC,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEF,QAAQ,CAAC;UACzD,IAAI,CAACV,gBAAgB,CAAC,SAAS,EAAE,mCAAmC,CAAC;UAErE;UACA,MAAMmC,KAAK,GAAG,IAAI,CAACxD,YAAY,CAACyD,SAAS,CAACR,CAAC,IAAIA,CAAC,CAAC5D,aAAa,KAAK,IAAI,CAACc,YAAY,CAACd,aAAa,CAAC;UACnG,IAAImE,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAACxD,YAAY,CAACwD,KAAK,CAAC,GAAGzB,QAAQ;;UAGrC,IAAI,CAACb,UAAU,EAAE;UACjB,IAAI,CAACE,SAAS,EAAE;UAChBgB,MAAM,CAACC,QAAQ,CAAC;YAAEC,GAAG,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAQ,CAAE,CAAC;QACjD,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfR,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;UACtC,IAAI,CAACnB,gBAAgB,CAAC,OAAO,EAAE,0CAA0C,CAAC;QAC5E;OACD,CAAC;;EAEN;EAEA;EACAD,SAASA,CAAA;IACP,IAAI,CAACb,eAAe,CAAC4B,KAAK,EAAE;IAC5B,IAAI,CAACjC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,YAAY,GAAG;MAClBd,aAAa,EAAE,CAAC;MAChBE,cAAc,EAAE,EAAE;MAClBG,kBAAkB,EAAE,EAAE;MACtBF,gBAAgB,EAAE,EAAE;MACpBC,oBAAoB,EAAE,EAAE;MACxBW,WAAW,EAAE;KACd;EACH;;;uBAvOaT,oBAAoB,EAAApC,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAApBtE,oBAAoB;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCXjCjH,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAmH,SAAA,cAAsB;UAEtBnH,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQLD,EAAA,CAAAmH,SAAA,iBAA0B;UAIvBnH,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAmH,SAAA,iBAES;UAETnH,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oDAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAAyE;UAAtBD,EAAA,CAAAgB,UAAA,mBAAAoG,uDAAA;YAAA,OAASF,GAAA,CAAAtD,SAAA,EAAW;UAAA,EAAC;UACtE5D,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,4BAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAA4B;UAExBD,EAAA,CAAAmH,SAAA,iBAAyD;UAE3DnH,EAAA,CAAAG,YAAA,EAAM;UAORH,EAAA,CAAAC,cAAA,eAAsI;UAItGD,EAAA,CAAAE,MAAA,8CAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAmH,SAAA,kBAA6G;UAE7GnH,EAAA,CAAAC,cAAA,eAAwB;UAI8CD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnFH,EAAA,CAAAC,cAAA,qBASE;UAJAD,EAAA,CAAAgB,UAAA,2BAAAqG,8DAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAtE,YAAA,CAAAZ,cAAA,GAAAsF,MAAA;UAAA,EAAyC;UAL3CtH,EAAA,CAAAG,YAAA,EASE;UACFH,EAAA,CAAAI,UAAA,KAAAmH,oCAAA,kBAGM;UACRvH,EAAA,CAAAG,YAAA,EAAM;UAOVH,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAuF;UAApCD,EAAA,CAAAgB,UAAA,mBAAAwG,uDAAA;YAAAxH,EAAA,CAAAmB,aAAA,CAAAsG,IAAA;YAAA,MAAAC,GAAA,GAAA1H,EAAA,CAAA2H,WAAA;YAAA,OAAS3H,EAAA,CAAAyB,WAAA,CAAAyF,GAAA,CAAArB,aAAA,CAAA6B,GAAA,CAAyB;UAAA,EAAC;UACpF1H,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UASjBH,EAAA,CAAAC,cAAA,gBAAuB;UAG8BD,EAAA,CAAAgB,UAAA,mBAAA4G,oDAAAN,MAAA;YAAA,OAASJ,GAAA,CAAA5D,mBAAA,CAAAgE,MAAA,CAA2B;UAAA,EAAC;UACxFtH,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAgB,UAAA,mBAAA6G,oDAAAP,MAAA;YAAA,OAASA,MAAA,CAAAQ,eAAA,EAAwB;UAAA,EAAC;UAC3D9H,EAAA,CAAAC,cAAA,gBAA2C;UAAvBD,EAAA,CAAAgB,UAAA,mBAAA+G,qDAAA;YAAA,OAASb,GAAA,CAAAvD,UAAA,EAAY;UAAA,EAAC;UAAC3D,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,cAAoD;UAAAD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3FH,EAAA,CAAAC,cAAA,gBAAyE;UAArCD,EAAA,CAAAgB,UAAA,sBAAAgH,wDAAA;YAAA,OAAYd,GAAA,CAAAjD,UAAA,EAAY;UAAA,EAAC;UAC3DjE,EAAA,CAAAmH,SAAA,UAAI;UAQJnH,EAAA,CAAAC,cAAA,iBAAiF;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxGH,EAAA,CAAAmH,SAAA,iBAME;UACFnH,EAAA,CAAAI,UAAA,KAAA6H,oCAAA,kBAEM;UACHjI,EAAA,CAAAC,cAAA,iBAAyF;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzGH,EAAA,CAAAmH,SAAA,iBAME;UACFnH,EAAA,CAAAI,UAAA,KAAA8H,oCAAA,kBAEM;UAIHlI,EAAA,CAAAC,cAAA,iBAA6F;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjHH,EAAA,CAAAmH,SAAA,iBAME;UACFnH,EAAA,CAAAI,UAAA,KAAA+H,oCAAA,kBAEM;UAENnI,EAAA,CAAAC,cAAA,iBAA2F;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1GH,EAAA,CAAAmH,SAAA,oBAMY;UACZnH,EAAA,CAAAI,UAAA,KAAAgI,oCAAA,kBAEM;UAINpI,EAAA,CAAAmH,SAAA,UAAM;UACNnH,EAAA,CAAAC,cAAA,kBAA+C;UAC7CD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAcNH,EAAA,CAAAI,UAAA,KAAAiI,oCAAA,kBAEM;UAGNrI,EAAA,CAAAC,cAAA,cAA6B;UAS6BD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAiD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAI,UAAA,MAAAkI,oCAAA,kBAkDK;UACPtI,EAAA,CAAAG,YAAA,EAAQ;UA+BjBH,EAAA,CAAAmH,SAAA,gBAOM;UACNnH,EAAA,CAAAC,cAAA,gBAAmC;UACZD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAiB9JH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;UAvPlBH,EAAA,CAAAO,SAAA,IAAyC;UAAzCP,EAAA,CAAAQ,UAAA,YAAA0G,GAAA,CAAAtE,YAAA,CAAAZ,cAAA,CAAyC;UAKrChC,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAyD,OAAA,IAAAzD,GAAA,CAAA8H,OAAA,CAA4C;UA6B3CvI,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAwI,eAAA,KAAAC,GAAA,EAAAvB,GAAA,CAAAxE,WAAA,EAAiC;UAI9C1C,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAQ,UAAA,cAAA0G,GAAA,CAAAlE,eAAA,CAA6B;UAiB3BhD,EAAA,CAAAO,SAAA,GAAqH;UAArHP,EAAA,CAAAQ,UAAA,WAAAkI,OAAA,GAAAxB,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,qCAAAD,OAAA,CAAAxE,OAAA,QAAAwE,OAAA,GAAAxB,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,qCAAAD,OAAA,CAAAH,OAAA,KAAArB,GAAA,CAAAvE,SAAA,EAAqH;UAWrH3C,EAAA,CAAAO,SAAA,GAAyH;UAAzHP,EAAA,CAAAQ,UAAA,WAAAoI,OAAA,GAAA1B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,uCAAAC,OAAA,CAAA1E,OAAA,QAAA0E,OAAA,GAAA1B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,uCAAAC,OAAA,CAAAL,OAAA,KAAArB,GAAA,CAAAvE,SAAA,EAAyH;UAczH3C,EAAA,CAAAO,SAAA,GAAiI;UAAjIP,EAAA,CAAAQ,UAAA,WAAAqI,OAAA,GAAA3B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,2CAAAE,OAAA,CAAA3E,OAAA,QAAA2E,OAAA,GAAA3B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,2CAAAE,OAAA,CAAAN,OAAA,KAAArB,GAAA,CAAAvE,SAAA,EAAiI;UAYjI3C,EAAA,CAAAO,SAAA,GAA6H;UAA7HP,EAAA,CAAAQ,UAAA,WAAAsI,OAAA,GAAA5B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,yCAAAG,OAAA,CAAA5E,OAAA,QAAA4E,OAAA,GAAA5B,GAAA,CAAAlE,eAAA,CAAA2F,GAAA,yCAAAG,OAAA,CAAAP,OAAA,KAAArB,GAAA,CAAAvE,SAAA,EAA6H;UAuB1H3C,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAQ,UAAA,SAAA0G,GAAA,CAAAtG,YAAA,CAAAkC,IAAA,CAAuB;UAuBe9C,EAAA,CAAAO,SAAA,IAAe;UAAfP,EAAA,CAAAQ,UAAA,YAAA0G,GAAA,CAAAzE,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
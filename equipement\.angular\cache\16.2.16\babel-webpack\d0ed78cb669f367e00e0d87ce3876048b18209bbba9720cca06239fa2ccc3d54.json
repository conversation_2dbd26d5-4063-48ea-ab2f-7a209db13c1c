{"ast": null, "code": "import { Equip } from './equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from '../utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Historique } from './Historique';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../Shared/layout/layout.component\";\nfunction EquipementComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r30 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r30);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r30.firstName, \" \", user_r30.lastName, \" - \", user_r30.email, \" \");\n  }\n}\nfunction EquipementComponent_mat_option_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r31 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r31);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", model_r31.nomModel, \" \");\n  }\n}\nfunction EquipementComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le mod\\u00E8le est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de s\\u00E9rie est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" La date est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r32.nomFournisseur, \" \");\n  }\n}\nfunction EquipementComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Au moins un fournisseur est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"img\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r9.imagePreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction EquipementComponent_mat_option_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", model_r33.nomModel, \" \");\n  }\n}\nfunction EquipementComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le mod\\u00E8le est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de s\\u00E9rie est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" La date est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_option_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r34 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r34.nomFournisseur, \" \");\n  }\n}\nfunction EquipementComponent_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Au moins un fournisseur est requis\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"img\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r17.imagePreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction EquipementComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"h5\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.selectedEquipement.model == null ? null : ctx_r18.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r18.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementComponent_mat_option_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r35.firstName, \" \", user_r35.lastName, \" - \", user_r35.email, \" \");\n  }\n}\nfunction EquipementComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \" L'utilisateur est requis\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"h5\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r22.selectedEquipement.model == null ? null : ctx_r22.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r22.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementComponent_mat_option_184_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r36 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r36);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r36.firstName, \" \", user_r36.lastName, \" - \", user_r36.email, \" \");\n  }\n}\nfunction EquipementComponent_div_185_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \" L'utilisateur est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_div_198_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r27.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.notification.message, \" \");\n  }\n}\nfunction EquipementComponent_tr_225_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r37 = i0.ɵɵnextContext().$implicit;\n    const ctx_r38 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r38.NameUtilisateur[equip_r37.idEqui]), \" \");\n  }\n}\nfunction EquipementComponent_tr_225_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_tr_225_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.openAffectationModal(equip_r37));\n    });\n    i0.ɵɵtext(1, \" Affecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_tr_225_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_tr_225_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.openEditedModal(equip_r37));\n    });\n    i0.ɵɵtext(1, \" \\uD83D\\uDD04 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_tr_225_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_tr_225_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.desaffecterEquipement(equip_r37));\n    });\n    i0.ɵɵtext(1, \" D\\u00E9saffecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementComponent_tr_225_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 107);\n    i0.ɵɵelement(2, \"img\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 107)(4, \"div\", 109)(5, \"h6\", 110);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 111);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 107)(10, \"span\", 112);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 107)(13, \"span\", 112);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 107)(17, \"span\", 113);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementComponent_tr_225_div_19_Template, 3, 3, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 107)(21, \"span\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 107)(24, \"span\", 112);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 116)(27, \"div\", 117);\n    i0.ɵɵtemplate(28, EquipementComponent_tr_225_button_28_Template, 2, 0, \"button\", 118);\n    i0.ɵɵtemplate(29, EquipementComponent_tr_225_button_29_Template, 2, 0, \"button\", 119);\n    i0.ɵɵtemplate(30, EquipementComponent_tr_225_button_30_Template, 2, 0, \"button\", 120);\n    i0.ɵɵelementStart(31, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_tr_225_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const equip_r37 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.openModal1(equip_r37));\n    });\n    i0.ɵɵtext(32, \" \\u270F\\uFE0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_tr_225_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const equip_r37 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.confirmDelete(equip_r37.idEqui));\n    });\n    i0.ɵɵtext(34, \" \\uD83D\\uDDD1\\uFE0F \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equip_r37 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r37.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r37.model == null ? null : equip_r37.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r37.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 16, equip_r37.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r37.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r37.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r37.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut.toLowerCase() === \"affecte\" || equip_r37.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", equip_r37.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r37.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((equip_r37.fournisseur == null ? null : equip_r37.fournisseur.nomFournisseur) || \"Aucun fournisseur\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"DISPONIBLE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"AFFECTE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"AFFECTE\");\n  }\n}\nfunction EquipementComponent_nav_226_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 129)(1, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_nav_226_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r59);\n      const i_r57 = restoredCtx.index;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.loadEquipements(i_r57));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r57 = ctx.index;\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r57 === ctx_r55.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r57 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementComponent_nav_226_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 127)(1, \"ul\", 128)(2, \"li\", 129)(3, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_nav_226_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.loadEquipements(ctx_r60.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementComponent_nav_226_li_5_Template, 3, 3, \"li\", 131);\n    i0.ɵɵelementStart(6, \"li\", 129)(7, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementComponent_nav_226_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.loadEquipements(ctx_r62.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r29.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r29.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r29.currentPage === ctx_r29.totalPages - 1);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class EquipementComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.isModalOpen = false;\n    this.isEditModalOpen = false;\n    this.isAffectationModalOpen = false;\n    this.isAffectationEditModalOpen = false;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      etatActuel: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      etatActuel: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.imagePreview = null;\n    this.selectedImage = null;\n    this.signupErrors = {};\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    this.form = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    // FormGroup pour la modification\n    this.editForm = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    this.affectationForm = this.fb.group({\n      user: [null, Validators.required],\n      equipement: [null],\n      commentaire: [''],\n      dateAffectation: [new Date()],\n      verrou: ['']\n    });\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    this.form.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire de modification\n    this.editForm.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    this.utilisateurCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateurs = users;\n    });\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  onModelSelected(model) {\n    this.newEquipement1.model = model;\n  }\n  onModelSelectedForAdd(model) {\n    this.form.patchValue({\n      model: model\n    });\n  }\n  onModelSelectedForEdit(model) {\n    this.editForm.patchValue({\n      model: model\n    });\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onModelInputChange(value) {\n    if (!value || typeof value === 'string') {\n      this.newEquipement1.model = null;\n    }\n  }\n  onUserSelected(user) {\n    if (this.isAffectationModalOpen) {\n      this.affectationForm.patchValue({\n        user: user\n      });\n    } else if (this.isAffectationEditModalOpen) {\n      this.EditedAffectation.user = user;\n    }\n    console.log('Utilisateur sélectionné:', user);\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  openModal1(equipement) {\n    const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\n    // Trouver le fournisseur correspondant dans la liste des fournisseurs\n    const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\n    this.newEquipement1 = {\n      ...equipement,\n      model: matchedModel ?? null\n    };\n    // Initialiser le formulaire de modification avec les données de l'équipement\n    this.editForm.patchValue({\n      model: this.newEquipement1.model,\n      numSerie: this.newEquipement1.numSerie,\n      description: this.newEquipement1.description,\n      dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\n      statut: this.newEquipement1.statut,\n      image: null,\n      fournisseurs: matchedFournisseur || null\n    });\n    console.log('Données équipement:', this.newEquipement1);\n    console.log('Fournisseur original:', this.newEquipement1.fournisseur);\n    console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\n    console.log('Date:', this.newEquipement1.dateAffectation);\n    this.modelCtrl.setValue(this.newEquipement1.model);\n    // Affiche la modale\n    this.isEditModalOpen = true;\n  }\n  onEditSubmit() {\n    this.submitted = true;\n    if (this.editForm.invalid) {\n      this.editForm.markAllAsTouched();\n      return;\n    }\n    const equipementData = {\n      ...this.editForm.value,\n      idEqui: this.newEquipement1.idEqui,\n      statut: this.newEquipement1.statut,\n      fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\n    };\n\n    this.authservice.updateEquip(equipementData).subscribe({\n      next: response => {\n        console.log('Update successful:', response);\n        this.showNotification('success', 'Équipement modifié avec succès');\n        this.closeEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const historique = new Historique();\n        historique.date = new Date();\n        historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Update failed:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'équipement');\n      }\n    });\n  }\n  updateData() {\n    console.log('Payload envoyé:', this.newEquipement1);\n    this.authservice.updateEquip(this.newEquipement1).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Équipement modifié avec succès');\n      this.closeModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\n    });\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  deleteEquip(id) {\n    this.authservice.deleteEquip(id).subscribe(() => {\n      this.showNotification('success', 'Équipement supprimé avec succès');\n      this.loadEquipements(this.currentPage);\n    });\n  }\n  enregistrerHistorique(messaege, idEquipement) {\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\n      this.NomEqui = data.equipement.model?.nomModel ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` : null;\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\n      const historique = new Historique();\n      historique.date = data.dateAffectation;\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\n      this.authservice.addHistorique(historique).subscribe({\n        next: response => {\n          console.log('Historique enregistré:', response);\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n    });\n  }\n  desaffecterEquipement(equip) {\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\n    if (isConfirmed) {\n      // Enregistrer l'historique AVANT de supprimer l'affectation\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\n        next: () => {\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\n            next: () => {\n              this.showNotification('success', 'Équipement désaffecté avec succès');\n              this.loadEquipements(this.currentPage);\n              window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n              });\n            },\n            error: error => {\n              console.error('Erreur lors du changement de statut:', error);\n              this.showNotification('error', 'Erreur lors du changement de statut');\n            }\n          });\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\n          this.showNotification('error', 'Échec de la désaffectation');\n        }\n      });\n    }\n  }\n  confirmDelete(ModelId) {\n    console.log(ModelId);\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\n    if (isConfirmed) {\n      this.deleteEquip(ModelId);\n    }\n  }\n  onAffectationSubmit() {\n    if (this.isAffectationModalOpen) {\n      this.handleNewAffectation();\n    } else if (this.isAffectationEditModalOpen) {\n      this.handleEditAffectation();\n    }\n  }\n  handleNewAffectation() {\n    this.affectationFormSubmitted = true;\n    if (!this.affectationForm.get('user')?.value) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    if (!this.selectedEquipement) {\n      console.error('Aucun équipement sélectionné');\n      return;\n    }\n    // S'assurer que l'équipement a le statut DISPONIBLE par défaut\n    this.selectedEquipement.statut = 'DISPONIBLE';\n    this.affectationForm.patchValue({\n      equipement: this.selectedEquipement\n    });\n    this.affectationForm.patchValue({\n      verrou: 'affecter'\n    });\n    console.log('Form Value:', this.affectationForm.value);\n    this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\n      next: response => {\n        console.log('Statut mis à jour avec succès:', response);\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour du statut:', error);\n      }\n    });\n    this.authservice.addAff(this.affectationForm.value).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation créée avec succès !');\n        this.closeAffectationModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const utilisateur = this.affectationForm.get('user')?.value;\n        const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\n        const numSerie = this.selectedEquipement?.numSerie || 'N/A';\n        const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\n      },\n      error: error => {\n        this.showNotification('error', 'Échec de la création de l\\'affectation');\n      }\n    });\n  }\n  handleEditAffectation() {\n    // Validate the edit form - only user is required\n    if (!this.EditedAffectation.user) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    const affectationData = {\n      ...this.EditedAffectation,\n      dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\n    };\n    console.log('Updating affectation:', affectationData);\n    this.authservice.updateAff(affectationData).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation modifiée avec succès !');\n        this.closeAffectationEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique manuellement\n        const historique = new Historique();\n        historique.date = new Date();\n        const equipementNom = this.selectedEquipement.model?.nomModel ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})` : 'Équipement inconnu';\n        const utilisateurNom = this.EditedAffectation.user ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        const dateFormatted = new Date().toLocaleDateString('fr-FR');\n        historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique de réaffectation enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Error updating affectation:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\n      }\n    });\n  }\n  onRegister() {\n    this.submitted = true;\n    console.log(this.form.value.model);\n    if (this.form.invalid) {\n      this.form.markAllAsTouched(); // 🔥 Triggers all error messages\n      return;\n    }\n    const historique = new Historique();\n    const equipementData = {\n      ...this.form.value,\n      statut: 'DISPONIBLE',\n      fournisseur: this.form.value.fournisseurs || null\n    };\n    console.log(equipementData);\n    this.authservice.addEquipement(equipementData).subscribe({\n      next: response => {\n        historique.date = new Date();\n        historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Équipement ajouté avec succès');\n        this.closeModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\n      },\n\n      error: error => {\n        console.error('Registration failed:', error);\n        alert('Échec de l’enregistrement');\n      }\n    });\n  }\n  onImageSelected(event) {\n    const file = event.target.files?.[0];\n    if (file) {\n      this.form.patchValue({\n        image: file\n      });\n      this.form.get('image')?.updateValueAndValidity();\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imagePreview = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n    this.resetForm();\n  }\n  closeEditModal() {\n    this.isEditModalOpen = false;\n    this.editForm.reset();\n    this.submitted = false;\n  }\n  closeOnOutsideClickEdit(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeEditModal();\n    }\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire\n  resetForm() {\n    this.form.reset();\n    // Réinitialiser avec le statut par défaut\n    this.form.patchValue({\n      statut: 'DISPONIBLE'\n    });\n    this.submitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"DISPONIBLE\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      etatActuel: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"DISPONIBLE\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n  }\n  // Méthodes pour l'affectation\n  openAffectationModal(equipement) {\n    this.selectedEquipement = equipement;\n    // Définir automatiquement le statut comme DISPONIBLE\n    this.selectedEquipement.statut = 'DISPONIBLE';\n    this.isAffectationModalOpen = true;\n    this.affectationFormSubmitted = false; // Reset submission state\n    // Réinitialiser le formulaire d'affectation\n    this.affectationForm.patchValue({\n      utilisateur: null,\n      equipement: this.selectedEquipement,\n      commentaire: '',\n      dateAffectation: new Date().toISOString().split('T')[0]\n    });\n  }\n  openEditedModal(equipement) {\n    // Set the selected equipment for the modal\n    this.selectedEquipement = equipement;\n    this.editAffectationFormSubmitted = false; // Reset submission state\n    this.isAffectationEditModalOpen = true;\n  }\n  closeAffectationModal() {\n    this.isAffectationModalOpen = false;\n    this.affectationForm.reset();\n  }\n  updateReaffication(equip) {\n    this.editAffectationFormSubmitted = true;\n    // Check if user is required and missing\n    if (!this.EditedAffectation.user) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    this.EditedAffectation.equipement = equip;\n    this.EditedAffectation.verrou = 'affecter';\n    this.authservice.updateAff(this.EditedAffectation).subscribe({\n      next: data => {\n        console.log(\"Affectation mise à jour avec succès\", data);\n        this.showNotification('success', 'Affectation modifiée avec succès !');\n        this.closeAffectationEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n      },\n\n      error: error => {\n        console.error(\"Erreur lors de la mise à jour de l'affectation\", error);\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\n      }\n    });\n  }\n  closeAffectationEditModal() {\n    this.isAffectationEditModalOpen = false;\n    this.utilisateurCtrl.setValue(null);\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip()\n    };\n  }\n  closeOnOutsideClickAffectation(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeAffectationModal();\n    }\n  }\n  closeOnOutsideClickAffectationEdit(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeAffectationEditModal();\n    }\n  }\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  static {\n    this.ɵfac = function EquipementComponent_Factory(t) {\n      return new (t || EquipementComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementComponent,\n      selectors: [[\"app-equipement\"]],\n      decls: 236,\n      vars: 58,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\", 3, \"click\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-label\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [\"value\", \"DISPONIBLE\"], [\"value\", \"AFFECTE\"], [\"value\", \"MAINTENANCE\"], [\"value\", \"HORS_SERVICE\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"model\", \"placeholder\", \"Rechercher un mod\\u00E8le...\", 3, \"matAutocomplete\"], [\"autoModelEdit\", \"matAutocomplete\"], [\"style\", \"color:red\", 4, \"ngIf\"], [\"for\", \"numSerieEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"numSerieEdit\", \"type\", \"text\", \"formControlName\", \"numSerie\", \"placeholder\", \"Entrer le num\\u00E9ro de s\\u00E9rie\", 1, \"form-inputp\"], [\"for\", \"descriptionEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"descriptionEdit\", \"type\", \"text\", \"formControlName\", \"description\", \"placeholder\", \"Entrer la description (optionnel)\", 1, \"form-inputp\"], [\"for\", \"dateAffectationEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"date\", \"id\", \"dateAffectationEdit\", \"formControlName\", \"dateAffectation\", 1, \"form-control\"], [\"for\", \"fournisseurEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"fournisseurEdit\", \"name\", \"fournisseurEdit\", \"formControlName\", \"fournisseurs\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\"], [\"disabled\", \"\", \"hidden\", \"\", 3, \"ngValue\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"imageEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"file\", \"id\", \"imageEdit\", \"accept\", \"image/*\", 1, \"form-inputp\", 3, \"change\"], [\"style\", \"margin-top: 10px;\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"autoModel\", \"matAutocomplete\"], [\"for\", \"numSerie\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"numSerie\", \"type\", \"text\", \"formControlName\", \"numSerie\", \"placeholder\", \"Entrer le num\\u00E9ro de s\\u00E9rie\", 1, \"form-inputp\"], [\"for\", \"description\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"description\", \"type\", \"text\", \"formControlName\", \"description\", \"placeholder\", \"Entrer la description (optionnel)\", 1, \"form-inputp\"], [\"for\", \"dateAffectation\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"date\", \"id\", \"dateAffectation\", \"formControlName\", \"dateAffectation\", 1, \"form-control\"], [\"for\", \"fournisseur\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"type\", \"name\", \"fournisseur\", \"formControlName\", \"fournisseurs\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\"], [\"for\", \"image\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"file\", \"id\", \"image\", \"accept\", \"image/*\", 1, \"form-inputp\", 3, \"change\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"20px\"], [\"style\", \"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"auto\", \"matAutocomplete\"], [\"style\", \"color:red; font-size: 12px;\", 4, \"ngIf\"], [\"for\", \"commentaire\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"formControlName\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\"], [\"type\", \"date\", \"formControlName\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\"], [\"type\", \"submit\", 1, \"btn-submit\"], [3, \"ngSubmit\"], [\"editAffectationForm\", \"ngForm\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", \"required\", \"\", 3, \"formControl\", \"matAutocomplete\"], [\"name\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"date\", \"name\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", 3, \"ngModel\", \"ngModelChange\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [3, \"value\"], [2, \"color\", \"red\"], [3, \"ngValue\"], [2, \"margin-top\", \"10px\"], [\"alt\", \"Aper\\u00E7u\", 2, \"width\", \"100px\", \"height\", \"auto\", \"border\", \"1px solid #ccc\", \"border-radius\", \"5px\", 3, \"src\"], [2, \"margin-bottom\", \"20px\", \"padding\", \"15px\", \"background-color\", \"#f8f9fa\", \"border-radius\", \"8px\"], [2, \"margin\", \"0\", \"color\", \"#333\"], [2, \"margin\", \"5px 0 0 0\", \"color\", \"#666\", \"font-size\", \"14px\"], [2, \"color\", \"red\", \"font-size\", \"12px\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"fw-normal\", \"text-truncate\", 2, \"max-width\", \"150px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-1\", \"text-end\"], [1, \"d-flex\", \"justify-content-end\", \"gap-1\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-warning\", \"title\", \"R\\u00E9affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-secondary\", \"title\", \"D\\u00E9saffecter\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-muted\", \"small\", \"mt-1\"], [\"title\", \"Affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"R\\u00E9affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-warning\", 3, \"click\"], [\"title\", \"D\\u00E9saffecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function EquipementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_button_click_24_listener() {\n            return ctx.openModal();\n          });\n          i0.ɵɵelementStart(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel \\u00E9quipement \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 17)(42, \"label\", 23);\n          i0.ɵɵtext(43, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"select\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementComponent_Template_select_ngModelChange_44_listener($event) {\n            return ctx.selectedStatut = $event;\n          })(\"change\", function EquipementComponent_Template_select_change_44_listener() {\n            return ctx.loadEquipements(0);\n          });\n          i0.ɵɵelementStart(45, \"option\", 25);\n          i0.ɵɵtext(46, \"Tous les statuts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"option\", 26);\n          i0.ɵɵtext(48, \"Disponible\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 27);\n          i0.ɵɵtext(50, \"Affect\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 28);\n          i0.ɵɵtext(52, \"En maintenance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"option\", 29);\n          i0.ɵɵtext(54, \"Hors service\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(55, \"div\", 30)(56, \"div\", 31)(57, \"input\", 32);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementComponent_Template_input_ngModelChange_57_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementComponent_Template_input_input_57_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"span\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_59_listener($event) {\n            return ctx.closeOnOutsideClickEdit($event);\n          });\n          i0.ɵɵelementStart(60, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_60_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(61, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_span_click_61_listener() {\n            return ctx.closeEditModal();\n          });\n          i0.ɵɵtext(62, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"h3\", 37);\n          i0.ɵɵtext(64, \"Modifier l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"form\", 38);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementComponent_Template_form_ngSubmit_65_listener() {\n            return ctx.onEditSubmit();\n          });\n          i0.ɵɵelement(66, \"br\")(67, \"br\");\n          i0.ɵɵelementStart(68, \"mat-form-field\", 18)(69, \"mat-label\");\n          i0.ɵɵtext(70, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(71, \"input\", 39);\n          i0.ɵɵelementStart(72, \"mat-autocomplete\", 20, 40);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementComponent_Template_mat_autocomplete_optionSelected_72_listener($event) {\n            return ctx.onModelSelectedForEdit($event.option.value);\n          });\n          i0.ɵɵtemplate(74, EquipementComponent_mat_option_74_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(75, EquipementComponent_div_75_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(76, \"label\", 42);\n          i0.ɵɵtext(77, \"Num\\u00E9ro de s\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(78, \"input\", 43);\n          i0.ɵɵtemplate(79, EquipementComponent_div_79_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(80, \"label\", 44);\n          i0.ɵɵtext(81, \"Description (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"input\", 45);\n          i0.ɵɵelementStart(83, \"label\", 46);\n          i0.ɵɵtext(84, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(85, \"input\", 47);\n          i0.ɵɵtemplate(86, EquipementComponent_div_86_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(87, \"label\", 48);\n          i0.ɵɵtext(88, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"select\", 49)(90, \"option\", 50);\n          i0.ɵɵtext(91, \"S\\u00E9lectionner fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(92, EquipementComponent_option_92_Template, 2, 2, \"option\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(93, EquipementComponent_div_93_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(94, \"label\", 52);\n          i0.ɵɵtext(95, \"Logo d'\\u00E9quipement (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"input\", 53);\n          i0.ɵɵlistener(\"change\", function EquipementComponent_Template_input_change_96_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(97, EquipementComponent_div_97_Template, 2, 1, \"div\", 54);\n          i0.ɵɵelement(98, \"br\");\n          i0.ɵɵelementStart(99, \"button\", 55);\n          i0.ɵɵtext(100, \" Modifier \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(101, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_101_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(102, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_102_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(103, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_span_click_103_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(104, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"h3\", 37);\n          i0.ɵɵtext(106, \"Ajouter un nouvel Equipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"form\", 38);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementComponent_Template_form_ngSubmit_107_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(108, \"br\")(109, \"br\");\n          i0.ɵɵelementStart(110, \"mat-form-field\", 18)(111, \"mat-label\");\n          i0.ɵɵtext(112, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(113, \"input\", 39);\n          i0.ɵɵelementStart(114, \"mat-autocomplete\", 20, 56);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementComponent_Template_mat_autocomplete_optionSelected_114_listener($event) {\n            return ctx.onModelSelectedForAdd($event.option.value);\n          });\n          i0.ɵɵtemplate(116, EquipementComponent_mat_option_116_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(117, EquipementComponent_div_117_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(118, \"label\", 57);\n          i0.ɵɵtext(119, \"Num\\u00E9ro de s\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(120, \"input\", 58);\n          i0.ɵɵtemplate(121, EquipementComponent_div_121_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(122, \"label\", 59);\n          i0.ɵɵtext(123, \"Description (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(124, \"input\", 60);\n          i0.ɵɵelementStart(125, \"label\", 61);\n          i0.ɵɵtext(126, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(127, \"input\", 62);\n          i0.ɵɵtemplate(128, EquipementComponent_div_128_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(129, \"label\", 63);\n          i0.ɵɵtext(130, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"select\", 64)(132, \"option\", 50);\n          i0.ɵɵtext(133, \"S\\u00E9lectionner fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(134, EquipementComponent_option_134_Template, 2, 2, \"option\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(135, EquipementComponent_div_135_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(136, \"label\", 65);\n          i0.ɵɵtext(137, \"Logo d'\\u00E9quipement (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"input\", 66);\n          i0.ɵɵlistener(\"change\", function EquipementComponent_Template_input_change_138_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(139, EquipementComponent_div_139_Template, 2, 1, \"div\", 54);\n          i0.ɵɵelement(140, \"br\");\n          i0.ɵɵelementStart(141, \"button\", 55);\n          i0.ɵɵtext(142, \" Enregistrer \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(143, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_143_listener($event) {\n            return ctx.closeOnOutsideClickAffectation($event);\n          });\n          i0.ɵɵelementStart(144, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_144_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(145, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_span_click_145_listener() {\n            return ctx.closeAffectationModal();\n          });\n          i0.ɵɵtext(146, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"h3\", 67);\n          i0.ɵɵtext(148, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(149, EquipementComponent_div_149_Template, 5, 2, \"div\", 68);\n          i0.ɵɵelementStart(150, \"form\", 69);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementComponent_Template_form_ngSubmit_150_listener() {\n            return ctx.onAffectationSubmit();\n          });\n          i0.ɵɵelementStart(151, \"mat-form-field\", 18)(152, \"mat-label\");\n          i0.ɵɵtext(153, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(154, \"input\", 19);\n          i0.ɵɵelementStart(155, \"mat-autocomplete\", 20, 70);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementComponent_Template_mat_autocomplete_optionSelected_155_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(157, EquipementComponent_mat_option_157_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(158, EquipementComponent_div_158_Template, 2, 0, \"div\", 71);\n          i0.ɵɵelementStart(159, \"label\", 72);\n          i0.ɵɵtext(160, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"textarea\", 73);\n          i0.ɵɵtext(162, \"      \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"label\", 61);\n          i0.ɵɵtext(164, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(165, \"input\", 74)(166, \"br\");\n          i0.ɵɵelementStart(167, \"button\", 75);\n          i0.ɵɵtext(168, \" Affecter l'\\u00E9quipement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(169, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_169_listener($event) {\n            return ctx.closeOnOutsideClickAffectationEdit($event);\n          });\n          i0.ɵɵelementStart(170, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_div_click_170_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(171, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementComponent_Template_span_click_171_listener() {\n            return ctx.closeAffectationEditModal();\n          });\n          i0.ɵɵtext(172, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"h3\", 67);\n          i0.ɵɵtext(174, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(175, EquipementComponent_div_175_Template, 5, 2, \"div\", 68);\n          i0.ɵɵelementStart(176, \"form\", 76, 77);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementComponent_Template_form_ngSubmit_176_listener() {\n            return ctx.selectedEquipement && ctx.updateReaffication(ctx.selectedEquipement);\n          });\n          i0.ɵɵelementStart(178, \"mat-form-field\", 18)(179, \"mat-label\");\n          i0.ɵɵtext(180, \"Utilisateur Actuel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(181, \"input\", 78);\n          i0.ɵɵelementStart(182, \"mat-autocomplete\", 20, 70);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementComponent_Template_mat_autocomplete_optionSelected_182_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(184, EquipementComponent_mat_option_184_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(185, EquipementComponent_div_185_Template, 2, 0, \"div\", 71);\n          i0.ɵɵelementStart(186, \"label\", 72);\n          i0.ɵɵtext(187, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(188, \"textarea\", 79);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementComponent_Template_textarea_ngModelChange_188_listener($event) {\n            return ctx.EditedAffectation.commentaire = $event;\n          });\n          i0.ɵɵtext(189, \"  \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"label\", 61);\n          i0.ɵɵtext(191, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"input\", 80);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementComponent_Template_input_ngModelChange_192_listener($event) {\n            return ctx.EditedAffectation.dateAffectation = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(193, \"br\");\n          i0.ɵɵelementStart(194, \"button\", 75);\n          i0.ɵɵtext(195, \" Modifier Affectation \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(196, \"body\", 81)(197, \"div\", 82);\n          i0.ɵɵtemplate(198, EquipementComponent_div_198_Template, 2, 2, \"div\", 83);\n          i0.ɵɵelementStart(199, \"div\", 7)(200, \"div\", 84)(201, \"div\", 85)(202, \"div\", 86)(203, \"div\", 14)(204, \"div\", 87)(205, \"table\", 88)(206, \"thead\")(207, \"tr\")(208, \"th\", 89);\n          i0.ɵɵtext(209, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(210, \"th\", 89);\n          i0.ɵɵtext(211, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(212, \"th\", 89);\n          i0.ɵɵtext(213, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"th\", 89);\n          i0.ɵɵtext(215, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(216, \"th\", 89);\n          i0.ɵɵtext(217, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(218, \"th\", 89);\n          i0.ɵɵtext(219, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(220, \"th\", 89);\n          i0.ɵɵtext(221, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(222, \"th\", 90);\n          i0.ɵɵtext(223, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(224, \"tbody\");\n          i0.ɵɵtemplate(225, EquipementComponent_tr_225_Template, 35, 19, \"tr\", 91);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(226, EquipementComponent_nav_226_Template, 9, 6, \"nav\", 92);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(227, \"div\", 84)(228, \"div\", 93)(229, \"p\", 94);\n          i0.ɵɵtext(230, \"Design and Developed by \");\n          i0.ɵɵelementStart(231, \"a\", 95);\n          i0.ɵɵtext(232, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(233, \" Distributed by \");\n          i0.ɵɵelementStart(234, \"a\", 96);\n          i0.ɵɵtext(235, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          const _r2 = i0.ɵɵreference(73);\n          const _r10 = i0.ɵɵreference(115);\n          const _r19 = i0.ɵɵreference(156);\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          let tmp_16_0;\n          let tmp_23_0;\n          let tmp_24_0;\n          let tmp_25_0;\n          let tmp_28_0;\n          let tmp_37_0;\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedStatut);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c1, ctx.isEditModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modelet);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.editForm.get(\"model\")) == null ? null : tmp_11_0.invalid) && (((tmp_11_0 = ctx.editForm.get(\"model\")) == null ? null : tmp_11_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.editForm.get(\"numSerie\")) == null ? null : tmp_12_0.invalid) && (((tmp_12_0 = ctx.editForm.get(\"numSerie\")) == null ? null : tmp_12_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.editForm.get(\"dateAffectation\")) == null ? null : tmp_13_0.invalid) && (((tmp_13_0 = ctx.editForm.get(\"dateAffectation\")) == null ? null : tmp_13_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.editForm.get(\"fournisseurs\")) == null ? null : tmp_16_0.invalid) && (((tmp_16_0 = ctx.editForm.get(\"fournisseurs\")) == null ? null : tmp_16_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.imagePreview);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c1, ctx.isModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r10);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modelet);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.form.get(\"model\")) == null ? null : tmp_23_0.invalid) && (((tmp_23_0 = ctx.form.get(\"model\")) == null ? null : tmp_23_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = ctx.form.get(\"numSerie\")) == null ? null : tmp_24_0.invalid) && (((tmp_24_0 = ctx.form.get(\"numSerie\")) == null ? null : tmp_24_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = ctx.form.get(\"dateAffectation\")) == null ? null : tmp_25_0.invalid) && (((tmp_25_0 = ctx.form.get(\"dateAffectation\")) == null ? null : tmp_25_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = ctx.form.get(\"fournisseurs\")) == null ? null : tmp_28_0.invalid) && (((tmp_28_0 = ctx.form.get(\"fournisseurs\")) == null ? null : tmp_28_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.imagePreview);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c1, ctx.isAffectationModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.affectationForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r19);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_37_0 = ctx.affectationForm.get(\"user\")) == null ? null : tmp_37_0.value) && ctx.affectationFormSubmitted);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(56, _c1, ctx.isAffectationEditModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r19);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.EditedAffectation.user && ctx.editAffectationFormSubmitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.commentaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.dateAffectation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, i3.FormControlDirective, i3.FormGroupDirective, i3.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #0051ff; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #d1d5db;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n\\n\\n.tag.hover-effect[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) !important;\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  filter: brightness(1.1);\\n}\\n\\n\\n\\n.varient-table[_ngcontent-%COMP%] {\\n  border-collapse: separate;\\n  border-spacing: 0;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  padding: 1rem 0.5rem;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #eee;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 1rem 0.5rem;\\n  vertical-align: middle;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   .badge[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  padding: 0.25rem 0.5rem;\\n  font-size: 0.75rem;\\n  border-radius: 0.375rem;\\n}\\n\\n.text-truncate[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n\\n\\n.container.my-2[_ngcontent-%COMP%] {\\n  margin-top: 0.5rem !important;\\n  margin-bottom: 0.5rem !important;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  margin-bottom: 10px !important;\\n}\\n\\n\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  overflow: hidden;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n}\\n\\n.varient-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border: 2px solid #e9ecef;\\n}\\n\\n\\n\\n.badge.rounded-pill[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  font-size: 0.7rem;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "Historique", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r30", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "model_r31", "ɵɵtextInterpolate1", "nomModel", "fournisseur_r32", "nomFournisseur", "ɵɵelement", "ctx_r9", "imagePreview", "ɵɵsanitizeUrl", "model_r33", "fournisseur_r34", "ctx_r17", "ɵɵtextInterpolate", "ctx_r18", "selectedEquipement", "model", "numSerie", "user_r35", "ctx_r22", "user_r36", "ctx_r27", "notification", "type", "message", "ɵɵpipeBind1", "ctx_r38", "NameUtilisateur", "equip_r37", "idEqui", "ɵɵlistener", "EquipementComponent_tr_225_button_28_Template_button_click_0_listener", "ɵɵrestoreView", "_r45", "ɵɵnextContext", "$implicit", "ctx_r43", "ɵɵresetView", "openAffectationModal", "EquipementComponent_tr_225_button_29_Template_button_click_0_listener", "_r48", "ctx_r46", "openEditedModal", "EquipementComponent_tr_225_button_30_Template_button_click_0_listener", "_r51", "ctx_r49", "desaffecterEquipement", "ɵɵtemplate", "EquipementComponent_tr_225_div_19_Template", "EquipementComponent_tr_225_button_28_Template", "EquipementComponent_tr_225_button_29_Template", "EquipementComponent_tr_225_button_30_Template", "EquipementComponent_tr_225_Template_button_click_31_listener", "restoredCtx", "_r53", "ctx_r52", "openModal1", "EquipementComponent_tr_225_Template_button_click_33_listener", "ctx_r54", "confirmDelete", "image", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "description", "<PERSON><PERSON><PERSON><PERSON>", "EquipementComponent_nav_226_li_5_Template_a_click_1_listener", "_r59", "i_r57", "index", "ctx_r58", "loadEquipements", "ɵɵclassProp", "ctx_r55", "currentPage", "EquipementComponent_nav_226_Template_a_click_3_listener", "_r61", "ctx_r60", "EquipementComponent_nav_226_li_5_Template", "EquipementComponent_nav_226_Template_a_click_7_listener", "ctx_r62", "ctx_r29", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "EquipementComponent", "authservice", "http", "fb", "utilisateurService", "isModalOpen", "isEditModalOpen", "isAffectationModalOpen", "isAffectationEditModalOpen", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "etatActuel", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "selectedImage", "signupErrors", "ngOnInit", "GetAllModels", "getFournisseur", "form", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "editForm", "affectationForm", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "get", "searchUsers", "users", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "displayUtilisateur", "displayModel", "onModelSelected", "onModelSelectedForAdd", "patchValue", "onModelSelectedForEdit", "getallFournisseur", "data", "onModelInputChange", "onUserSelected", "log", "onUserSearchSelected", "closeOnOutsideClick", "event", "target", "classList", "contains", "closeModal", "matchedModel", "find", "m", "idModel", "matchedFournisseur", "f", "idFournisseur", "formatDateForInput", "onEditSubmit", "invalid", "mark<PERSON>llAsTouched", "equipementData", "updateEquip", "response", "showNotification", "closeEditModal", "historique", "date", "addHistorique", "updateData", "page", "keyword", "username", "userVal", "searchEquipements1", "getAllEquipements", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "deleteEquip", "enregistrerHistorique", "messaege", "idEquipement", "getAffectationById", "dateFormatted", "toLocaleDateString", "equip", "isConfirmed", "window", "confirm", "deleteAff", "addStatutDisponible", "scrollTo", "top", "behavior", "ModelId", "onAffectationSubmit", "handleNewAffectation", "handleEditAffectation", "addStatutAffecte", "addAff", "closeAffectationModal", "utilisateur", "equipementNom", "utilisateurNom", "affectationData", "updateAff", "closeAffectationEditModal", "onRegister", "addEquipement", "alert", "onImageSelected", "file", "files", "updateValueAndValidity", "reader", "FileReader", "onload", "result", "readAsDataURL", "onFileSelected", "formData", "FormData", "append", "post", "imageUrl", "fullUrl", "resetErrors", "getAllModel", "openModal", "resetForm", "reset", "closeOnOutsideClickEdit", "setTimeout", "hideNotification", "toISOString", "split", "updateReaffication", "closeOnOutsideClickAffectation", "closeOnOutsideClickAffectationEdit", "date<PERSON><PERSON>j", "isNaN", "getTime", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementComponent_Template", "rf", "ctx", "EquipementComponent_Template_button_click_24_listener", "EquipementComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementComponent_mat_option_40_Template", "EquipementComponent_Template_select_ngModelChange_44_listener", "EquipementComponent_Template_select_change_44_listener", "EquipementComponent_Template_input_ngModelChange_57_listener", "EquipementComponent_Template_input_input_57_listener", "EquipementComponent_Template_div_click_59_listener", "EquipementComponent_Template_div_click_60_listener", "stopPropagation", "EquipementComponent_Template_span_click_61_listener", "EquipementComponent_Template_form_ngSubmit_65_listener", "EquipementComponent_Template_mat_autocomplete_optionSelected_72_listener", "EquipementComponent_mat_option_74_Template", "EquipementComponent_div_75_Template", "EquipementComponent_div_79_Template", "EquipementComponent_div_86_Template", "EquipementComponent_option_92_Template", "EquipementComponent_div_93_Template", "EquipementComponent_Template_input_change_96_listener", "EquipementComponent_div_97_Template", "EquipementComponent_Template_div_click_101_listener", "EquipementComponent_Template_div_click_102_listener", "EquipementComponent_Template_span_click_103_listener", "EquipementComponent_Template_form_ngSubmit_107_listener", "EquipementComponent_Template_mat_autocomplete_optionSelected_114_listener", "EquipementComponent_mat_option_116_Template", "EquipementComponent_div_117_Template", "EquipementComponent_div_121_Template", "EquipementComponent_div_128_Template", "EquipementComponent_option_134_Template", "EquipementComponent_div_135_Template", "EquipementComponent_Template_input_change_138_listener", "EquipementComponent_div_139_Template", "EquipementComponent_Template_div_click_143_listener", "EquipementComponent_Template_div_click_144_listener", "EquipementComponent_Template_span_click_145_listener", "EquipementComponent_div_149_Template", "EquipementComponent_Template_form_ngSubmit_150_listener", "EquipementComponent_Template_mat_autocomplete_optionSelected_155_listener", "EquipementComponent_mat_option_157_Template", "EquipementComponent_div_158_Template", "EquipementComponent_Template_div_click_169_listener", "EquipementComponent_Template_div_click_170_listener", "EquipementComponent_Template_span_click_171_listener", "EquipementComponent_div_175_Template", "EquipementComponent_Template_form_ngSubmit_176_listener", "EquipementComponent_Template_mat_autocomplete_optionSelected_182_listener", "EquipementComponent_mat_option_184_Template", "EquipementComponent_div_185_Template", "EquipementComponent_Template_textarea_ngModelChange_188_listener", "EquipementComponent_Template_input_ngModelChange_192_listener", "EquipementComponent_div_198_Template", "EquipementComponent_tr_225_Template", "EquipementComponent_nav_226_Template", "_r0", "ɵɵpureFunction1", "_c1", "_r2", "tmp_11_0", "touched", "tmp_12_0", "tmp_13_0", "tmp_16_0", "_r10", "tmp_23_0", "tmp_24_0", "tmp_25_0", "tmp_28_0", "_r19", "tmp_37_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\equipement\\equipement.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\equipement\\equipement.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Equip } from './equip';\r\nimport { Model } from '../model/Model';\r\nimport { TypeService } from '../dashboard/type.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport * as bootstrap from 'bootstrap';\r\n// or for just Modal:\r\nimport { Modal } from 'bootstrap';\r\nimport { Fournisseur } from '../fournisseur/Fournisseur';\r\nimport { Utilisateur } from '../utilisateur/utilisateur';\r\nimport { UtilisateurService } from '../utilisateur/utilisateur.service';\r\nimport { AffectationEquipement } from '../affecta/AffectationEquipement';\r\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\r\nimport { Affectation } from '../affecta/Affectation';\r\nimport { Historique } from './Historique';\r\n@Component({\r\n  selector: 'app-equipement',\r\n  templateUrl: './equipement.component.html',\r\n  styleUrls: ['./equipement.component.css']\r\n})\r\nexport class EquipementComponent implements OnInit {\r\nisModalOpen = false;\r\nisEditModalOpen = false;\r\nisAffectationModalOpen = false;\r\nisAffectationEditModalOpen = false;\r\nmodels:Model[]=[];\r\nequiements:Equip[]=[];\r\nutilisateurs: Utilisateur[] = [];\r\n\r\nfilteredUtilisateurs: Utilisateur[] = [];\r\nfilteredUtilisateursSearch: Utilisateur[] = [];\r\nmodelet: Model[] = [];\r\n  NomEqui:String|null=null;\r\n    NomUser:String|null=null;\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\ncurrentPage = 0;\r\npageSize = 4;\r\nsearchTerm: string = '';\r\n\r\n// Affectation form\r\naffectationForm!: FormGroup;\r\nselectedEquipement!: Equip \r\naffectationFormSubmitted = false;\r\neditAffectationFormSubmitted = false;\r\n\r\nnewEquipement:Equip={\r\nidEqui:0,\r\nnumSerie:\"\",\r\nstatut:\"\",\r\nimage:\"\",\r\nmodel:null,\r\ndateAffectation:new Date,\r\ndescription:\"\",\r\nfournisseur:null,\r\netatActuel:null,\r\n\r\n};\r\nnewEquipement1:Equip={\r\nidEqui:0,\r\nnumSerie:\"\",\r\nstatut:\"\",\r\nimage:\"\",\r\nmodel:null,\r\ndateAffectation:new Date,\r\ndescription:\"\",\r\nfournisseur:null,\r\netatActuel:null,\r\n\r\n};\r\nform!: FormGroup;\r\neditForm!: FormGroup;\r\n\r\nEditedAffectation:Affectation={\r\n  id:0,\r\n  commentaire:\"\",\r\n  dateAffectation:new Date(),\r\n  user:new Utilisateur(),\r\n  equipement:new Equip(),\r\n  verrou:\"\"\r\n\r\n}\r\nNameUtilisateur:string[]=[];\r\nidsEqui:number[]=[];\r\ntableAffectation: any = {};\r\n\r\nsubmitted = false;\r\nfournisseurs:Fournisseur[]=[]; \r\n  totalPages: any;\r\n  utilisateurCtrl = new FormControl();\r\n  utilisateurSearchCtrl = new FormControl();\r\n  modelCtrl = new FormControl();\r\nconstructor(\r\n  private authservice:TypeService,\r\n  private http:HttpClient,\r\n  private fb: FormBuilder,\r\n  private utilisateurService: UtilisateurService\r\n) { }\r\n  ngOnInit(): void {\r\n      this.currentPage = 0;\r\n\r\n    this.GetAllModels();\r\n    this.loadEquipements(this.currentPage);\r\n    this.getFournisseur();\r\n \r\n\r\nthis.form = this.fb.group({\r\n  model: [null, Validators.required],\r\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\r\n  description: [''], \r\n  dateAffectation: ['', Validators.required],\r\n  statut: ['DISPONIBLE'], \r\n  image: [null], \r\n  fournisseurs: [null, Validators.required]\r\n});\r\n\r\n// FormGroup pour la modification\r\nthis.editForm = this.fb.group({\r\n  model: [null, Validators.required],\r\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\r\n  description: [''], // Description optionnelle - pas de validation\r\n  dateAffectation: ['', Validators.required],\r\n  statut: ['DISPONIBLE'], // Statut par défaut, pas de validation requise\r\n  image: [null], // Image optionnelle - pas de validation\r\n  fournisseurs: [null, Validators.required]\r\n});\r\n\r\n\r\n\r\n\r\nthis.affectationForm = this.fb.group({\r\n  user: [null, Validators.required],\r\n  equipement: [null],\r\n  commentaire: [''], // No validation - optional\r\n  dateAffectation: [new Date()], // No validation - optional\r\n  verrou: ['']\r\n});\r\n\r\n\r\n\r\n// Autocomplete pour le modal de modification\r\nthis.modelCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n// Autocomplete pour le formulaire d'ajout\r\nthis.form.get('model')?.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n// Autocomplete pour le formulaire de modification\r\nthis.editForm.get('model')?.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n\r\nthis.utilisateurCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchUsers(value.trim());\r\n        } else {\r\n\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(users => {\r\n    this.filteredUtilisateurs = users;\r\n  });\r\n\r\n// Autocomplete pour la recherche\r\nthis.utilisateurSearchCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    tap((value: any) => {\r\n\r\n      if (typeof value === 'string' && value.trim() === '') {\r\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\r\n        this.loadEquipements(0);\r\n      }\r\n    }),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n\r\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\r\n  next: (res) => {\r\n    this.equiements = res.content;\r\n    this.totalPages = res.totalPages;\r\n    this.fetchUtilisateurs(this.equiements);\r\n  },\r\n  error: (err) => console.error(err)\r\n});\r\nthis.loadEquipements(0);\r\n          \r\n          return this.authservice.searchUsers(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(users => {\r\n    this.filteredUtilisateursSearch = users;\r\n  });\r\n\r\n\r\n\r\n}\r\n\r\n\r\n\r\n\r\n  \r\n\r\ndisplayUtilisateur(user: any): string {\r\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\r\n}\r\n\r\n\r\ndisplayModel(model: any): string {\r\n  return  model? `${model.nomModel}` : '';\r\n}\r\n\r\nonModelSelected(model: any): void {\r\n  this.newEquipement1.model = model;\r\n}\r\n\r\nonModelSelectedForAdd(model: any): void {\r\n  this.form.patchValue({ model: model });\r\n}\r\n\r\nonModelSelectedForEdit(model: any): void {\r\n  this.editForm.patchValue({ model: model });\r\n}\r\n\r\n\r\n\r\n getFournisseur()\r\n  {\r\n\r\n  this.authservice.getallFournisseur().subscribe(data => {\r\n  this.fournisseurs = data;\r\n\r\n});\r\n\r\n\r\n  }\r\n\r\n  onModelInputChange(value: string) {\r\n\r\n  if (!value || typeof value === 'string') {\r\n    this.newEquipement1.model = null;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  onUserSelected(user: Utilisateur) {\r\n\r\n    if (this.isAffectationModalOpen) {\r\n\r\n      this.affectationForm.patchValue({\r\n        user: user\r\n      });\r\n    } else if (this.isAffectationEditModalOpen) {\r\n\r\n      this.EditedAffectation.user = user;\r\n    }\r\n    console.log('Utilisateur sélectionné:', user);\r\n  }\r\n\r\n  onUserSearchSelected(user: Utilisateur) {\r\n    this.loadEquipements(0);\r\n \r\n  }\r\n\r\n  closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\nopenModal1(equipement: Equip) {\r\n  const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\r\n\r\n  // Trouver le fournisseur correspondant dans la liste des fournisseurs\r\n  const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\r\n\r\n  this.newEquipement1 = {\r\n    ...equipement,\r\n    model: matchedModel ?? null\r\n  };\r\n\r\n  // Initialiser le formulaire de modification avec les données de l'équipement\r\n  this.editForm.patchValue({\r\n    model: this.newEquipement1.model,\r\n    numSerie: this.newEquipement1.numSerie,\r\n    description: this.newEquipement1.description,\r\n    dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\r\n    statut: this.newEquipement1.statut,\r\n    image: null,\r\n    fournisseurs: matchedFournisseur || null\r\n  });\r\n\r\n  console.log('Données équipement:', this.newEquipement1);\r\n  console.log('Fournisseur original:', this.newEquipement1.fournisseur);\r\n  console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\r\n  console.log('Date:', this.newEquipement1.dateAffectation);\r\n\r\n  this.modelCtrl.setValue(this.newEquipement1.model);\r\n\r\n  // Affiche la modale\r\n  this.isEditModalOpen = true;\r\n}\r\n\r\nonEditSubmit(): void {\r\n  this.submitted = true;\r\n\r\n  if (this.editForm.invalid) {\r\n    this.editForm.markAllAsTouched(); \r\n    return;\r\n  }\r\n\r\n  const equipementData = {\r\n    ...this.editForm.value,\r\n    idEqui: this.newEquipement1.idEqui, // Garder l'ID original\r\n    statut: this.newEquipement1.statut, // Préserver le statut original\r\n    fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\r\n  };\r\n\r\n  this.authservice.updateEquip(equipementData).subscribe({\r\n    next: (response) => {\r\n      console.log('Update successful:', response);\r\n      this.showNotification('success', 'Équipement modifié avec succès');\r\n      this.closeEditModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n      const historique = new Historique();\r\n      historique.date = new Date();\r\n      historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n    },\r\n    error: (error) => {\r\n      console.error('Update failed:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\r\n    }\r\n  });\r\n}\r\n\r\nupdateData() {\r\n  console.log('Payload envoyé:', this.newEquipement1);\r\n  this.authservice.updateEquip(this.newEquipement1).subscribe(\r\n    (response) => {\r\n      console.log('Update successful:', response);\r\n      this.showNotification('success', 'Équipement modifié avec succès');\r\n      this.closeModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n\r\n    },\r\n    (error) => {\r\n      console.error('Update failed:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\r\n    }\r\n  );\r\n}\r\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\r\n\r\nloadEquipements(page: number): void {\r\n  this.currentPage = page;\r\n\r\n  const keyword = this.searchTerm.trim();\r\n  const statut = this.selectedStatut.trim();\r\n\r\n  let username = '';\r\n  const userVal = this.utilisateurSearchCtrl.value;\r\n\r\n  if (typeof userVal === 'string') {\r\n    username = userVal.trim();\r\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\r\n    username = userVal.username.trim();\r\n  }\r\n\r\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\r\n  if (username !== '') {\r\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 2 : Statut seul (sans username)\r\n  if (keyword === '' && statut !== '') {\r\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 3 : Keyword seul (sans username ni statut)\r\n  if (keyword !== '' && statut === '') {\r\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 4 : Keyword + Statut (sans username)\r\n  if (keyword !== '' && statut !== '') {\r\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 5 : Aucun filtre → tout afficher\r\n  this.authservice.getAllEquipements(page, this.pageSize).subscribe({\r\n    next: (res) => {\r\n      this.equiements = res.content;\r\n      this.totalPages = res.totalPages;\r\n      this.fetchUtilisateurs(this.equiements);\r\n    },\r\n    error: (err) => console.error(err)\r\n  });\r\n}\r\n\r\n\r\nprivate fetchUtilisateurs(equiements: any[]): void {\r\n  console.log(equiements);\r\n  equiements.forEach(eq => {\r\n   \r\n    this.idsEqui[eq.idEqui]=eq.idEqui;\r\n     })\r\n     console.log(this.idsEqui); \r\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\r\n\r\n      data.forEach(affectation => {\r\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\r\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\r\n      \r\n      });\r\n    });\r\n\r\n\r\n\r\n\r\n}\r\n\r\n\r\n  onSearchChange(): void {\r\n\r\n    this.loadEquipements(0);\r\n  }\r\n\r\n  \r\n   deleteEquip(id: number) {\r\n    this.authservice.deleteEquip(id).subscribe(() => {\r\n      this.showNotification('success', 'Équipement supprimé avec succès');\r\n      this.loadEquipements(this.currentPage); \r\n\r\n\r\n    });\r\n  }\r\n\r\n\r\n\r\n\r\n  private enregistrerHistorique(messaege: string, idEquipement: number) {\r\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\r\n    this.NomEqui = data.equipement.model?.nomModel \r\n  ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` \r\n  : null;\r\n\r\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\r\n\r\n      const historique = new Historique();\r\n      historique.date = data.dateAffectation;\r\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\r\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\r\n\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  desaffecterEquipement(equip: Equip) {\r\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\r\n    if (isConfirmed) {\r\n      // Enregistrer l'historique AVANT de supprimer l'affectation\r\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\r\n\r\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\r\n        next: () => {\r\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\r\n            next: () => {\r\n              this.showNotification('success', 'Équipement désaffecté avec succès');\r\n              this.loadEquipements(this.currentPage);\r\n              window.scrollTo({ top: 0, behavior: 'smooth' });\r\n            },\r\n            error: (error) => {\r\n              console.error('Erreur lors du changement de statut:', error);\r\n              this.showNotification('error', 'Erreur lors du changement de statut');\r\n            }\r\n          });\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\r\n          this.showNotification('error', 'Échec de la désaffectation');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n   confirmDelete(ModelId: number): void {\r\n    console.log(ModelId);\r\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\r\n    if (isConfirmed) {\r\n      this.deleteEquip(ModelId);\r\n    }\r\n  }\r\nonAffectationSubmit() {\r\n  \r\n  if (this.isAffectationModalOpen) {\r\n\r\n    this.handleNewAffectation();\r\n  } else if (this.isAffectationEditModalOpen) {\r\n   \r\n    this.handleEditAffectation();\r\n  }\r\n}\r\n\r\nprivate handleNewAffectation() {\r\n  this.affectationFormSubmitted = true;\r\n\r\n  if (!this.affectationForm.get('user')?.value) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  if (!this.selectedEquipement) {\r\n    console.error('Aucun équipement sélectionné');\r\n    return;\r\n  }\r\n\r\n  // S'assurer que l'équipement a le statut DISPONIBLE par défaut\r\n  this.selectedEquipement.statut = 'DISPONIBLE';\r\n  this.affectationForm.patchValue({ equipement: this.selectedEquipement });\r\n  this.affectationForm.patchValue({ verrou:'affecter' });\r\n\r\n  console.log('Form Value:', this.affectationForm.value);\r\n  this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\r\n    next: (response) => {\r\n      console.log('Statut mis à jour avec succès:', response);\r\n    },\r\n    error: (error) => {\r\n      console.error('Erreur lors de la mise à jour du statut:', error);\r\n    }\r\n  });\r\n  this.authservice.addAff(this.affectationForm.value).subscribe({\r\n    next: (response: any) => {\r\n      this.showNotification('success', 'Affectation créée avec succès !');\r\n      this.closeAffectationModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n      const utilisateur = this.affectationForm.get('user')?.value;\r\n      const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\r\n      const numSerie = this.selectedEquipement?.numSerie || 'N/A';\r\n      const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\r\n      this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\r\n    },\r\n    error: (error) => {\r\n      this.showNotification('error', 'Échec de la création de l\\'affectation');\r\n    }\r\n  });\r\n}\r\n\r\nprivate handleEditAffectation() {\r\n  // Validate the edit form - only user is required\r\n  if (!this.EditedAffectation.user) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  \r\n  const affectationData = {\r\n    ...this.EditedAffectation,\r\n    dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\r\n  };\r\n\r\n  console.log('Updating affectation:', affectationData);\r\n\r\n  this.authservice.updateAff(affectationData).subscribe({\r\n    next: (response: any) => {\r\n      this.showNotification('success', 'Affectation modifiée avec succès !');\r\n      this.closeAffectationEditModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n\r\n      // Enregistrer dans l'historique manuellement\r\n      const historique = new Historique();\r\n      historique.date = new Date();\r\n\r\n      const equipementNom = this.selectedEquipement.model?.nomModel\r\n        ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})`\r\n        : 'Équipement inconnu';\r\n\r\n      const utilisateurNom = this.EditedAffectation.user\r\n        ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim()\r\n        : 'Utilisateur inconnu';\r\n\r\n      const dateFormatted = new Date().toLocaleDateString('fr-FR');\r\n      historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\r\n\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique de réaffectation enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\r\n        }\r\n      });\r\n    },\r\n    error: (error) => {\r\n      console.error('Error updating affectation:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'affectation');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n\r\n\r\n    \r\n  onRegister(): void {\r\n  this.submitted = true;\r\n\r\nconsole.log(this.form.value.model);\r\nif (this.form.invalid) {\r\n    this.form.markAllAsTouched(); // 🔥 Triggers all error messages\r\n    return;\r\n  }\r\nconst historique = new Historique();\r\n\r\n  const equipementData = {\r\n    ...this.form.value,\r\n    statut: 'DISPONIBLE', \r\n    fournisseur: this.form.value.fournisseurs || null \r\n  };\r\nconsole.log(equipementData);\r\n  this.authservice.addEquipement(equipementData).subscribe({\r\n    next: (response) => {\r\n      historique.date = new Date();\r\n      historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\r\nthis.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Équipement ajouté avec succès');\r\n      this.closeModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      alert('Échec de l’enregistrement');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n  imagePreview: string | ArrayBuffer | null = null;\r\nselectedImage: File | null = null;\r\n\r\nonImageSelected(event: Event): void {\r\n  const file = (event.target as HTMLInputElement).files?.[0];\r\n  if (file) {\r\n    this.form.patchValue({ image: file });\r\n    this.form.get('image')?.updateValueAndValidity();\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      this.imagePreview = reader.result;\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n}\r\n\r\n\r\n  \r\nonFileSelected(event: any) {\r\n  const file = event.target.files[0];\r\n\r\n  if (file) {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\r\n      (response) => {\r\n        if (response && response.imageUrl) {\r\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\r\n          console.log('Image URL saved: ', fullUrl);\r\n\r\n         \r\n          this.form.patchValue({\r\n            image: fullUrl\r\n          });\r\n        } else {\r\n          console.error('Invalid response from API');\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error during image upload', error);\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\n\r\n\r\nsignupErrors: any = {};\r\n    \r\n  resetErrors() {\r\n    this.signupErrors = {};\r\n  }\r\n\r\n      GetAllModels()\r\n    {\r\n      this.authservice.getAllModel().subscribe(data => {\r\n      this.models = data;\r\n   \r\n    });\r\n    }\r\n\r\n\r\n\r\n\r\n  openModal() {\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n    this.resetForm();\r\n  }\r\n\r\n  closeEditModal() {\r\n    this.isEditModalOpen = false;\r\n    this.editForm.reset();\r\n    this.submitted = false;\r\n  }\r\n\r\n  closeOnOutsideClickEdit(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeEditModal();\r\n    }\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n\r\n  // Méthode pour réinitialiser le formulaire\r\n  resetForm(): void {\r\n    this.form.reset();\r\n    // Réinitialiser avec le statut par défaut\r\n    this.form.patchValue({\r\n      statut: 'DISPONIBLE'\r\n    });\r\n    this.submitted = false;\r\n    this.newEquipement = {\r\n      idEqui: 0,\r\n      numSerie: \"\",\r\n      statut: \"DISPONIBLE\",\r\n      image: \"\",\r\n      model: null,\r\n      dateAffectation: new Date(),\r\n      description: \"\",\r\n      fournisseur:null,\r\n      etatActuel:null\r\n    };\r\n    this.newEquipement1 = {\r\n      idEqui: 0,\r\n      numSerie: \"\",\r\n      statut: \"DISPONIBLE\",\r\n      image: \"\",\r\n      model: null,\r\n      dateAffectation: new Date(),\r\n      description: \"\",\r\n      fournisseur:null\r\n      \r\n    };\r\n  }\r\n\r\n  // Méthodes pour l'affectation\r\n  openAffectationModal(equipement: Equip) {\r\n    this.selectedEquipement = equipement;\r\n    // Définir automatiquement le statut comme DISPONIBLE\r\n    this.selectedEquipement.statut = 'DISPONIBLE';\r\n    this.isAffectationModalOpen = true;\r\n    this.affectationFormSubmitted = false; // Reset submission state\r\n\r\n    // Réinitialiser le formulaire d'affectation\r\n    this.affectationForm.patchValue({\r\n      utilisateur: null,\r\n      equipement: this.selectedEquipement, // Définir l'équipement avec le statut DISPONIBLE\r\n      commentaire: '',\r\n      dateAffectation: new Date().toISOString().split('T')[0]\r\n    });\r\n  }\r\n\r\n\r\nopenEditedModal(equipement: Equip) {\r\n\r\n  // Set the selected equipment for the modal\r\n  this.selectedEquipement = equipement;\r\n  this.editAffectationFormSubmitted = false; // Reset submission state\r\n  \r\n    this.isAffectationEditModalOpen = true;\r\n\r\n}\r\n\r\n\r\n  closeAffectationModal() {\r\n    this.isAffectationModalOpen = false;\r\n\r\n    this.affectationForm.reset();\r\n  }\r\n\r\n\r\nupdateReaffication(equip: Equip) {\r\n  this.editAffectationFormSubmitted = true;\r\n\r\n  // Check if user is required and missing\r\n  if (!this.EditedAffectation.user) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  this.EditedAffectation.equipement = equip;\r\n  this.EditedAffectation.verrou = 'affecter';\r\n\r\n  this.authservice.updateAff(this.EditedAffectation)\r\n    .subscribe({\r\n      next: (data) => {\r\n        console.log(\"Affectation mise à jour avec succès\", data);\r\n        this.showNotification('success', 'Affectation modifiée avec succès !');\r\n        this.closeAffectationEditModal();\r\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de la mise à jour de l'affectation\", error);\r\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\r\n      }\r\n    });\r\n}\r\n\r\n\r\n  closeAffectationEditModal() {\r\n    this.isAffectationEditModalOpen = false;\r\n\r\n    this.utilisateurCtrl.setValue(null);\r\n\r\n    this.EditedAffectation = {\r\n      id: 0,\r\n      commentaire: \"\",\r\n      dateAffectation: new Date(),\r\n      user: new Utilisateur(),\r\n      equipement: new Equip()\r\n    };\r\n  }\r\n\r\n  closeOnOutsideClickAffectation(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeAffectationModal();\r\n    }\r\n  }\r\n\r\n  closeOnOutsideClickAffectationEdit(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeAffectationEditModal();\r\n    }\r\n  }\r\n\r\n  // Méthode pour formater la date pour les inputs HTML\r\n  formatDateForInput(date: any): string | null {\r\n    if (!date) return null;\r\n\r\n    try {\r\n      const dateObj = new Date(date);\r\n      if (isNaN(dateObj.getTime())) return null;\r\n\r\n      // Format YYYY-MM-DD pour les inputs de type date\r\n      return dateObj.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error('Erreur lors du formatage de la date:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Méthodes pour la pagination\r\n  goToPage(page: number): void {\r\n    if (page >= 0 && page < this.totalPages) {\r\n      this.loadEquipements(page);\r\n    }\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages - 1) {\r\n      this.loadEquipements(this.currentPage + 1);\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 0) {\r\n      this.loadEquipements(this.currentPage - 1);\r\n    }\r\n  }\r\n\r\n  // Méthode pour générer les numéros de pages\r\n  getPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\r\n\r\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\r\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\r\n\r\n    // Ajuster startPage si on est près de la fin\r\n    if (endPage - startPage < maxPagesToShow - 1) {\r\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\r\n    }\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n}\r\n\r\n  \r\n", "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\r\n  \r\n</head>\r\n\r\n<body>\r\n  <!--  Body Wrapper -->\r\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\r\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\r\n\r\n    <!--  App Topstrip -->\r\n    \r\n    <!-- Sidebar Start -->\r\n<app-layout></app-layout>\r\n\r\n    <!--  Sidebar End -->\r\n    <!--  Main wrapper -->\r\n    <div class=\"body-wrapper\">\r\n      <!--  Header Start -->\r\n      <header class=\"app-header\">\r\n\r\n      </header>\r\n      <!--  Header End -->\r\n      <div class=\"body-wrapper-inner\">\r\n        <div class=\"container-fluid\">\r\n                <div class=\"welcome-header\">\r\n  <h1>Bienvenue dans votre espace</h1>\r\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\r\n\r\n</div>\r\n<!-- Bouton pour ouvrir la modale -->\r\n\r\n<div class=\"header-container\">\r\n  <div class=\"header-text\">\r\n    <h2>Équipements</h2>\r\n    <p>Gérez les différents types d'équipements informatiques\r\n\r\n</p>\r\n  </div>\r\n<button class=\"add-user-btn\" (click)=\"openModal()\" >\r\n  <span class=\"icon\">+</span>Nouvel équipement\r\n\r\n</button>\r\n</div>\r\n\r\n<!-- Formulaire de recherche simple -->\r\n<div class=\"card mt-3 mb-4\">\r\n  <div class=\"card-body\">\r\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\r\n\r\n    <div class=\"row g-3\">\r\n      <!-- Recherche par utilisateur -->\r\n      <div class=\"col-md-6\">\r\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\r\n          <mat-label>Utilisateur</mat-label>\r\n          <input\r\n            type=\"text\"\r\n            matInput\r\n            [formControl]=\"utilisateurSearchCtrl\"\r\n            [matAutocomplete]=\"autoUserSearch\"\r\n            placeholder=\"Rechercher un utilisateur...\">\r\n\r\n          <mat-autocomplete\r\n            #autoUserSearch=\"matAutocomplete\"\r\n            [displayWith]=\"displayUtilisateur\"\r\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\r\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\r\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\r\n            </mat-option>\r\n          </mat-autocomplete>\r\n        </mat-form-field>\r\n      </div>\r\n\r\n      <!-- Recherche par statut -->\r\n<div class=\"col-md-6\">\r\n  <label class=\"form-label\">Statut</label>\r\n  <select class=\"form-select\" [(ngModel)]=\"selectedStatut\" (change)=\"loadEquipements(0)\">\r\n    <option value=\"\">Tous les statuts</option>\r\n    <option value=\"DISPONIBLE\">Disponible</option>\r\n    <option value=\"AFFECTE\">Affecté</option>\r\n    <option value=\"MAINTENANCE\">En maintenance</option>\r\n    <option value=\"HORS_SERVICE\">Hors service</option>\r\n  </select>\r\n</div>\r\n</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"search-wrapper\">\r\n  <div class=\"custom-search\">\r\n    <input\r\n      type=\"text\"\r\n      placeholder=\"Rechercher un equipement...\"\r\n      [(ngModel)]=\"searchTerm\"\r\n      (input)=\"onSearchChange()\"\r\n      class=\"form-control\"\r\n    />\r\n    <span class=\"icon-search\"></span>\r\n  </div>\r\n</div>\r\n\r\n</div>\r\n<!-- Modal -->\r\n\r\n<!-- Modal de modification -->\r\n<div class=\"modal\" [ngClass]=\"{'show': isEditModalOpen}\" (click)=\"closeOnOutsideClickEdit($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeEditModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\">Modifier l'équipement</h3>\r\n    <form [formGroup]=\"editForm\" (ngSubmit)=\"onEditSubmit()\" novalidate>\r\n      <br><br>\r\n\r\n      <!-- 🧩 Model -->\r\n      <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\r\n        <mat-label>Modèle</mat-label>\r\n        <input type=\"text\"\r\n               matInput\r\n               formControlName=\"model\"\r\n               [matAutocomplete]=\"autoModelEdit\"\r\n               placeholder=\"Rechercher un modèle...\">\r\n        <mat-autocomplete #autoModelEdit=\"matAutocomplete\"\r\n                          [displayWith]=\"displayModel\"\r\n                          (optionSelected)=\"onModelSelectedForEdit($event.option.value)\">\r\n          <mat-option *ngFor=\"let model of modelet\" [value]=\"model\">\r\n            {{ model.nomModel }}\r\n          </mat-option>\r\n        </mat-autocomplete>\r\n      </mat-form-field>\r\n\r\n      <div *ngIf=\"editForm.get('model')?.invalid && (editForm.get('model')?.touched || submitted)\" style=\"color:red\">\r\n        Le modèle est requis\r\n      </div>\r\n\r\n      <!-- 🧩 Numéro de série -->\r\n      <label for=\"numSerieEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Numéro de série</label>\r\n      <input\r\n        class=\"form-inputp\"\r\n        id=\"numSerieEdit\"\r\n        type=\"text\"\r\n        formControlName=\"numSerie\"\r\n        placeholder=\"Entrer le numéro de série\"\r\n      />\r\n      <div *ngIf=\"editForm.get('numSerie')?.invalid && (editForm.get('numSerie')?.touched || submitted)\" style=\"color:red\">\r\n        Le numéro de série est requis (min 4 caractères)\r\n      </div>\r\n\r\n      <!-- 🧩 Description -->\r\n      <label for=\"descriptionEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Description (optionnel)</label>\r\n      <input\r\n        class=\"form-inputp\"\r\n        id=\"descriptionEdit\"\r\n        type=\"text\"\r\n        formControlName=\"description\"\r\n        placeholder=\"Entrer la description (optionnel)\"\r\n      />\r\n\r\n      <!-- 🧩 Date -->\r\n      <label for=\"dateAffectationEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'acquisition</label>\r\n      <input\r\n        type=\"date\"\r\n        id=\"dateAffectationEdit\"\r\n        class=\"form-control\"\r\n        formControlName=\"dateAffectation\"\r\n      />\r\n      <div *ngIf=\"editForm.get('dateAffectation')?.invalid && (editForm.get('dateAffectation')?.touched || submitted)\" style=\"color:red\">\r\n        La date est requise\r\n      </div>\r\n\r\n      <label for=\"fournisseurEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Fournisseur</label>\r\n      <select\r\n        class=\"form-inputp\"\r\n        id=\"fournisseurEdit\"\r\n        name=\"fournisseurEdit\"\r\n        formControlName=\"fournisseurs\"\r\n        style=\"width: 100%;\"\r\n        required\r\n    >\r\n        <option [ngValue]=\"null\" disabled hidden>Sélectionner fournisseur</option>\r\n        <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\r\n          {{ fournisseur.nomFournisseur }}\r\n        </option>\r\n      </select>\r\n      <div *ngIf=\"editForm.get('fournisseurs')?.invalid && (editForm.get('fournisseurs')?.touched || submitted)\" style=\"color:red\">\r\n        Au moins un fournisseur est requis\r\n      </div>\r\n\r\n      <!-- 🧩 Image -->\r\n      <label for=\"imageEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Logo d'équipement (optionnel)</label>\r\n      <input\r\n        type=\"file\"\r\n        id=\"imageEdit\"\r\n        (change)=\"onFileSelected($event)\"\r\n        accept=\"image/*\"\r\n        class=\"form-inputp\"\r\n      />\r\n      <div *ngIf=\"imagePreview\" style=\"margin-top: 10px;\">\r\n        <img [src]=\"imagePreview\" alt=\"Aperçu\" style=\"width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;\" />\r\n      </div>\r\n\r\n      <br />\r\n      <button type=\"submit\" class=\"btn btn-primary\">\r\n        Modifier\r\n      </button>\r\n    </form>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\" >Ajouter un nouvel Equipement</h3>\r\n<form [formGroup]=\"form\" (ngSubmit)=\"onRegister()\" novalidate>\r\n  <br><br>\r\n\r\n  <!-- 🧩 Model -->\r\n<mat-form-field appearance=\"outline\" style=\"width: 100%;\">\r\n  <mat-label>Modèle</mat-label>\r\n  <input type=\"text\"\r\n         matInput\r\n         formControlName=\"model\"\r\n         [matAutocomplete]=\"autoModel\"\r\n         placeholder=\"Rechercher un modèle...\">\r\n  <mat-autocomplete #autoModel=\"matAutocomplete\"\r\n                    [displayWith]=\"displayModel\"\r\n                    (optionSelected)=\"onModelSelectedForAdd($event.option.value)\">\r\n    <mat-option *ngFor=\"let model of modelet\" [value]=\"model\">\r\n      {{ model.nomModel }}\r\n    </mat-option>\r\n  </mat-autocomplete>\r\n</mat-form-field>\r\n\r\n  <div *ngIf=\"form.get('model')?.invalid && (form.get('model')?.touched || submitted)\" style=\"color:red\">\r\n    Le modèle est requis\r\n  </div>\r\n\r\n  <!-- 🧩 Numéro de série -->\r\n  <label for=\"numSerie\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Numéro de série</label>\r\n  <input\r\n    class=\"form-inputp\"\r\n    id=\"numSerie\"\r\n    type=\"text\"\r\n    formControlName=\"numSerie\"\r\n    placeholder=\"Entrer le numéro de série\"\r\n  />\r\n  <div *ngIf=\"form.get('numSerie')?.invalid && (form.get('numSerie')?.touched || submitted)\" style=\"color:red\">\r\n    Le numéro de série est requis (min 4 caractères)\r\n  </div>\r\n\r\n  <!-- 🧩 Description -->\r\n  <label for=\"description\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Description (optionnel)</label>\r\n  <input\r\n    class=\"form-inputp\"\r\n    id=\"description\"\r\n    type=\"text\"\r\n    formControlName=\"description\"\r\n    placeholder=\"Entrer la description (optionnel)\"\r\n  />\r\n\r\n  <!-- 🧩 Date -->\r\n  <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'acquisition</label>\r\n  <input\r\n    type=\"date\"\r\n    id=\"dateAffectation\"\r\n    class=\"form-control\"\r\n    formControlName=\"dateAffectation\"\r\n  />\r\n  <div *ngIf=\"form.get('dateAffectation')?.invalid && (form.get('dateAffectation')?.touched || submitted)\" style=\"color:red\">\r\n    La date est requise\r\n  </div>\r\n   <label for=\"fournisseur\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Fournisseur</label>\r\n<select\r\n  class=\"form-inputp\"\r\n  id=\"type\"\r\n  name=\"fournisseur\"\r\n  formControlName=\"fournisseurs\"\r\n  style=\"width: 100%;\"\r\n  required\r\n\r\n>\r\n <option [ngValue]=\"null\" disabled hidden>Sélectionner fournisseur</option>\r\n  <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\r\n    {{ fournisseur.nomFournisseur }}\r\n  </option>\r\n</select>\r\n<div *ngIf=\"form.get('fournisseurs')?.invalid && (form.get('fournisseurs')?.touched || submitted)\" style=\"color:red\">\r\n  Au moins un fournisseur est requis\r\n</div>\r\n\r\n\r\n  <!-- 🧩 Image -->\r\n  <label for=\"image\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Logo d'équipement (optionnel)</label>\r\n  <input \r\n    type=\"file\" \r\n    id=\"image\" \r\n    (change)=\"onFileSelected($event)\" \r\n    accept=\"image/*\"\r\n    class=\"form-inputp\"\r\n  />\r\n  <div *ngIf=\"imagePreview\" style=\"margin-top: 10px;\">\r\n    <img [src]=\"imagePreview\" alt=\"Aperçu\" style=\"width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;\" />\r\n  </div>\r\n\r\n  <br />\r\n  <button type=\"submit\" class=\"btn btn-primary\">\r\n    Enregistrer\r\n  </button>\r\n</form>\r\n\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<!-- Modal d'affectation -->\r\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationModalOpen}\" (click)=\"closeOnOutsideClickAffectation($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeAffectationModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\r\n\r\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\r\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\r\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\r\n    </div>\r\n\r\n    <form [formGroup]=\"affectationForm\" (ngSubmit)=\"onAffectationSubmit()\">\r\n\r\n<mat-form-field appearance=\"outline\" style=\"width: 100%;\">\r\n  <mat-label>Utilisateur</mat-label>\r\n  <input\r\n    type=\"text\"\r\n    matInput\r\n    [formControl]=\"utilisateurCtrl\"\r\n    [matAutocomplete]=\"auto\"\r\n    placeholder=\"Rechercher un utilisateur...\">\r\n    \r\n  <mat-autocomplete\r\n    #auto=\"matAutocomplete\"\r\n    [displayWith]=\"displayUtilisateur\"\r\n    (optionSelected)=\"onUserSelected($event.option.value)\">\r\n    <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\r\n      {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\r\n    </mat-option>\r\n  </mat-autocomplete>\r\n</mat-form-field>\r\n\r\n\r\n<div *ngIf=\"!affectationForm.get('user')?.value && affectationFormSubmitted\"\r\n     style=\"color:red; font-size: 12px;\">\r\n  L'utilisateur est requis\r\n</div>\r\n\r\n\r\n      <!-- Commentaire -->\r\n      <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\r\n      <textarea\r\n        formControlName=\"commentaire\"\r\n        class=\"form-inputp\"\r\n        rows=\"3\"\r\n        placeholder=\"Commentaire sur l'affectation (optionnel)...\"\r\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\r\n      </textarea>\r\n\r\n      <!-- Date d'affectation -->\r\n      <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\r\n      <input\r\n        type=\"date\"\r\n        formControlName=\"dateAffectation\"\r\n        class=\"form-inputp\"\r\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\r\n\r\n      <br />\r\n      <button type=\"submit\" class=\"btn-submit\">\r\n         Affecter l'équipement\r\n      </button>\r\n    </form>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n<!-- Modal d'affectation -->\r\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationEditModalOpen}\" (click)=\"closeOnOutsideClickAffectationEdit($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeAffectationEditModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\r\n\r\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\r\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\r\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\r\n    </div>\r\n\r\n<form (ngSubmit)=\"selectedEquipement && updateReaffication(selectedEquipement)\" #editAffectationForm=\"ngForm\">\r\n\r\n  <!-- Utilisateur -->\r\n  <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\r\n    <mat-label>Utilisateur Actuel</mat-label>\r\n <input\r\n  type=\"text\"\r\n  matInput\r\n  [formControl]=\"utilisateurCtrl\"\r\n  [matAutocomplete]=\"auto\"\r\n  placeholder=\"Rechercher un utilisateur...\"\r\n  required>\r\n\r\n    <mat-autocomplete \r\n      #auto=\"matAutocomplete\" \r\n      [displayWith]=\"displayUtilisateur\"\r\n      (optionSelected)=\"onUserSelected($event.option.value)\">\r\n      <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\r\n        {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\r\n      </mat-option>\r\n    </mat-autocomplete>\r\n  </mat-form-field>\r\n\r\n  <div *ngIf=\"!EditedAffectation.user && editAffectationFormSubmitted\" style=\"color:red; font-size: 12px;\">\r\n    L'utilisateur est requis\r\n  </div>\r\n\r\n  <!-- Commentaire -->\r\n  <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\r\n  <textarea\r\n    name=\"commentaire\"\r\n    [(ngModel)]=\"EditedAffectation.commentaire\"\r\n    class=\"form-inputp\"\r\n    rows=\"3\"\r\n    placeholder=\"Commentaire sur l'affectation (optionnel)...\"\r\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\r\n  </textarea>\r\n\r\n  <!-- Date d'affectation -->\r\n  <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\r\n  <input\r\n    type=\"date\"\r\n    name=\"dateAffectation\"\r\n    [(ngModel)]=\"EditedAffectation.dateAffectation\"\r\n    class=\"form-inputp\"\r\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\r\n\r\n  <!-- Submit -->\r\n  <br />\r\n  <button type=\"submit\" class=\"btn-submit\">\r\n  Modifier Affectation\r\n  </button>\r\n</form>\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<style>\r\n    .card-custom {\r\n      border-radius: 12px;\r\n      padding: 20px;\r\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\r\n    }\r\n\r\n    .btn-outline-lightblue {\r\n      border: 1px solid #cfe2ff;\r\n      color: #0d6efd;\r\n      background-color: #e7f1ff;\r\n    }\r\n\r\n    .tag {\r\n      background-color: #e7f1ff;\r\n      color: #0d6efd;\r\n      padding: 3px 10px;\r\n      font-size: 0.8rem;\r\n      border-radius: 15px;\r\n      position: absolute;\r\n      right: 20px;\r\n      top: 20px;\r\n    }\r\n\r\n.icon-box {\r\n  font-size: 48px; /* optional - for icon size */\r\n  width: 100px;     /* increase width */\r\n  height: 100px;    /* set height */\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  color: #0d6efd;\r\n  margin-right: 10px;\r\n border-radius: 0% !important;\r\n}\r\n\r\n    .btn-icon {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n    }\r\n\r\n.card-custom {\r\n  border-radius: 12px;\r\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\r\n  background-color: #fff;\r\n  color: #212529; /* Darker text */\r\n  font-size: 0.95rem; /* Slightly larger base font */\r\n}\r\n\r\n.card-custom strong {\r\n  font-weight: 600; /* Heavier for labels */\r\n  color: #1a1a1a;\r\n}\r\n\r\n.card-custom h5 {\r\n  font-weight: 600;\r\n  color: #000;\r\n}\r\n\r\n.card-custom small,\r\n.text-muted {\r\n  color: #495057 !important; /* Less faded gray */\r\n}\r\n\r\n.icon-box {\r\n  font-size: 32px;\r\n  color: #0d6efd;\r\n  margin-right: 10px;\r\n}\r\n\r\n.tag {\r\n  background-color: #e7f1ff;\r\n  color: #0d6efd;\r\n  padding: 3px 10px;\r\n  font-size: 0.8rem;\r\n  border-radius: 15px;\r\n  position: absolute;\r\n  right: 20px;\r\n  top: 20px;\r\n}\r\n\r\n\r\n\r\n  </style>\r\n\r\n<body class=\"bg-light\">\r\n  <div class=\"container my-2\">\r\n\r\n    <!-- Simple Notification Bar -->\r\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n      {{ notification.message }}\r\n    </div>\r\n\r\n    <!-- Tableau des équipements -->\r\n    <div class=\"container-fluid\">\r\n      <div class=\"row\">\r\n        <div class=\"col-12\">\r\n          <div class=\"card\">\r\n            <div class=\"card-body\">\r\n              <div class=\"table-responsive mt-1\">\r\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\r\n                  <thead>\r\n                    <tr>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Description</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted\">Fournisseur</th>\r\n                      <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\r\n                    </tr>\r\n                  </thead>\r\n                  <tbody>\r\n                    <tr *ngFor=\"let equip of equiements\">\r\n                      <!-- Image -->\r\n                      <td class=\"px-1\">\r\n                        <img [src]=\"equip.image\"\r\n                             alt=\"Équipement\"\r\n                             class=\"rounded-circle img-fluid\"\r\n                             width=\"40\" height=\"40\" />\r\n                      </td>\r\n\r\n                      <!-- Modèle -->\r\n                      <td class=\"px-1\">\r\n                        <div class=\"ms-3\">\r\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\r\n                          <span class=\"fw-normal text-muted\">Modèle</span>\r\n                        </div>\r\n                      </td>\r\n\r\n                      <!-- Numéro de série -->\r\n                      <td class=\"px-1\">\r\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\r\n                      </td>\r\n\r\n                      <!-- Date d'acquisition -->\r\n                      <td class=\"px-1\">\r\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\r\n                      </td>\r\n\r\n                      <!-- Statut -->\r\n                      <td class=\"px-1\">\r\n                        <span class=\"badge rounded-pill\"\r\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\r\n                              [style.color]=\"'white'\">\r\n                          {{ equip.statut }}\r\n                        </span>\r\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\r\n                             class=\"text-muted small mt-1\">\r\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\r\n                        </div>\r\n                      </td>\r\n\r\n                      <!-- Description -->\r\n                      <td class=\"px-1\">\r\n                        <span class=\"fw-normal text-truncate\" style=\"max-width: 150px; display: inline-block;\"\r\n                              [title]=\"equip.description\">\r\n                          {{ equip.description }}\r\n                        </span>\r\n                      </td>\r\n\r\n                      <!-- Fournisseur -->\r\n                      <td class=\"px-1\">\r\n                        <span class=\"fw-normal\">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>\r\n                      </td>\r\n\r\n                      <!-- Actions -->\r\n                      <td class=\"px-1 text-end\">\r\n                        <div class=\"d-flex justify-content-end gap-1\">\r\n                          <!-- Bouton d'affectation conditionnel -->\r\n                          <button\r\n                            *ngIf=\"equip.statut === 'DISPONIBLE'\"\r\n                            class=\"btn btn-sm btn-outline-primary\"\r\n                            (click)=\"openAffectationModal(equip)\"\r\n                            title=\"Affecter\">\r\n                            Affecter\r\n                          </button>\r\n                          <button\r\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\r\n                            class=\"btn btn-sm btn-outline-warning\"\r\n                            (click)=\"openEditedModal(equip)\"\r\n                            title=\"Réaffecter\">\r\n                            🔄\r\n                          </button>\r\n                          <button\r\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\r\n                            class=\"btn btn-sm btn-outline-secondary\"\r\n                            (click)=\"desaffecterEquipement(equip)\"\r\n                            title=\"Désaffecter\">\r\n                            Désaffecter\r\n                          </button>\r\n                          <button\r\n                            class=\"btn btn-sm btn-outline-primary\"\r\n                            (click)=\"openModal1(equip)\"\r\n                            title=\"Modifier\">\r\n                            ✏️\r\n                          </button>\r\n                          <button\r\n                            class=\"btn btn-sm btn-outline-danger\"\r\n                            (click)=\"confirmDelete(equip.idEqui)\"\r\n                            title=\"Supprimer\">\r\n                            🗑️\r\n                          </button>\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  </tbody>\r\n                </table>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n\r\n    <!-- Pagination Bootstrap -->\r\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\r\n  <ul class=\"pagination\">\r\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\r\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\r\n    </li>\r\n\r\n    <li class=\"page-item\"\r\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\r\n        [class.active]=\"i === currentPage\">\r\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\r\n    </li>\r\n\r\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\r\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\r\n    </li>\r\n  </ul>\r\n</nav>\r\n\r\n  </div>\r\n</body>\r\n\r\n\r\n\r\n          <!--  Row 1 -->\r\n          <div class=\"row\">\r\n            \r\n          <div class=\"py-6 px-6 text-center\">\r\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\r\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\r\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\r\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\r\n  <script src=\"./assets/js/app.min.js\"></script>\r\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\r\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\r\n  <script src=\"./assets/js/dashboard.js\"></script>\r\n  <!-- solar icons -->\r\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\r\n</body>\r\n\r\n</html>"], "mappings": "AACA,SAASA,KAAK,QAAQ,SAAS;AAI/B,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,4BAA4B;AAGxD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAE7E,SAASC,UAAU,QAAQ,cAAc;;;;;;;;;;;;;;ICwD7BC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,QAAA,CAAAG,SAAA,OAAAH,QAAA,CAAAI,QAAA,SAAAJ,QAAA,CAAAK,KAAA,MACF;;;;;IAuDFV,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAO,SAAA,CAAe;IACvDX,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAD,SAAA,CAAAE,QAAA,MACF;;;;;IAIJb,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAE,MAAA,wEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAI,UAAA,YAAAU,eAAA,CAAuB;IACtEd,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAE,eAAA,CAAAC,cAAA,MACF;;;;;IAEFf,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAgB,SAAA,eAAyH;IAC3HhB,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,QAAAa,MAAA,CAAAC,YAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAoB;;;;;IAgC7BnB,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAgB,SAAA,CAAe;IACvDpB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,SAAA,CAAAP,QAAA,MACF;;;;;IAIFb,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAA6G;IAC3GD,EAAA,CAAAE,MAAA,wEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYNH,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAI,UAAA,YAAAiB,eAAA,CAAuB;IACtErB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAS,eAAA,CAAAN,cAAA,MACF;;;;;IAEFf,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAgB,SAAA,eAAyH;IAC3HhB,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,QAAAkB,OAAA,CAAAJ,YAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAoB;;;;;IAsCzBnB,EAAA,CAAAC,cAAA,eAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,aAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAuB,iBAAA,CAAAC,OAAA,CAAAC,kBAAA,CAAAC,KAAA,kBAAAF,OAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAb,QAAA,CAAwC;IAChBb,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAY,kBAAA,yBAAAY,OAAA,CAAAC,kBAAA,CAAAE,QAAA,KAA2C;;;;;IAkBzG3B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAwB,QAAA,CAAc;IAClE5B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAApB,SAAA,OAAAoB,QAAA,CAAAnB,QAAA,SAAAmB,QAAA,CAAAlB,KAAA,MACF;;;;;IAKJV,EAAA,CAAAC,cAAA,eACyC;IACvCD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwCFH,EAAA,CAAAC,cAAA,eAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,aAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAuB,iBAAA,CAAAM,OAAA,CAAAJ,kBAAA,CAAAC,KAAA,kBAAAG,OAAA,CAAAJ,kBAAA,CAAAC,KAAA,CAAAb,QAAA,CAAwC;IAChBb,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAY,kBAAA,yBAAAiB,OAAA,CAAAJ,kBAAA,CAAAE,QAAA,KAA2C;;;;;IAoBvG3B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAA0B,QAAA,CAAc;IAClE9B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuB,QAAA,CAAAtB,SAAA,OAAAsB,QAAA,CAAArB,QAAA,SAAAqB,QAAA,CAAApB,KAAA,MACF;;;;;IAIJV,EAAA,CAAAC,cAAA,eAAyG;IACvGD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgIJH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAA2B,OAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFjC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAmB,OAAA,CAAAC,YAAA,CAAAE,OAAA,MACF;;;;;IAyDoBlC,EAAA,CAAAC,cAAA,eACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,0BAAAZ,EAAA,CAAAmC,WAAA,OAAAC,OAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,MAAA,QACF;;;;;;IAoBEvC,EAAA,CAAAC,cAAA,kBAImB;IADjBD,EAAA,CAAAwC,UAAA,mBAAAC,sEAAA;MAAAzC,EAAA,CAAA0C,aAAA,CAAAC,IAAA;MAAA,MAAAL,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAD,OAAA,CAAAE,oBAAA,CAAAV,SAAA,CAA2B;IAAA,EAAC;IAErCtC,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBAIqB;IADnBD,EAAA,CAAAwC,UAAA,mBAAAS,sEAAA;MAAAjD,EAAA,CAAA0C,aAAA,CAAAQ,IAAA;MAAA,MAAAZ,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAM,OAAA,GAAAnD,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAI,OAAA,CAAAC,eAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IAEhCtC,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBAIsB;IADpBD,EAAA,CAAAwC,UAAA,mBAAAa,sEAAA;MAAArD,EAAA,CAAA0C,aAAA,CAAAY,IAAA;MAAA,MAAAhB,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAU,OAAA,GAAAvD,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAQ,OAAA,CAAAC,qBAAA,CAAAlB,SAAA,CAA4B;IAAA,EAAC;IAEtCtC,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7EfH,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAAgB,SAAA,eAG8B;IAChChB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,eAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,eAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAyD,UAAA,KAAAC,0CAAA,mBAGM;IACR1D,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,eAAiB;IAGbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAAiB;IACSD,EAAA,CAAAE,MAAA,IAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/FH,EAAA,CAAAC,cAAA,eAA0B;IAGtBD,EAAA,CAAAyD,UAAA,KAAAE,6CAAA,sBAMS;IACT3D,EAAA,CAAAyD,UAAA,KAAAG,6CAAA,sBAMS;IACT5D,EAAA,CAAAyD,UAAA,KAAAI,6CAAA,sBAMS;IACT7D,EAAA,CAAAC,cAAA,mBAGmB;IADjBD,EAAA,CAAAwC,UAAA,mBAAAsB,6DAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAsB,IAAA;MAAA,MAAA1B,SAAA,GAAAyB,WAAA,CAAAlB,SAAA;MAAA,MAAAoB,OAAA,GAAAjE,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAkB,OAAA,CAAAC,UAAA,CAAA5B,SAAA,CAAiB;IAAA,EAAC;IAE3BtC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAGoB;IADlBD,EAAA,CAAAwC,UAAA,mBAAA2B,6DAAA;MAAA,MAAAJ,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAsB,IAAA;MAAA,MAAA1B,SAAA,GAAAyB,WAAA,CAAAlB,SAAA;MAAA,MAAAuB,OAAA,GAAApE,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAA/B,SAAA,CAAAC,MAAA,CAA2B;IAAA,EAAC;IAErCvC,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAtFNH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAkC,SAAA,CAAAgC,KAAA,EAAAtE,EAAA,CAAAmB,aAAA,CAAmB;IASYnB,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAuB,iBAAA,CAAAe,SAAA,CAAAZ,KAAA,kBAAAY,SAAA,CAAAZ,KAAA,CAAAb,QAAA,CAA2B;IAOvCb,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAuB,iBAAA,CAAAe,SAAA,CAAAX,QAAA,CAAoB;IAKpB3B,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAuE,WAAA,SAAAjC,SAAA,CAAAkC,eAAA,gBAAgD;IAMlExE,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAAyE,WAAA,qBAAAnC,SAAA,CAAAoC,MAAA,gCAAApC,SAAA,CAAAoC,MAAA,uCAA2H;IAE/H1E,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAA0B,SAAA,CAAAoC,MAAA,MACF;IACM1E,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,CAAAC,WAAA,oBAAArC,SAAA,CAAAoC,MAAA,CAAAC,WAAA,sBAA0F;IAS1F3E,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAkC,SAAA,CAAAsC,WAAA,CAA2B;IAC/B5E,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAA0B,SAAA,CAAAsC,WAAA,MACF;IAKwB5E,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAuB,iBAAA,EAAAe,SAAA,CAAAuC,WAAA,kBAAAvC,SAAA,CAAAuC,WAAA,CAAA9D,cAAA,yBAA8D;IAQjFf,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,kBAAmC;IAOnC1E,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,eAAgC;IAOhC1E,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,eAAgC;;;;;;IAsCzD1E,EAAA,CAAAC,cAAA,cAEuC;IAChBD,EAAA,CAAAwC,UAAA,mBAAAsC,6DAAA;MAAA,MAAAf,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAqC,IAAA;MAAA,MAAAC,KAAA,GAAAjB,WAAA,CAAAkB,KAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAmC,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAAoF,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACctF,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAuB,iBAAA,CAAAyD,KAAA,KAAW;;;;;;;;;IATnEhF,EAAA,CAAAC,cAAA,eAAuE;IAG5CD,EAAA,CAAAwC,UAAA,mBAAA+C,wDAAA;MAAAvF,EAAA,CAAA0C,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAA0C,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACtF,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAyD,UAAA,IAAAiC,yCAAA,kBAIK;IAEL1F,EAAA,CAAAC,cAAA,cAAwE;IACjDD,EAAA,CAAAwC,UAAA,mBAAAmD,wDAAA;MAAA3F,EAAA,CAAA0C,aAAA,CAAA8C,IAAA;MAAA,MAAAI,OAAA,GAAA5F,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAA6C,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACtF,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAoF,WAAA,aAAAS,OAAA,CAAAP,WAAA,OAAoC;IAKrCtF,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8F,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,OAAA,CAAAI,UAAA,EAA+B;IAK9BjG,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAoF,WAAA,aAAAS,OAAA,CAAAP,WAAA,KAAAO,OAAA,CAAAI,UAAA,KAAiD;;;;;;;;ADrrB3E,OAAM,MAAOC,mBAAmB;EA2EhCF,YACUG,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA9E5B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAAlF,YAAY,GAAG;MACbmF,IAAI,EAAE,KAAK;MACXlF,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAoD,WAAW,GAAG,CAAC;IACf,KAAA8B,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBjF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACX+C,MAAM,EAAC,EAAE;MACTJ,KAAK,EAAC,EAAE;MACR5C,KAAK,EAAC,IAAI;MACV8C,eAAe,EAAC,IAAIiD,IAAI,CAAJ,CAAI;MACxB7C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChB6C,UAAU,EAAC;KAEV;IACD,KAAAC,cAAc,GAAO;MACrBpF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACX+C,MAAM,EAAC,EAAE;MACTJ,KAAK,EAAC,EAAE;MACR5C,KAAK,EAAC,IAAI;MACV8C,eAAe,EAAC,IAAIiD,IAAI,CAAJ,CAAI;MACxB7C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChB6C,UAAU,EAAC;KAEV;IAID,KAAAE,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdtD,eAAe,EAAC,IAAIiD,IAAI,EAAE;MAC1BM,IAAI,EAAC,IAAItI,WAAW,EAAE;MACtBuI,UAAU,EAAC,IAAI1I,KAAK,EAAE;MACtB2I,MAAM,EAAC;KAER;IACD,KAAA5F,eAAe,GAAU,EAAE;IAC3B,KAAA6F,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAI/I,WAAW,EAAE;IACnC,KAAAgJ,qBAAqB,GAAG,IAAIhJ,WAAW,EAAE;IACzC,KAAAiJ,SAAS,GAAG,IAAIjJ,WAAW,EAAE;IAsV/B,KAAAkJ,cAAc,GAAW,EAAE,CAAC,CAAC;IA+U3B,KAAAvH,YAAY,GAAgC,IAAI;IAClD,KAAAwH,aAAa,GAAgB,IAAI;IAgDjC,KAAAC,YAAY,GAAQ,EAAE;EAhtBlB;EACFC,QAAQA,CAAA;IACJ,IAAI,CAACtD,WAAW,GAAG,CAAC;IAEtB,IAAI,CAACuD,YAAY,EAAE;IACnB,IAAI,CAAC1D,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAACwD,cAAc,EAAE;IAGzB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC1C,EAAE,CAAC2C,KAAK,CAAC;MACxBtH,KAAK,EAAE,CAAC,IAAI,EAAElC,UAAU,CAACyJ,QAAQ,CAAC;MAClCtH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACyJ,QAAQ,EAAEzJ,UAAU,CAAC0J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DtE,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBJ,eAAe,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACyJ,QAAQ,CAAC;MAC1CvE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBJ,KAAK,EAAE,CAAC,IAAI,CAAC;MACb+D,YAAY,EAAE,CAAC,IAAI,EAAE7I,UAAU,CAACyJ,QAAQ;KACzC,CAAC;IAEF;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAAC9C,EAAE,CAAC2C,KAAK,CAAC;MAC5BtH,KAAK,EAAE,CAAC,IAAI,EAAElC,UAAU,CAACyJ,QAAQ,CAAC;MAClCtH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACyJ,QAAQ,EAAEzJ,UAAU,CAAC0J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DtE,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBJ,eAAe,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACyJ,QAAQ,CAAC;MAC1CvE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBJ,KAAK,EAAE,CAAC,IAAI,CAAC;MACb+D,YAAY,EAAE,CAAC,IAAI,EAAE7I,UAAU,CAACyJ,QAAQ;KACzC,CAAC;IAKF,IAAI,CAACG,eAAe,GAAG,IAAI,CAAC/C,EAAE,CAAC2C,KAAK,CAAC;MACnCjB,IAAI,EAAE,CAAC,IAAI,EAAEvI,UAAU,CAACyJ,QAAQ,CAAC;MACjCjB,UAAU,EAAE,CAAC,IAAI,CAAC;MAClBF,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBtD,eAAe,EAAE,CAAC,IAAIiD,IAAI,EAAE,CAAC;MAC7BQ,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;IAIF;IACA,IAAI,CAACO,SAAS,CAACa,YAAY,CACxBC,IAAI,CACH5J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAAC0J,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACtD,WAAW,CAACuD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO5J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA+J,SAAS,CAAChD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACoC,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACjCC,IAAI,CACH5J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAAC0J,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACtD,WAAW,CAACuD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO5J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA+J,SAAS,CAAChD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACwC,QAAQ,CAACS,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACrCC,IAAI,CACH5J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAAC0J,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACtD,WAAW,CAACuD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO5J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA+J,SAAS,CAAChD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAGJ,IAAI,CAAC2B,eAAe,CAACe,YAAY,CAC9BC,IAAI,CACH5J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAAC0J,KAAK,IAAG;MAEhB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACtD,WAAW,CAAC0D,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UAEL,OAAO5J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA+J,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAChD,oBAAoB,GAAGgD,KAAK;IACnC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvB,qBAAqB,CAACc,YAAY,CACpCC,IAAI,CACH5J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEyJ,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACjB,qBAAqB,CAACwB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAAC7E,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFtF,SAAS,CAAC0J,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACtD,WAAW,CAAC8D,iBAAiB,CAAC,IAAI,CAAC5C,UAAU,EAAC,IAAI,CAACkB,qBAAqB,CAACgB,KAAK,EAAC,CAAC,EAAC,IAAI,CAACnC,QAAQ,CAAC,CAACuC,SAAS,CAAC;YAC7GO,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;cAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;cAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;YACzC,CAAC;YACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAACpF,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACgB,WAAW,CAAC0D,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAO5J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA+J,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAC/C,0BAA0B,GAAG+C,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAW,kBAAkBA,CAAC1C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAACvH,SAAS,IAAIuH,IAAI,CAACtH,QAAQ,MAAMsH,IAAI,CAACrH,KAAK,EAAE,GAAG,EAAE;EACzE;EAGAgK,YAAYA,CAAChJ,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACb,QAAQ,EAAE,GAAG,EAAE;EACzC;EAEA8J,eAAeA,CAACjJ,KAAU;IACxB,IAAI,CAACiG,cAAc,CAACjG,KAAK,GAAGA,KAAK;EACnC;EAEAkJ,qBAAqBA,CAAClJ,KAAU;IAC9B,IAAI,CAACqH,IAAI,CAAC8B,UAAU,CAAC;MAAEnJ,KAAK,EAAEA;IAAK,CAAE,CAAC;EACxC;EAEAoJ,sBAAsBA,CAACpJ,KAAU;IAC/B,IAAI,CAACyH,QAAQ,CAAC0B,UAAU,CAAC;MAAEnJ,KAAK,EAAEA;IAAK,CAAE,CAAC;EAC5C;EAICoH,cAAcA,CAAA;IAGb,IAAI,CAAC3C,WAAW,CAAC4E,iBAAiB,EAAE,CAACpB,SAAS,CAACqB,IAAI,IAAG;MACtD,IAAI,CAAC3C,YAAY,GAAG2C,IAAI;IAE1B,CAAC,CAAC;EAGA;EAEAC,kBAAkBA,CAAC1B,KAAa;IAEhC,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,IAAI,CAAC5B,cAAc,CAACjG,KAAK,GAAG,IAAI;;EAEpC;EASEwJ,cAAcA,CAACnD,IAAiB;IAE9B,IAAI,IAAI,CAACtB,sBAAsB,EAAE;MAE/B,IAAI,CAAC2C,eAAe,CAACyB,UAAU,CAAC;QAC9B9C,IAAI,EAAEA;OACP,CAAC;KACH,MAAM,IAAI,IAAI,CAACrB,0BAA0B,EAAE;MAE1C,IAAI,CAACkB,iBAAiB,CAACG,IAAI,GAAGA,IAAI;;IAEpCyC,OAAO,CAACW,GAAG,CAAC,0BAA0B,EAAEpD,IAAI,CAAC;EAC/C;EAEAqD,oBAAoBA,CAACrD,IAAiB;IACpC,IAAI,CAAC5C,eAAe,CAAC,CAAC,CAAC;EAEzB;EAEAkG,mBAAmBA,CAACC,KAAiB;IACnC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACC,UAAU,EAAE;;EAErB;EAEFxH,UAAUA,CAAC8D,UAAiB;IAC1B,MAAM2D,YAAY,GAAG,IAAI,CAAChF,MAAM,CAACiF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK9D,UAAU,CAACtG,KAAK,EAAEoK,OAAO,CAAC;IAEnF;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAC1D,YAAY,CAACuD,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKjE,UAAU,CAACnD,WAAW,EAAEoH,aAAa,CAAC;IAEjH,IAAI,CAACtE,cAAc,GAAG;MACpB,GAAGK,UAAU;MACbtG,KAAK,EAAEiK,YAAY,IAAI;KACxB;IAED;IACA,IAAI,CAACxC,QAAQ,CAAC0B,UAAU,CAAC;MACvBnJ,KAAK,EAAE,IAAI,CAACiG,cAAc,CAACjG,KAAK;MAChCC,QAAQ,EAAE,IAAI,CAACgG,cAAc,CAAChG,QAAQ;MACtCiD,WAAW,EAAE,IAAI,CAAC+C,cAAc,CAAC/C,WAAW;MAC5CJ,eAAe,EAAE,IAAI,CAAC0H,kBAAkB,CAAC,IAAI,CAACvE,cAAc,CAACnD,eAAe,CAAC;MAC7EE,MAAM,EAAE,IAAI,CAACiD,cAAc,CAACjD,MAAM;MAClCJ,KAAK,EAAE,IAAI;MACX+D,YAAY,EAAE0D,kBAAkB,IAAI;KACrC,CAAC;IAEFvB,OAAO,CAACW,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACxD,cAAc,CAAC;IACvD6C,OAAO,CAACW,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxD,cAAc,CAAC9C,WAAW,CAAC;IACrE2F,OAAO,CAACW,GAAG,CAAC,mCAAmC,EAAEY,kBAAkB,CAAC;IACpEvB,OAAO,CAACW,GAAG,CAAC,OAAO,EAAE,IAAI,CAACxD,cAAc,CAACnD,eAAe,CAAC;IAEzD,IAAI,CAACgE,SAAS,CAACuB,QAAQ,CAAC,IAAI,CAACpC,cAAc,CAACjG,KAAK,CAAC;IAElD;IACA,IAAI,CAAC8E,eAAe,GAAG,IAAI;EAC7B;EAEA2F,YAAYA,CAAA;IACV,IAAI,CAAC/D,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACe,QAAQ,CAACiD,OAAO,EAAE;MACzB,IAAI,CAACjD,QAAQ,CAACkD,gBAAgB,EAAE;MAChC;;IAGF,MAAMC,cAAc,GAAG;MACrB,GAAG,IAAI,CAACnD,QAAQ,CAACI,KAAK;MACtBhH,MAAM,EAAE,IAAI,CAACoF,cAAc,CAACpF,MAAM;MAClCmC,MAAM,EAAE,IAAI,CAACiD,cAAc,CAACjD,MAAM;MAClCG,WAAW,EAAE,IAAI,CAACsE,QAAQ,CAACI,KAAK,CAAClB,YAAY,IAAI,IAAI,CAAC;KACvD;;IAED,IAAI,CAAClC,WAAW,CAACoG,WAAW,CAACD,cAAc,CAAC,CAAC3C,SAAS,CAAC;MACrDO,IAAI,EAAGsC,QAAQ,IAAI;QACjBhC,OAAO,CAACW,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;QAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;QAClE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACvH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAMqH,UAAU,GAAG,IAAI5M,UAAU,EAAE;QACnC4M,UAAU,CAACC,IAAI,GAAG,IAAInF,IAAI,EAAE;QAC5BkF,UAAU,CAAC7E,WAAW,GAAG,iCAAiCwE,cAAc,CAAC5K,KAAK,EAAEb,QAAQ,eAAeyL,cAAc,CAAC3K,QAAQ,GAAG;QACjI,IAAI,CAACwE,WAAW,CAAC0G,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UACnDO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;UACjD,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAK,UAAUA,CAAA;IACRtC,OAAO,CAACW,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACxD,cAAc,CAAC;IACnD,IAAI,CAACxB,WAAW,CAACoG,WAAW,CAAC,IAAI,CAAC5E,cAAc,CAAC,CAACgC,SAAS,CACxD6C,QAAQ,IAAI;MACXhC,OAAO,CAACW,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;MAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;MAClE,IAAI,CAACf,UAAU,EAAE;MACjB,IAAI,CAACvG,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MACxC;IAEF,CAAC,EACAgF,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;IAC7E,CAAC,CACF;EACH;EAGAtH,eAAeA,CAAC4H,IAAY;IAC1B,IAAI,CAACzH,WAAW,GAAGyH,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAAC3F,UAAU,CAACmC,IAAI,EAAE;IACtC,MAAM9E,MAAM,GAAG,IAAI,CAAC+D,cAAc,CAACe,IAAI,EAAE;IAEzC,IAAIyD,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAAC3E,qBAAqB,CAACgB,KAAK;IAEhD,IAAI,OAAO2D,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAAC1D,IAAI,EAAE;KAC1B,MAAM,IAAI0D,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACzD,IAAI,EAAE;;IAGpC;IACA,IAAIyD,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC9G,WAAW,CAAC8D,iBAAiB,CAAC+C,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAAC3F,QAAQ,CAAC,CAACuC,SAAS,CAAC;QACzFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;UAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;QACzC,CAAC;QACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAItI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAACgH,kBAAkB,CAAC,EAAE,EAAEzI,MAAM,EAAEqI,IAAI,EAAE,IAAI,CAAC3F,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;UAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;QACzC,CAAC;QACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAItI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAAC8D,iBAAiB,CAAC+C,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC3F,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;UAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;QACzC,CAAC;QACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAItI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAACgH,kBAAkB,CAACH,OAAO,EAAEtI,MAAM,EAAEqI,IAAI,EAAE,IAAI,CAAC3F,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAClFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;UAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;QACzC,CAAC;QACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAACpE,WAAW,CAACiH,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAAC3F,QAAQ,CAAC,CAACuC,SAAS,CAAC;MAChEO,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACvD,UAAU,GAAGuD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACnE,UAAU,GAAGkE,GAAG,CAAClE,UAAU;QAChC,IAAI,CAACoE,iBAAiB,CAAC,IAAI,CAACzD,UAAU,CAAC;MACzC,CAAC;MACD0D,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACzD,UAAiB;IACzC4D,OAAO,CAACW,GAAG,CAACvE,UAAU,CAAC;IACvBA,UAAU,CAACyG,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAACpF,OAAO,CAACoF,EAAE,CAAC/K,MAAM,CAAC,GAAC+K,EAAE,CAAC/K,MAAM;IAChC,CAAC,CAAC;IACFiI,OAAO,CAACW,GAAG,CAAC,IAAI,CAACjD,OAAO,CAAC;IAC1B,IAAI,CAAC/B,WAAW,CAACoH,oBAAoB,CAAC,IAAI,CAACrF,OAAO,CAAC,CAACyB,SAAS,CAACqB,IAAI,IAAG;MAEnEA,IAAI,CAACqC,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAACrF,gBAAgB,CAACqF,WAAW,CAACxF,UAAU,CAACzF,MAAM,CAAC,GAAGiL,WAAW;QAClE,IAAI,CAACnL,eAAe,CAACmL,WAAW,CAACxF,UAAU,CAACzF,MAAM,CAAC,GAAGiL,WAAW,CAACzF,IAAI,CAACvH,SAAS,GAAG,GAAG,GAAGgN,WAAW,CAACzF,IAAI,CAACtH,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGEgN,cAAcA,CAAA;IAEZ,IAAI,CAACtI,eAAe,CAAC,CAAC,CAAC;EACzB;EAGCuI,WAAWA,CAAC7F,EAAU;IACrB,IAAI,CAAC1B,WAAW,CAACuH,WAAW,CAAC7F,EAAE,CAAC,CAAC8B,SAAS,CAAC,MAAK;MAC9C,IAAI,CAAC8C,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;MACnE,IAAI,CAACtH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IAGxC,CAAC,CAAC;EACJ;EAKQqI,qBAAqBA,CAACC,QAAgB,EAAEC,YAAoB;IAClE,IAAI,CAAC1H,WAAW,CAAC2H,kBAAkB,CAACD,YAAY,CAAC,CAAClE,SAAS,CAACqB,IAAI,IAAG;MACnE,IAAI,CAAC/D,OAAO,GAAG+D,IAAI,CAAChD,UAAU,CAACtG,KAAK,EAAEb,QAAQ,GAC9C,GAAGmK,IAAI,CAAChD,UAAU,CAACtG,KAAK,CAACb,QAAQ,cAAcmK,IAAI,CAAChD,UAAU,CAACrG,QAAQ,GAAG,GAC1E,IAAI;MAEF,IAAI,CAACuF,OAAO,GAAG8D,IAAI,CAACjD,IAAI,CAACvH,SAAS,GAAG,GAAG,GAAGwK,IAAI,CAACjD,IAAI,CAACtH,QAAQ;MAE7D,MAAMkM,UAAU,GAAG,IAAI5M,UAAU,EAAE;MACnC4M,UAAU,CAACC,IAAI,GAAG5B,IAAI,CAACxG,eAAe;MACtC,MAAMuJ,aAAa,GAAG/C,IAAI,CAACxG,eAAe,GAAG,IAAIiD,IAAI,CAACuD,IAAI,CAACxG,eAAe,CAAC,CAACwJ,kBAAkB,CAAC,OAAO,CAAC,GAAG,IAAIvG,IAAI,EAAE,CAACuG,kBAAkB,CAAC,OAAO,CAAC;MAChJrB,UAAU,CAAC7E,WAAW,GAAG,GAAG,IAAI,CAACb,OAAO,IAAI2G,QAAQ,IAAI,IAAI,CAAC1G,OAAO,OAAO6G,aAAa,EAAE;MAE1F,IAAI,CAAC5H,WAAW,CAAC0G,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;QACnDO,IAAI,EAAGsC,QAAQ,IAAI;UACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;QACjD,CAAC;QACDlC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;QAC5E;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA9G,qBAAqBA,CAACyK,KAAY;IAChC,MAAMC,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,uDAAuD,CAAC;IAC3F,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAACP,qBAAqB,CAAC,sBAAsB,EAAEM,KAAK,CAAC1L,MAAM,CAAC;MAEhE,IAAI,CAAC4D,WAAW,CAACkI,SAAS,CAAC,IAAI,CAAClG,gBAAgB,CAAC8F,KAAK,CAAC1L,MAAM,CAAC,CAACsF,EAAE,CAAC,CAAC8B,SAAS,CAAC;QAC3EO,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC/D,WAAW,CAACmI,mBAAmB,CAACL,KAAK,CAAC1L,MAAM,CAAC,CAACoH,SAAS,CAAC;YAC3DO,IAAI,EAAEA,CAAA,KAAK;cACT,IAAI,CAACuC,gBAAgB,CAAC,SAAS,EAAE,mCAAmC,CAAC;cACrE,IAAI,CAACtH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;cACtC6I,MAAM,CAACI,QAAQ,CAAC;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAQ,CAAE,CAAC;YACjD,CAAC;YACDnE,KAAK,EAAGA,KAAK,IAAI;cACfE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;cAC5D,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,qCAAqC,CAAC;YACvE;WACD,CAAC;QACJ,CAAC;QACDnC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,CAAC;QAC9D;OACD,CAAC;;EAEN;EAGCpI,aAAaA,CAACqK,OAAe;IAC5BlE,OAAO,CAACW,GAAG,CAACuD,OAAO,CAAC;IACpB,MAAMR,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC;IAChF,IAAIF,WAAW,EAAE;MACf,IAAI,CAACR,WAAW,CAACgB,OAAO,CAAC;;EAE7B;EACFC,mBAAmBA,CAAA;IAEjB,IAAI,IAAI,CAAClI,sBAAsB,EAAE;MAE/B,IAAI,CAACmI,oBAAoB,EAAE;KAC5B,MAAM,IAAI,IAAI,CAAClI,0BAA0B,EAAE;MAE1C,IAAI,CAACmI,qBAAqB,EAAE;;EAEhC;EAEQD,oBAAoBA,CAAA;IAC1B,IAAI,CAACtH,wBAAwB,GAAG,IAAI;IAEpC,IAAI,CAAC,IAAI,CAAC8B,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK,EAAE;MAC5CiB,OAAO,CAACW,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAAC,IAAI,CAAC1J,kBAAkB,EAAE;MAC5B+I,OAAO,CAACF,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAI,CAAC7I,kBAAkB,CAACiD,MAAM,GAAG,YAAY;IAC7C,IAAI,CAAC0E,eAAe,CAACyB,UAAU,CAAC;MAAE7C,UAAU,EAAE,IAAI,CAACvG;IAAkB,CAAE,CAAC;IACxE,IAAI,CAAC2H,eAAe,CAACyB,UAAU,CAAC;MAAE5C,MAAM,EAAC;IAAU,CAAE,CAAC;IAEtDuC,OAAO,CAACW,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC/B,eAAe,CAACG,KAAK,CAAC;IACtD,IAAI,CAACpD,WAAW,CAAC2I,gBAAgB,CAAC,IAAI,CAACrN,kBAAkB,CAACc,MAAM,CAAC,CAACoH,SAAS,CAAC;MAC1EO,IAAI,EAAGsC,QAAQ,IAAI;QACjBhC,OAAO,CAACW,GAAG,CAAC,gCAAgC,EAAEqB,QAAQ,CAAC;MACzD,CAAC;MACDlC,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;IACF,IAAI,CAACnE,WAAW,CAAC4I,MAAM,CAAC,IAAI,CAAC3F,eAAe,CAACG,KAAK,CAAC,CAACI,SAAS,CAAC;MAC5DO,IAAI,EAAGsC,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;QACnE,IAAI,CAACuC,qBAAqB,EAAE;QAC5B,IAAI,CAAC7J,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAM2J,WAAW,GAAG,IAAI,CAAC7F,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK;QAC3D,MAAM2F,aAAa,GAAG,IAAI,CAACzN,kBAAkB,EAAEC,KAAK,EAAEb,QAAQ,IAAI,oBAAoB;QACtF,MAAMc,QAAQ,GAAG,IAAI,CAACF,kBAAkB,EAAEE,QAAQ,IAAI,KAAK;QAC3D,MAAMwN,cAAc,GAAGF,WAAW,GAAG,GAAGA,WAAW,CAACzO,SAAS,IAAI,EAAE,IAAIyO,WAAW,CAACxO,QAAQ,IAAI,EAAE,EAAE,CAAC+I,IAAI,EAAE,GAAG,qBAAqB;QAClI,IAAI,CAACmE,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAAClM,kBAAkB,CAACc,MAAM,CAAC;MAC9E,CAAC;MACD+H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,wCAAwC,CAAC;MAC1E;KACD,CAAC;EACJ;EAEQoC,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC,IAAI,CAACjH,iBAAiB,CAACG,IAAI,EAAE;MAChCyC,OAAO,CAACW,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAIF,MAAMiE,eAAe,GAAG;MACtB,GAAG,IAAI,CAACxH,iBAAiB;MACzBpD,eAAe,EAAE,IAAI,CAACoD,iBAAiB,CAACpD,eAAe,GAAG,IAAIiD,IAAI,CAAC,IAAI,CAACG,iBAAiB,CAACpD,eAAe,CAAC,GAAG,IAAIiD,IAAI;KACtH;IAED+C,OAAO,CAACW,GAAG,CAAC,uBAAuB,EAAEiE,eAAe,CAAC;IAErD,IAAI,CAACjJ,WAAW,CAACkJ,SAAS,CAACD,eAAe,CAAC,CAACzF,SAAS,CAAC;MACpDO,IAAI,EAAGsC,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAAC6C,yBAAyB,EAAE;QAChC,IAAI,CAACnK,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QAExC;QACA,MAAMqH,UAAU,GAAG,IAAI5M,UAAU,EAAE;QACnC4M,UAAU,CAACC,IAAI,GAAG,IAAInF,IAAI,EAAE;QAE5B,MAAMyH,aAAa,GAAG,IAAI,CAACzN,kBAAkB,CAACC,KAAK,EAAEb,QAAQ,GACzD,GAAG,IAAI,CAACY,kBAAkB,CAACC,KAAK,CAACb,QAAQ,eAAe,IAAI,CAACY,kBAAkB,CAACE,QAAQ,GAAG,GAC3F,oBAAoB;QAExB,MAAMwN,cAAc,GAAG,IAAI,CAACvH,iBAAiB,CAACG,IAAI,GAC9C,GAAG,IAAI,CAACH,iBAAiB,CAACG,IAAI,CAACvH,SAAS,IAAI,EAAE,IAAI,IAAI,CAACoH,iBAAiB,CAACG,IAAI,CAACtH,QAAQ,IAAI,EAAE,EAAE,CAAC+I,IAAI,EAAE,GACrG,qBAAqB;QAEzB,MAAMuE,aAAa,GAAG,IAAItG,IAAI,EAAE,CAACuG,kBAAkB,CAAC,OAAO,CAAC;QAC5DrB,UAAU,CAAC7E,WAAW,GAAG,GAAGoH,aAAa,sBAAsBC,cAAc,OAAOpB,aAAa,EAAE;QAEnG,IAAI,CAAC5H,WAAW,CAAC0G,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UACnDO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,yCAAyC,EAAEqB,QAAQ,CAAC;UAClE,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,qEAAqE,EAAEA,KAAK,CAAC;UAC7F;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,4CAA4C,CAAC;MAC9E;KACD,CAAC;EACJ;EAME8C,UAAUA,CAAA;IACV,IAAI,CAACnH,SAAS,GAAG,IAAI;IAEvBoC,OAAO,CAACW,GAAG,CAAC,IAAI,CAACpC,IAAI,CAACQ,KAAK,CAAC7H,KAAK,CAAC;IAClC,IAAI,IAAI,CAACqH,IAAI,CAACqD,OAAO,EAAE;MACnB,IAAI,CAACrD,IAAI,CAACsD,gBAAgB,EAAE,CAAC,CAAC;MAC9B;;IAEJ,MAAMM,UAAU,GAAG,IAAI5M,UAAU,EAAE;IAEjC,MAAMuM,cAAc,GAAG;MACrB,GAAG,IAAI,CAACvD,IAAI,CAACQ,KAAK;MAClB7E,MAAM,EAAE,YAAY;MACpBG,WAAW,EAAE,IAAI,CAACkE,IAAI,CAACQ,KAAK,CAAClB,YAAY,IAAI;KAC9C;IACHmC,OAAO,CAACW,GAAG,CAACmB,cAAc,CAAC;IACzB,IAAI,CAACnG,WAAW,CAACqJ,aAAa,CAAClD,cAAc,CAAC,CAAC3C,SAAS,CAAC;MACvDO,IAAI,EAAGsC,QAAQ,IAAI;QACjBG,UAAU,CAACC,IAAI,GAAG,IAAInF,IAAI,EAAE;QAC5BkF,UAAU,CAAC7E,WAAW,GAAG,iCAAiCwE,cAAc,CAAC5K,KAAK,EAAEb,QAAQ,eAAeyL,cAAc,CAAC3K,QAAQ,GAAG;QACvI,IAAI,CAACwE,WAAW,CAAC0G,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UAC7CO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;UACjD,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;QACFE,OAAO,CAACW,GAAG,CAAC,8BAA8B,EAAEqB,QAAQ,CAAC;QACrD,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,+BAA+B,CAAC;QACjE,IAAI,CAACf,UAAU,EAAE;QACjB,IAAI,CAACvG,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;MACF,CAAC;;MACDgF,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CmF,KAAK,CAAC,2BAA2B,CAAC;MACpC;KACD,CAAC;EACJ;EAMAC,eAAeA,CAACpE,KAAY;IAC1B,MAAMqE,IAAI,GAAIrE,KAAK,CAACC,MAA2B,CAACqE,KAAK,GAAG,CAAC,CAAC;IAC1D,IAAID,IAAI,EAAE;MACR,IAAI,CAAC5G,IAAI,CAAC8B,UAAU,CAAC;QAAEvG,KAAK,EAAEqL;MAAI,CAAE,CAAC;MACrC,IAAI,CAAC5G,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEiG,sBAAsB,EAAE;MAEhD,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAAC9O,YAAY,GAAG4O,MAAM,CAACG,MAAM;MACnC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAIAQ,cAAcA,CAAC7E,KAAU;IACvB,MAAMqE,IAAI,GAAGrE,KAAK,CAACC,MAAM,CAACqE,KAAK,CAAC,CAAC,CAAC;IAElC,IAAID,IAAI,EAAE;MACR,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEX,IAAI,CAAC;MAE7B,IAAI,CAACvJ,IAAI,CAACmK,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACzG,SAAS,CACpE6C,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACgE,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBjE,QAAQ,CAACgE,QAAQ,EAAE;UAC3DhG,OAAO,CAACW,GAAG,CAAC,mBAAmB,EAAEsF,OAAO,CAAC;UAGzC,IAAI,CAAC1H,IAAI,CAAC8B,UAAU,CAAC;YACnBvG,KAAK,EAAEmM;WACR,CAAC;SACH,MAAM;UACLjG,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEoG,WAAWA,CAAA;IACT,IAAI,CAAC/H,YAAY,GAAG,EAAE;EACxB;EAEIE,YAAYA,CAAA;IAEZ,IAAI,CAAC1C,WAAW,CAACwK,WAAW,EAAE,CAAChH,SAAS,CAACqB,IAAI,IAAG;MAChD,IAAI,CAACrE,MAAM,GAAGqE,IAAI;IAEpB,CAAC,CAAC;EACF;EAKF4F,SAASA,CAAA;IACP,IAAI,CAACrK,WAAW,GAAG,IAAI;EACzB;EAEAmF,UAAUA,CAAA;IACR,IAAI,CAACnF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACsK,SAAS,EAAE;EAClB;EAEAnE,cAAcA,CAAA;IACZ,IAAI,CAAClG,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC2C,QAAQ,CAAC2H,KAAK,EAAE;IACrB,IAAI,CAAC1I,SAAS,GAAG,KAAK;EACxB;EAEA2I,uBAAuBA,CAACzF,KAAiB;IACvC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACiB,cAAc,EAAE;;EAEzB;EAEA;EACAD,gBAAgBA,CAACxK,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACF,YAAY,GAAG;MAClBmF,IAAI,EAAE,IAAI;MACVlF,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACA8O,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACjP,YAAY,CAACmF,IAAI,GAAG,KAAK;EAChC;EAEA;EACA0J,SAASA,CAAA;IACP,IAAI,CAAC9H,IAAI,CAAC+H,KAAK,EAAE;IACjB;IACA,IAAI,CAAC/H,IAAI,CAAC8B,UAAU,CAAC;MACnBnG,MAAM,EAAE;KACT,CAAC;IACF,IAAI,CAAC0D,SAAS,GAAG,KAAK;IACtB,IAAI,CAACZ,aAAa,GAAG;MACnBjF,MAAM,EAAE,CAAC;MACTZ,QAAQ,EAAE,EAAE;MACZ+C,MAAM,EAAE,YAAY;MACpBJ,KAAK,EAAE,EAAE;MACT5C,KAAK,EAAE,IAAI;MACX8C,eAAe,EAAE,IAAIiD,IAAI,EAAE;MAC3B7C,WAAW,EAAE,EAAE;MACfC,WAAW,EAAC,IAAI;MAChB6C,UAAU,EAAC;KACZ;IACD,IAAI,CAACC,cAAc,GAAG;MACpBpF,MAAM,EAAE,CAAC;MACTZ,QAAQ,EAAE,EAAE;MACZ+C,MAAM,EAAE,YAAY;MACpBJ,KAAK,EAAE,EAAE;MACT5C,KAAK,EAAE,IAAI;MACX8C,eAAe,EAAE,IAAIiD,IAAI,EAAE;MAC3B7C,WAAW,EAAE,EAAE;MACfC,WAAW,EAAC;KAEb;EACH;EAEA;EACA7B,oBAAoBA,CAACgF,UAAiB;IACpC,IAAI,CAACvG,kBAAkB,GAAGuG,UAAU;IACpC;IACA,IAAI,CAACvG,kBAAkB,CAACiD,MAAM,GAAG,YAAY;IAC7C,IAAI,CAAC+B,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACa,wBAAwB,GAAG,KAAK,CAAC,CAAC;IAEvC;IACA,IAAI,CAAC8B,eAAe,CAACyB,UAAU,CAAC;MAC9BoE,WAAW,EAAE,IAAI;MACjBjH,UAAU,EAAE,IAAI,CAACvG,kBAAkB;MACnCqG,WAAW,EAAE,EAAE;MACftD,eAAe,EAAE,IAAIiD,IAAI,EAAE,CAACyJ,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACvD,CAAC;EACJ;EAGF/N,eAAeA,CAAC4E,UAAiB;IAE/B;IACA,IAAI,CAACvG,kBAAkB,GAAGuG,UAAU;IACpC,IAAI,CAACT,4BAA4B,GAAG,KAAK,CAAC,CAAC;IAEzC,IAAI,CAACb,0BAA0B,GAAG,IAAI;EAE1C;EAGEsI,qBAAqBA,CAAA;IACnB,IAAI,CAACvI,sBAAsB,GAAG,KAAK;IAEnC,IAAI,CAAC2C,eAAe,CAAC0H,KAAK,EAAE;EAC9B;EAGFM,kBAAkBA,CAACnD,KAAY;IAC7B,IAAI,CAAC1G,4BAA4B,GAAG,IAAI;IAExC;IACA,IAAI,CAAC,IAAI,CAACK,iBAAiB,CAACG,IAAI,EAAE;MAChCyC,OAAO,CAACW,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAACvD,iBAAiB,CAACI,UAAU,GAAGiG,KAAK;IACzC,IAAI,CAACrG,iBAAiB,CAACK,MAAM,GAAG,UAAU;IAE1C,IAAI,CAAC9B,WAAW,CAACkJ,SAAS,CAAC,IAAI,CAACzH,iBAAiB,CAAC,CAC/C+B,SAAS,CAAC;MACTO,IAAI,EAAGc,IAAI,IAAI;QACbR,OAAO,CAACW,GAAG,CAAC,qCAAqC,EAAEH,IAAI,CAAC;QACxD,IAAI,CAACyB,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAAC6C,yBAAyB,EAAE;QAChC,IAAI,CAACnK,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MAC1C,CAAC;;MACDgF,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,4CAA4C,CAAC;MAC9E;KACD,CAAC;EACN;EAGE6C,yBAAyBA,CAAA;IACvB,IAAI,CAAC5I,0BAA0B,GAAG,KAAK;IAEvC,IAAI,CAAC4B,eAAe,CAACyB,QAAQ,CAAC,IAAI,CAAC;IAEnC,IAAI,CAACnC,iBAAiB,GAAG;MACvBC,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,EAAE;MACftD,eAAe,EAAE,IAAIiD,IAAI,EAAE;MAC3BM,IAAI,EAAE,IAAItI,WAAW,EAAE;MACvBuI,UAAU,EAAE,IAAI1I,KAAK;KACtB;EACH;EAEA+R,8BAA8BA,CAAC/F,KAAiB;IAC9C,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACuD,qBAAqB,EAAE;;EAEhC;EAEAsC,kCAAkCA,CAAChG,KAAiB;IAClD,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAAC6D,yBAAyB,EAAE;;EAEpC;EAEA;EACApD,kBAAkBA,CAACU,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAM2E,OAAO,GAAG,IAAI9J,IAAI,CAACmF,IAAI,CAAC;MAC9B,IAAI4E,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACL,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAO7G,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAoH,QAAQA,CAAC3E,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAC9G,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAAC4H,IAAI,CAAC;;EAE9B;EAEA4E,QAAQA,CAAA;IACN,IAAI,IAAI,CAACrM,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAsM,YAAYA,CAAA;IACV,IAAI,IAAI,CAACtM,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAuM,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC5M,WAAW,GAAG2M,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACpM,UAAU,GAAG,CAAC,EAAE+L,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;;;uBAvgCW5L,mBAAmB,EAAAlG,EAAA,CAAAwS,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA1S,EAAA,CAAAwS,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA5S,EAAA,CAAAwS,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA9S,EAAA,CAAAwS,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAnB9M,mBAAmB;MAAA+M,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCpBhCvT,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAgB,SAAA,cAAsB;UAEtBhB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAgB,SAAA,iBAAyB;UAIrBhB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAgB,SAAA,iBAES;UAEThB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAAoD;UAAvBD,EAAA,CAAAwC,UAAA,mBAAAiR,sDAAA;YAAA,OAASD,GAAA,CAAA5C,SAAA,EAAW;UAAA,EAAC;UAChD5Q,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,+BAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAgB,SAAA,iBAK6C;UAE7ChB,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAAwC,UAAA,4BAAAkR,yEAAAC,MAAA;YAAA,OAAkBH,GAAA,CAAApI,oBAAA,CAAAuI,MAAA,CAAAC,MAAA,CAAArK,KAAA,CAAyC;UAAA,EAAC;UAC5DvJ,EAAA,CAAAyD,UAAA,KAAAoQ,0CAAA,yBAEa;UACf7T,EAAA,CAAAG,YAAA,EAAmB;UAK7BH,EAAA,CAAAC,cAAA,eAAsB;UACMD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,kBAAuF;UAA3DD,EAAA,CAAAwC,UAAA,2BAAAsR,8DAAAH,MAAA;YAAA,OAAAH,GAAA,CAAA/K,cAAA,GAAAkL,MAAA;UAAA,EAA4B,oBAAAI,uDAAA;YAAA,OAAWP,GAAA,CAAArO,eAAA,CAAgB,CAAC,CAAC;UAAA,EAA7B;UACtDnF,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnDH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOtDH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAAwC,UAAA,2BAAAwR,6DAAAL,MAAA;YAAA,OAAAH,GAAA,CAAAnM,UAAA,GAAAsM,MAAA;UAAA,EAAwB,mBAAAM,qDAAA;YAAA,OACfT,GAAA,CAAA/F,cAAA,EAAgB;UAAA,EADD;UAH1BzN,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAgB,SAAA,gBAAiC;UACnChB,EAAA,CAAAG,YAAA,EAAM;UAORH,EAAA,CAAAC,cAAA,eAAmG;UAA1CD,EAAA,CAAAwC,UAAA,mBAAA0R,mDAAAP,MAAA;YAAA,OAASH,GAAA,CAAAzC,uBAAA,CAAA4C,MAAA,CAA+B;UAAA,EAAC;UAChG3T,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAA2R,mDAAAR,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3DpU,EAAA,CAAAC,cAAA,gBAA+C;UAA3BD,EAAA,CAAAwC,UAAA,mBAAA6R,oDAAA;YAAA,OAASb,GAAA,CAAA9G,cAAA,EAAgB;UAAA,EAAC;UAAC1M,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7DH,EAAA,CAAAC,cAAA,cAAmD;UAAAD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7EH,EAAA,CAAAC,cAAA,gBAAoE;UAAvCD,EAAA,CAAAwC,UAAA,sBAAA8R,uDAAA;YAAA,OAAYd,GAAA,CAAArH,YAAA,EAAc;UAAA,EAAC;UACtDnM,EAAA,CAAAgB,SAAA,UAAI;UAGJhB,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAgB,SAAA,iBAI6C;UAC7ChB,EAAA,CAAAC,cAAA,gCAEiF;UAA/DD,EAAA,CAAAwC,UAAA,4BAAA+R,yEAAAZ,MAAA;YAAA,OAAkBH,GAAA,CAAA1I,sBAAA,CAAA6I,MAAA,CAAAC,MAAA,CAAArK,KAAA,CAA2C;UAAA,EAAC;UAC9EvJ,EAAA,CAAAyD,UAAA,KAAA+Q,0CAAA,yBAEa;UACfxU,EAAA,CAAAG,YAAA,EAAmB;UAGrBH,EAAA,CAAAyD,UAAA,KAAAgR,mCAAA,kBAEM;UAGNzU,EAAA,CAAAC,cAAA,iBAAqF;UAAAD,EAAA,CAAAE,MAAA,iCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5GH,EAAA,CAAAgB,SAAA,iBAME;UACFhB,EAAA,CAAAyD,UAAA,KAAAiR,mCAAA,kBAEM;UAGN1U,EAAA,CAAAC,cAAA,iBAAwF;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvHH,EAAA,CAAAgB,SAAA,iBAME;UAGFhB,EAAA,CAAAC,cAAA,iBAA4F;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtHH,EAAA,CAAAgB,SAAA,iBAKE;UACFhB,EAAA,CAAAyD,UAAA,KAAAkR,mCAAA,kBAEM;UAEN3U,EAAA,CAAAC,cAAA,iBAAwF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3GH,EAAA,CAAAC,cAAA,kBAOD;UAC4CD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAyD,UAAA,KAAAmR,sCAAA,qBAES;UACX5U,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAyD,UAAA,KAAAoR,mCAAA,kBAEM;UAGN7U,EAAA,CAAAC,cAAA,iBAAkF;UAAAD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvHH,EAAA,CAAAC,cAAA,iBAME;UAHAD,EAAA,CAAAwC,UAAA,oBAAAsS,sDAAAnB,MAAA;YAAA,OAAUH,GAAA,CAAArD,cAAA,CAAAwD,MAAA,CAAsB;UAAA,EAAC;UAHnC3T,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAyD,UAAA,KAAAsR,mCAAA,kBAEM;UAEN/U,EAAA,CAAAgB,SAAA,UAAM;UACNhB,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAQfH,EAAA,CAAAC,cAAA,gBAA2F;UAAtCD,EAAA,CAAAwC,UAAA,mBAAAwS,oDAAArB,MAAA;YAAA,OAASH,GAAA,CAAAnI,mBAAA,CAAAsI,MAAA,CAA2B;UAAA,EAAC;UACxF3T,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAAyS,oDAAAtB,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3DpU,EAAA,CAAAC,cAAA,iBAA2C;UAAvBD,EAAA,CAAAwC,UAAA,mBAAA0S,qDAAA;YAAA,OAAS1B,GAAA,CAAA9H,UAAA,EAAY;UAAA,EAAC;UAAC1L,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,eAAoD;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzFH,EAAA,CAAAC,cAAA,iBAA8D;UAArCD,EAAA,CAAAwC,UAAA,sBAAA2S,wDAAA;YAAA,OAAY3B,GAAA,CAAAjE,UAAA,EAAY;UAAA,EAAC;UAChDvP,EAAA,CAAAgB,SAAA,WAAI;UAGNhB,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,oBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAgB,SAAA,kBAI6C;UAC7ChB,EAAA,CAAAC,cAAA,iCAEgF;UAA9DD,EAAA,CAAAwC,UAAA,4BAAA4S,0EAAAzB,MAAA;YAAA,OAAkBH,GAAA,CAAA5I,qBAAA,CAAA+I,MAAA,CAAAC,MAAA,CAAArK,KAAA,CAA0C;UAAA,EAAC;UAC7EvJ,EAAA,CAAAyD,UAAA,MAAA4R,2CAAA,yBAEa;UACfrV,EAAA,CAAAG,YAAA,EAAmB;UAGnBH,EAAA,CAAAyD,UAAA,MAAA6R,oCAAA,kBAEM;UAGNtV,EAAA,CAAAC,cAAA,kBAAiF;UAAAD,EAAA,CAAAE,MAAA,kCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxGH,EAAA,CAAAgB,SAAA,kBAME;UACFhB,EAAA,CAAAyD,UAAA,MAAA8R,oCAAA,kBAEM;UAGNvV,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAgB,SAAA,kBAME;UAGFhB,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClHH,EAAA,CAAAgB,SAAA,kBAKE;UACFhB,EAAA,CAAAyD,UAAA,MAAA+R,oCAAA,kBAEM;UACLxV,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1GH,EAAA,CAAAC,cAAA,mBAQC;UACyCD,EAAA,CAAAE,MAAA,sCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAyD,UAAA,MAAAgS,uCAAA,qBAES;UACXzV,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAyD,UAAA,MAAAiS,oCAAA,kBAEM;UAIJ1V,EAAA,CAAAC,cAAA,kBAA8E;UAAAD,EAAA,CAAAE,MAAA,2CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,kBAME;UAHAD,EAAA,CAAAwC,UAAA,oBAAAmT,uDAAAhC,MAAA;YAAA,OAAUH,GAAA,CAAArD,cAAA,CAAAwD,MAAA,CAAsB;UAAA,EAAC;UAHnC3T,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAyD,UAAA,MAAAmS,oCAAA,kBAEM;UAEN5V,EAAA,CAAAgB,SAAA,WAAM;UACNhB,EAAA,CAAAC,cAAA,mBAA8C;UAC5CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UA2BXH,EAAA,CAAAC,cAAA,gBAAiH;UAAjDD,EAAA,CAAAwC,UAAA,mBAAAqT,oDAAAlC,MAAA;YAAA,OAASH,GAAA,CAAAnC,8BAAA,CAAAsC,MAAA,CAAsC;UAAA,EAAC;UAC9G3T,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAAsT,oDAAAnC,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3DpU,EAAA,CAAAC,cAAA,iBAAsD;UAAlCD,EAAA,CAAAwC,UAAA,mBAAAuT,qDAAA;YAAA,OAASvC,GAAA,CAAAxE,qBAAA,EAAuB;UAAA,EAAC;UAAChP,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,eAAkD;UAAAD,EAAA,CAAAE,MAAA,mCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAyD,UAAA,MAAAuS,oCAAA,kBAGM;UAENhW,EAAA,CAAAC,cAAA,iBAAuE;UAAnCD,EAAA,CAAAwC,UAAA,sBAAAyT,wDAAA;YAAA,OAAYzC,GAAA,CAAA7E,mBAAA,EAAqB;UAAA,EAAC;UAE1E3O,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAgB,SAAA,kBAK6C;UAE7ChB,EAAA,CAAAC,cAAA,iCAGyD;UAAvDD,EAAA,CAAAwC,UAAA,4BAAA0T,0EAAAvC,MAAA;YAAA,OAAkBH,GAAA,CAAAtI,cAAA,CAAAyI,MAAA,CAAAC,MAAA,CAAArK,KAAA,CAAmC;UAAA,EAAC;UACtDvJ,EAAA,CAAAyD,UAAA,MAAA0S,2CAAA,yBAEa;UACfnW,EAAA,CAAAG,YAAA,EAAmB;UAIrBH,EAAA,CAAAyD,UAAA,MAAA2S,oCAAA,kBAGM;UAIApW,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,qBAKyH;UACzHD,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAAgB,SAAA,kBAIuG;UAGvGhB,EAAA,CAAAC,cAAA,mBAAyC;UACtCD,EAAA,CAAAE,MAAA,qCACH;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAWfH,EAAA,CAAAC,cAAA,gBAAyH;UAArDD,EAAA,CAAAwC,UAAA,mBAAA6T,oDAAA1C,MAAA;YAAA,OAASH,GAAA,CAAAlC,kCAAA,CAAAqC,MAAA,CAA0C;UAAA,EAAC;UACtH3T,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAA8T,oDAAA3C,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3DpU,EAAA,CAAAC,cAAA,iBAA0D;UAAtCD,EAAA,CAAAwC,UAAA,mBAAA+T,qDAAA;YAAA,OAAS/C,GAAA,CAAAlE,yBAAA,EAA2B;UAAA,EAAC;UAACtP,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAC,cAAA,eAAkD;UAAAD,EAAA,CAAAE,MAAA,mCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAyD,UAAA,MAAA+S,oCAAA,kBAGM;UAEVxW,EAAA,CAAAC,cAAA,qBAA8G;UAAxGD,EAAA,CAAAwC,UAAA,sBAAAiU,wDAAA;YAAA,OAAAjD,GAAA,CAAA/R,kBAAA,IAAkC+R,GAAA,CAAApC,kBAAA,CAAAoC,GAAA,CAAA/R,kBAAA,CAAsC;UAAA,EAAC;UAG7EzB,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAgB,SAAA,kBAMU;UAEPhB,EAAA,CAAAC,cAAA,iCAGyD;UAAvDD,EAAA,CAAAwC,UAAA,4BAAAkU,0EAAA/C,MAAA;YAAA,OAAkBH,GAAA,CAAAtI,cAAA,CAAAyI,MAAA,CAAAC,MAAA,CAAArK,KAAA,CAAmC;UAAA,EAAC;UACtDvJ,EAAA,CAAAyD,UAAA,MAAAkT,2CAAA,yBAEa;UACf3W,EAAA,CAAAG,YAAA,EAAmB;UAGrBH,EAAA,CAAAyD,UAAA,MAAAmT,oCAAA,kBAEM;UAGN5W,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,qBAMyH;UAJvHD,EAAA,CAAAwC,UAAA,2BAAAqU,iEAAAlD,MAAA;YAAA,OAAAH,GAAA,CAAA5L,iBAAA,CAAAE,WAAA,GAAA6L,MAAA;UAAA,EAA2C;UAK7C3T,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAAC,cAAA,kBAKuG;UAFrGD,EAAA,CAAAwC,UAAA,2BAAAsU,8DAAAnD,MAAA;YAAA,OAAAH,GAAA,CAAA5L,iBAAA,CAAApD,eAAA,GAAAmP,MAAA;UAAA,EAA+C;UAHjD3T,EAAA,CAAAG,YAAA,EAKuG;UAGvGH,EAAA,CAAAgB,SAAA,WAAM;UACNhB,EAAA,CAAAC,cAAA,mBAAyC;UACzCD,EAAA,CAAAE,MAAA,+BACA;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAkGXH,EAAA,CAAAC,cAAA,iBAAuB;UAInBD,EAAA,CAAAyD,UAAA,MAAAsT,oCAAA,kBAEM;UAGN/W,EAAA,CAAAC,cAAA,eAA6B;UAS6BD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAiD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAyD,UAAA,MAAAuT,mCAAA,mBA4FK;UACPhX,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAyD,UAAA,MAAAwT,oCAAA,kBAgBM;UAEJjX,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,gBAAiB;UAGMD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;;;;;UA3pBzJH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAAjL,qBAAA,CAAqC,oBAAA2O,GAAA;UAMrClX,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAA/I,kBAAA,CAAkC;UAELzK,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAzM,0BAAA,CAA6B;UAUxC/G,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA/K,cAAA,CAA4B;UAiBpDzI,EAAA,CAAAM,SAAA,IAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAnM,UAAA,CAAwB;UAYXrH,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmX,eAAA,KAAAC,GAAA,EAAA5D,GAAA,CAAAhN,eAAA,EAAqC;UAI9CxG,EAAA,CAAAM,SAAA,GAAsB;UAAtBN,EAAA,CAAAI,UAAA,cAAAoT,GAAA,CAAArK,QAAA,CAAsB;UASjBnJ,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,oBAAAiX,GAAA,CAAiC;UAGtBrX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAA9I,YAAA,CAA4B;UAEd1K,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAxM,OAAA,CAAU;UAMtChH,EAAA,CAAAM,SAAA,GAAqF;UAArFN,EAAA,CAAAI,UAAA,WAAAkX,QAAA,GAAA9D,GAAA,CAAArK,QAAA,CAAAS,GAAA,4BAAA0N,QAAA,CAAAlL,OAAA,QAAAkL,QAAA,GAAA9D,GAAA,CAAArK,QAAA,CAAAS,GAAA,4BAAA0N,QAAA,CAAAC,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAAqF;UAarFpI,EAAA,CAAAM,SAAA,GAA2F;UAA3FN,EAAA,CAAAI,UAAA,WAAAoX,QAAA,GAAAhE,GAAA,CAAArK,QAAA,CAAAS,GAAA,+BAAA4N,QAAA,CAAApL,OAAA,QAAAoL,QAAA,GAAAhE,GAAA,CAAArK,QAAA,CAAAS,GAAA,+BAAA4N,QAAA,CAAAD,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAA2F;UAsB3FpI,EAAA,CAAAM,SAAA,GAAyG;UAAzGN,EAAA,CAAAI,UAAA,WAAAqX,QAAA,GAAAjE,GAAA,CAAArK,QAAA,CAAAS,GAAA,sCAAA6N,QAAA,CAAArL,OAAA,QAAAqL,QAAA,GAAAjE,GAAA,CAAArK,QAAA,CAAAS,GAAA,sCAAA6N,QAAA,CAAAF,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAAyG;UAarGpI,EAAA,CAAAM,SAAA,GAAgB;UAAhBN,EAAA,CAAAI,UAAA,iBAAgB;UACQJ,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAnL,YAAA,CAAe;UAI3CrI,EAAA,CAAAM,SAAA,GAAmG;UAAnGN,EAAA,CAAAI,UAAA,WAAAsX,QAAA,GAAAlE,GAAA,CAAArK,QAAA,CAAAS,GAAA,mCAAA8N,QAAA,CAAAtL,OAAA,QAAAsL,QAAA,GAAAlE,GAAA,CAAArK,QAAA,CAAAS,GAAA,mCAAA8N,QAAA,CAAAH,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAAmG;UAanGpI,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAAtS,YAAA,CAAkB;UAeXlB,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmX,eAAA,KAAAC,GAAA,EAAA5D,GAAA,CAAAjN,WAAA,EAAiC;UAI9CvG,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,cAAAoT,GAAA,CAAAzK,IAAA,CAAkB;UASf/I,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,oBAAAuX,IAAA,CAA6B;UAGlB3X,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAA9I,YAAA,CAA4B;UAEd1K,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAxM,OAAA,CAAU;UAMpChH,EAAA,CAAAM,SAAA,GAA6E;UAA7EN,EAAA,CAAAI,UAAA,WAAAwX,QAAA,GAAApE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,4BAAAgO,QAAA,CAAAxL,OAAA,QAAAwL,QAAA,GAAApE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,4BAAAgO,QAAA,CAAAL,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAA6E;UAa7EpI,EAAA,CAAAM,SAAA,GAAmF;UAAnFN,EAAA,CAAAI,UAAA,WAAAyX,QAAA,GAAArE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,+BAAAiO,QAAA,CAAAzL,OAAA,QAAAyL,QAAA,GAAArE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,+BAAAiO,QAAA,CAAAN,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAAmF;UAsBnFpI,EAAA,CAAAM,SAAA,GAAiG;UAAjGN,EAAA,CAAAI,UAAA,WAAA0X,QAAA,GAAAtE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,sCAAAkO,QAAA,CAAA1L,OAAA,QAAA0L,QAAA,GAAAtE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,sCAAAkO,QAAA,CAAAP,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAAiG;UAahGpI,EAAA,CAAAM,SAAA,GAAgB;UAAhBN,EAAA,CAAAI,UAAA,iBAAgB;UACSJ,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAAnL,YAAA,CAAe;UAI3CrI,EAAA,CAAAM,SAAA,GAA2F;UAA3FN,EAAA,CAAAI,UAAA,WAAA2X,QAAA,GAAAvE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,mCAAAmO,QAAA,CAAA3L,OAAA,QAAA2L,QAAA,GAAAvE,GAAA,CAAAzK,IAAA,CAAAa,GAAA,mCAAAmO,QAAA,CAAAR,OAAA,KAAA/D,GAAA,CAAApL,SAAA,EAA2F;UAczFpI,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAAtS,YAAA,CAAkB;UAkCPlB,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmX,eAAA,KAAAC,GAAA,EAAA5D,GAAA,CAAA/M,sBAAA,EAA4C;UAKrDzG,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAA/R,kBAAA,CAAwB;UAKxBzB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,cAAAoT,GAAA,CAAApK,eAAA,CAA6B;UAOnCpJ,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAAlL,eAAA,CAA+B,oBAAA0P,IAAA;UAM/BhY,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAA/I,kBAAA,CAAkC;UAELzK,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA1M,oBAAA,CAAuB;UAOlD9G,EAAA,CAAAM,SAAA,GAAqE;UAArEN,EAAA,CAAAI,UAAA,YAAA6X,QAAA,GAAAzE,GAAA,CAAApK,eAAA,CAAAQ,GAAA,2BAAAqO,QAAA,CAAA1O,KAAA,KAAAiK,GAAA,CAAAlM,wBAAA,CAAqE;UAsCxDtH,EAAA,CAAAM,SAAA,IAAgD;UAAhDN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAmX,eAAA,KAAAC,GAAA,EAAA5D,GAAA,CAAA9M,0BAAA,EAAgD;UAKzD1G,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAA/R,kBAAA,CAAwB;UAahCzB,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAAlL,eAAA,CAA+B,oBAAA0P,IAAA;UAO3BhY,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAoT,GAAA,CAAA/I,kBAAA,CAAkC;UAELzK,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA1M,oBAAA,CAAuB;UAMlD9G,EAAA,CAAAM,SAAA,GAA6D;UAA7DN,EAAA,CAAAI,UAAA,UAAAoT,GAAA,CAAA5L,iBAAA,CAAAG,IAAA,IAAAyL,GAAA,CAAAjM,4BAAA,CAA6D;UAQjEvH,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA5L,iBAAA,CAAAE,WAAA,CAA2C;UAY3C9H,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA5L,iBAAA,CAAApD,eAAA,CAA+C;UA8GzCxE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAAxR,YAAA,CAAAmF,IAAA,CAAuB;UAyBSnH,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAAoT,GAAA,CAAA5M,UAAA,CAAa;UAwGN5G,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAoT,GAAA,CAAAvN,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
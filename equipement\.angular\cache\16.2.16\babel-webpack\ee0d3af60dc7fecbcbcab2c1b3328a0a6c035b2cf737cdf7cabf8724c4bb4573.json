{"ast": null, "code": "import { Utilisateur } from './utilisateur';\nimport { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./utilisateur.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"ngx-webcam\";\nconst _c0 = [\"video\"];\nfunction UtilisateurComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.notification.message, \" \");\n  }\n}\nfunction UtilisateurComponent_div_26_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"L'email est requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UtilisateurComponent_div_26_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Email invalide.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UtilisateurComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, UtilisateurComponent_div_26_span_1_Template, 2, 0, \"span\", 29);\n    i0.ɵɵtemplate(2, UtilisateurComponent_div_26_span_2_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r2 = i0.ɵɵreference(25);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r2.errors == null ? null : _r2.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r2.errors == null ? null : _r2.errors[\"email\"]);\n  }\n}\nfunction UtilisateurComponent_div_34_div_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Le mot de passe est requis.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UtilisateurComponent_div_34_div_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Le mot de passe doit contenir au moins 6 caract\\u00E8res. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UtilisateurComponent_div_34_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtemplate(1, UtilisateurComponent_div_34_div_5_span_1_Template, 2, 0, \"span\", 29);\n    i0.ɵɵtemplate(2, UtilisateurComponent_div_34_div_5_span_2_Template, 2, 0, \"span\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r9 = i0.ɵɵreference(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r9.errors == null ? null : _r9.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r9.errors == null ? null : _r9.errors[\"minlength\"]);\n  }\n}\nfunction UtilisateurComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"label\", 31);\n    i0.ɵɵtext(2, \"Mot de passe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 32, 33);\n    i0.ɵɵlistener(\"ngModelChange\", function UtilisateurComponent_div_34_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.password = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, UtilisateurComponent_div_34_div_5_Template, 3, 2, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r9 = i0.ɵɵreference(4);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.password);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r9.invalid && _r9.touched);\n  }\n}\nfunction UtilisateurComponent_div_35_p_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r15.resultMessage);\n  }\n}\nfunction UtilisateurComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"webcam\", 36);\n    i0.ɵɵlistener(\"imageCapture\", function UtilisateurComponent_div_35_Template_webcam_imageCapture_2_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.handleImage($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Positionnez votre visage et cliquez pour v\\u00E9rifier\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function UtilisateurComponent_div_35_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.triggerSnapshot());\n    });\n    i0.ɵɵtext(6, \" D\\u00E9marrer la v\\u00E9rification\\n\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, UtilisateurComponent_div_35_p_7_Template, 2, 1, \"p\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"width\", 400)(\"height\", 350)(\"trigger\", ctx_r5.trigger);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.resultMessage);\n  }\n}\nfunction UtilisateurComponent_p_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 38);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.errorMessage, \" \");\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    active: a0\n  };\n};\nconst _c2 = function () {\n  return [\"/motpasseoublie\"];\n};\nconst _c3 = \"body[_ngcontent-%COMP%] {\\n  font-family: 'Inter', sans-serif;\\n  background: #f5f7fa;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n.login-container[_ngcontent-%COMP%] {\\n  max-width: 580px;\\n  margin: 100px auto;\\n  padding: 20px;\\n  margin-top: 90px;\\n  \\n}\\n.password-box[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.password-box[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border: 1px solid #ccc;\\n  border-radius: 8px;\\n}\\n\\n\\n.login-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 20px; \\n\\n  left: 50%;\\n  transform: translateX(-50%);\\n  text-align: center;\\n  margin-bottom: 0;\\n}\\n\\n.icon-shield[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-top: -20px;\\n\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: -10px 0 0 0; \\n\\n  font-size: 32px;\\n}\\n\\n\\n\\n\\n.login-box[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  padding: 35px 40px;\\n}\\n\\n.login-box[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  font-size: 22ppx;\\n}\\n\\n.subtitle[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  color: #666;\\n  margin-bottom: 25px;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-top: 20px;\\n  font-weight: 600;\\n  font-size: 15px;\\n  color: black;\\n}\\n\\nselect[_ngcontent-%COMP%], input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px 16px;\\n  margin-top: 8px;\\n  border: 1px solid #ccc;\\n  border-radius: 10px;\\n  font-size: 15px;\\n}\\n\\n.input-group[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 12px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: #888;\\n  font-size: 16px;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  padding-left: 36px;\\n}\\n\\n.auth-methods[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin: 25px 0 20px;\\n  gap: 10px;\\n}\\n\\n.auth-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  padding: 12px 0;\\n  background: #f0f0f0;\\n  border: none;\\n  border-radius: 10px;\\n  cursor: pointer;\\n  font-weight: 600;\\n  font-size: 15px;\\n  transition: background 0.2s;\\n}\\n\\n.auth-btn.active[_ngcontent-%COMP%] {\\n  background: #0d6efd;\\n  color: white;\\n}\\n\\n.face-box[_ngcontent-%COMP%] {\\n  margin: 10px 0 30px;\\n}\\n\\n.face-frame[_ngcontent-%COMP%] {\\n  border: 2px dashed #ccc;\\n  border-radius: 14px;\\n  text-align: center;\\n  padding: 25px 15px;\\n}\\n\\n.camera-icon[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  margin-bottom: 10px;\\n}\\n\\n.start-recognition[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding: 10px 18px;\\n  background: #0d6efd;\\n  color: white;\\n  border: none;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  font-size: 14px;\\n}\\n\\n.connect-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: #0d6efd;\\n  color: white;\\n  padding: 14px;\\n  border: none;\\n  border-radius: 10px;\\n  font-weight: bold;\\n  font-size: 17px;\\n  cursor: pointer;\\n  margin-top: 20px;\\n}\\n\\n.forgot-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 18px;\\n}\\n\\n.forgot-link[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #0d6efd;\\n  text-decoration: none;\\n  font-size: 15px;\\n}\\n.auth-title[_ngcontent-%COMP%] {\\n  font-size: 18px;         \\n\\n  font-weight: 600;        \\n\\n  margin: 30px 0 10px 0;   \\n\\n  color: #333;             \\n\\n}\\n.error-message[_ngcontent-%COMP%] {\\n  color: red;\\n  font-size: 0.85em;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n}\\n\\n.input-group[_ngcontent-%COMP%]   input.ng-invalid.ng-touched[_ngcontent-%COMP%] {\\n  border-color: red;\\n}\\n.face-frame[_ngcontent-%COMP%] {\\n  border: 3px solid #007bff;\\n  padding: 16px;\\n  text-align: center;\\n  border-radius: 10px;\\n  width: 280px; \\n\\n  margin: auto;\\n  background-color: #f0f0f0;\\n}\\n\\n.video-frame[_ngcontent-%COMP%] {\\n  width: 240px;  \\n\\n  height: 180px;\\n  border: 2px dashed #007bff;\\n  margin-bottom: 10px;\\n  border-radius: 10px;\\n  object-fit: cover;\\n}\\n.face-frame[_ngcontent-%COMP%]   video[_ngcontent-%COMP%] {\\n  width: 400px !important;     \\n\\n  height: auto !important;     \\n\\n  max-height: 150px;           \\n\\n  border-radius: 8px;          \\n\\n  object-fit: cover;           \\n\\n  display: block;\\n  margin-bottom: 10px;\\n}\\n.face-box[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n}\\n\\n.face-frame[_ngcontent-%COMP%] {\\n  background: #fafafa;\\n  border-radius: 12px;\\n  padding: 20px;\\n  max-width: 700px;\\n  width: 100%;\\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\\n  text-align: center;\\n  font-family: Arial, sans-serif;\\n}\\n\\n.my-webcam[_ngcontent-%COMP%]   video[_ngcontent-%COMP%] {\\n  width: 600px !important;\\n  height: 400px !important;\\n  border-radius: 10px;\\n  object-fit: cover;\\n  display: block;\\n  margin: 100px auto 15px auto;\\n  box-shadow: 0 0 8px rgba(0,0,0,0.15);\\n  margin-bottom: 8px;\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  background-color: #3f51b5;\\n  border: none;\\n  color: rgb(0, 0, 0);\\n  padding: 10px 18px;\\n  font-size: 16px;\\n  border-radius: 8px;\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n\\nbutton[_ngcontent-%COMP%]:hover {\\n  background-color: #495ee5;\\n    color: rgb(255, 255, 255);\\n}\\n\\np[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  color: #000000;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  z-index: 9999;\\n  padding: 12px 20px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 0;\\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border-bottom: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border-bottom: 1px solid #f5c6cb;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\";\nexport class UtilisateurComponent {\n  constructor(userService, router, http) {\n    this.userService = userService;\n    this.router = router;\n    this.http = http;\n    this.methode = 'password';\n    this.email = '';\n    this.password = '';\n    this.errorMessage = '';\n    this.cameraStarted = false;\n    this.cameraStream = null;\n    // Notification system\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.showWebcam = false;\n    this.resultMessage = '';\n    this.trigger = new Subject();\n    this.webcamImage = null;\n    this.user = new Utilisateur();\n  }\n  ngOnInit() {\n    localStorage.clear();\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  ngAfterViewInit() {\n    if (this.methode === 'face') {\n      this.startCamera();\n    }\n  }\n  ngOnChanges(changes) {\n    if (changes['methode']) {\n      const newValue = changes['methode'].currentValue;\n      if (newValue === 'face') {\n        this.startCamera();\n      } else if (newValue === 'password') {\n        this.stopCamera();\n      }\n    }\n  }\n  setMethode(type) {\n    this.methode = type;\n    if (type === 'face') {\n      setTimeout(() => this.startCamera(), 0);\n    } else if (type === 'password') {\n      this.stopCamera();\n    }\n  }\n  onLogin() {\n    if (!this.email || !this.password) {\n      this.errorMessage = 'Veuillez remplir tous les champs';\n      return;\n    }\n    const user = {\n      registrationNumber: this.email,\n      password: this.password\n    };\n    this.userService.login(user).subscribe(response => {\n      sessionStorage.setItem('token', response.token);\n      sessionStorage.setItem('username', response.username);\n      sessionStorage.setItem('id', response.registrationNumber);\n      sessionStorage.setItem('role', response.role);\n      console.log('User logged in successfully:', response);\n      if (response.role === 'USER') {\n        this.router.navigate(['/equipementDSI']);\n      }\n      this.router.navigate(['/dashboard']);\n    }, error => {\n      console.error('Erreur de connexion :', error);\n      this.errorMessage = 'Nom d’utilisateur ou mot de passe incorrect';\n    });\n  }\n  startCamera() {\n    if (this.cameraStarted || !this.videoElementRef) return;\n    const video = this.videoElementRef.nativeElement;\n    navigator.mediaDevices.getUserMedia({\n      video: true\n    }).then(stream => {\n      video.srcObject = stream;\n      this.cameraStream = stream;\n      this.cameraStarted = true;\n    }).catch(error => {\n      console.error('Erreur accès caméra :', error);\n      this.showNotification('error', 'Impossible d\\'accéder à la caméra');\n    });\n  }\n  stopCamera() {\n    if (this.cameraStream) {\n      this.cameraStream.getTracks().forEach(track => track.stop());\n      this.cameraStream = null;\n      this.cameraStarted = false;\n      if (this.videoElementRef?.nativeElement) {\n        this.videoElementRef.nativeElement.srcObject = null;\n      }\n    }\n  }\n  triggerSnapshot() {\n    console.log(\"Snapshot triggered\");\n    this.trigger.next();\n  }\n  handleImage(webcamImage) {\n    this.webcamImage = webcamImage;\n    console.log(\"Image captured:\", webcamImage);\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    this.http.post('http://127.0.0.1:5000/verify-face', {\n      image: webcamImage.imageAsDataUrl\n    }, {\n      headers: headers,\n      withCredentials: true // Important for handling credentials\n    }).subscribe(res => {\n      this.resultMessage = res.verified ? \"✅ Face Verified!\" : \"❌ Verification Failed\";\n      if (res.verified) {\n        this.router.navigate(['/dashboard']); // Adjust route as needed\n      }\n    }, error => {\n      console.error(\"Verification error:\", error);\n      this.resultMessage = \"❌ Error during verification.\";\n    });\n  }\n  static {\n    this.ɵfac = function UtilisateurComponent_Factory(t) {\n      return new (t || UtilisateurComponent)(i0.ɵɵdirectiveInject(i1.UtilisateurService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UtilisateurComponent,\n      selectors: [[\"app-utilisateur\"]],\n      viewQuery: function UtilisateurComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.videoElementRef = _t.first);\n        }\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 42,\n      vars: 14,\n      consts: [[\"lang\", \"fr\"], [\"charset\", \"UTF-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1.0\"], [\"href\", \"https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap\", \"rel\", \"stylesheet\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [\"novalidate\", \"\", 3, \"ngSubmit\"], [\"loginForm\", \"ngForm\"], [1, \"login-container\"], [1, \"login-header\"], [\"src\", \"assets/images/logos/esprit.png\", \"alt\", \"\", 2, \"width\", \"200px\", \"height\", \"auto\", \"display\", \"block\", \"margin-left\", \"0px\"], [1, \"login-box\"], [1, \"subtitle\"], [\"for\", \"email\"], [1, \"input-group\"], [1, \"icon\"], [\"type\", \"email\", \"name\", \"email\", \"id\", \"email\", \"placeholder\", \"<EMAIL>\", \"required\", \"\", \"email\", \"\", 3, \"ngModel\", \"ngModelChange\"], [\"emailRef\", \"ngModel\"], [\"class\", \"error-message\", 4, \"ngIf\"], [1, \"auth-title\"], [1, \"auth-methods\"], [\"type\", \"button\", 1, \"auth-btn\", 3, \"ngClass\", \"click\"], [\"class\", \"password-box\", 4, \"ngIf\"], [\"class\", \"face-box\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"connect-btn\"], [\"class\", \"error-message\", \"style\", \"color: red; margin-top: 10px; margin-bottom: 15px; font-size: 18px;\", 4, \"ngIf\"], [1, \"forgot-link\"], [3, \"routerLink\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"error-message\"], [4, \"ngIf\"], [1, \"password-box\"], [\"for\", \"password\"], [\"type\", \"password\", \"name\", \"password\", \"id\", \"password\", \"placeholder\", \"Entrez votre mot de passe\", \"required\", \"\", \"minlength\", \"6\", 3, \"ngModel\", \"ngModelChange\"], [\"passwordRef\", \"ngModel\"], [1, \"face-box\"], [1, \"face-frame\"], [1, \"my-webcam\", 3, \"width\", \"height\", \"trigger\", \"imageCapture\"], [2, \"color\", \"white\", 3, \"click\"], [1, \"error-message\", 2, \"color\", \"red\", \"margin-top\", \"10px\", \"margin-bottom\", \"15px\", \"font-size\", \"18px\"]],\n      template: function UtilisateurComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Connexion\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"link\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"body\");\n          i0.ɵɵtemplate(8, UtilisateurComponent_div_8_Template, 2, 2, \"div\", 4);\n          i0.ɵɵelementStart(9, \"form\", 5, 6);\n          i0.ɵɵlistener(\"ngSubmit\", function UtilisateurComponent_Template_form_ngSubmit_9_listener() {\n            return ctx.onLogin();\n          });\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8);\n          i0.ɵɵelement(13, \"img\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 10)(15, \"h2\");\n          i0.ɵɵtext(16, \"Authentification S\\u00E9curis\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"p\", 11);\n          i0.ɵɵtext(18, \"Choisissez votre m\\u00E9thode de connexion pr\\u00E9f\\u00E9r\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"label\", 12);\n          i0.ɵɵtext(20, \"Adresse email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 13)(22, \"span\", 14);\n          i0.ɵɵtext(23, \"@\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"input\", 15, 16);\n          i0.ɵɵlistener(\"ngModelChange\", function UtilisateurComponent_Template_input_ngModelChange_24_listener($event) {\n            return ctx.email = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(26, UtilisateurComponent_div_26_Template, 3, 2, \"div\", 17);\n          i0.ɵɵelementStart(27, \"p\", 18);\n          i0.ɵɵtext(28, \"M\\u00E9thode d'authentification\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 19)(30, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function UtilisateurComponent_Template_button_click_30_listener() {\n            return ctx.setMethode(\"password\");\n          });\n          i0.ɵɵtext(31, \" \\uD83D\\uDD12 Mot de passe \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function UtilisateurComponent_Template_button_click_32_listener() {\n            return ctx.setMethode(\"face\");\n          });\n          i0.ɵɵtext(33, \" \\uD83D\\uDCF7 Reconnaissance faciale \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(34, UtilisateurComponent_div_34_Template, 6, 2, \"div\", 21);\n          i0.ɵɵtemplate(35, UtilisateurComponent_div_35_Template, 8, 4, \"div\", 22);\n          i0.ɵɵelementStart(36, \"button\", 23);\n          i0.ɵɵtext(37, \"Se connecter\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, UtilisateurComponent_p_38_Template, 2, 1, \"p\", 24);\n          i0.ɵɵelementStart(39, \"div\", 25)(40, \"a\", 26);\n          i0.ɵɵtext(41, \"Mot de passe oubli\\u00E9 ?\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          const _r2 = i0.ɵɵreference(25);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngModel\", ctx.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r2.invalid && _r2.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx.methode === \"password\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c1, ctx.methode === \"face\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.methode === \"password\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.methode === \"face\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(13, _c2));\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgIf, i2.RouterLink, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.MinLengthValidator, i5.EmailValidator, i5.NgModel, i5.NgForm, i6.WebcamComponent],\n      styles: [_c3, _c3]\n    });\n  }\n}", "map": {"version": 3, "names": ["Utilisa<PERSON>ur", "Subject", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "ctx_r0", "notification", "type", "ɵɵadvance", "ɵɵtextInterpolate1", "message", "ɵɵtemplate", "UtilisateurComponent_div_26_span_1_Template", "UtilisateurComponent_div_26_span_2_Template", "_r2", "errors", "UtilisateurComponent_div_34_div_5_span_1_Template", "UtilisateurComponent_div_34_div_5_span_2_Template", "_r9", "ɵɵlistener", "UtilisateurComponent_div_34_Template_input_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r14", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "password", "UtilisateurComponent_div_34_div_5_Template", "ctx_r4", "invalid", "touched", "ɵɵtextInterpolate", "ctx_r15", "resultMessage", "UtilisateurComponent_div_35_Template_webcam_imageCapture_2_listener", "_r17", "ctx_r16", "handleImage", "UtilisateurComponent_div_35_Template_button_click_5_listener", "ctx_r18", "triggerSnapshot", "UtilisateurComponent_div_35_p_7_Template", "ctx_r5", "trigger", "ctx_r6", "errorMessage", "UtilisateurComponent", "constructor", "userService", "router", "http", "methode", "email", "cameraStarted", "cameraStream", "show", "showWebcam", "webcamImage", "user", "ngOnInit", "localStorage", "clear", "showNotification", "setTimeout", "hideNotification", "ngAfterViewInit", "startCamera", "ngOnChanges", "changes", "newValue", "currentValue", "stopCamera", "setMethode", "onLogin", "registrationNumber", "login", "subscribe", "response", "sessionStorage", "setItem", "token", "username", "role", "console", "log", "navigate", "error", "videoElementRef", "video", "nativeElement", "navigator", "mediaDevices", "getUserMedia", "then", "stream", "srcObject", "catch", "getTracks", "for<PERSON>ach", "track", "stop", "next", "headers", "post", "image", "imageAsDataUrl", "withCredentials", "res", "verified", "ɵɵdirectiveInject", "i1", "UtilisateurService", "i2", "Router", "i3", "HttpClient", "selectors", "viewQuery", "UtilisateurComponent_Query", "rf", "ctx", "ɵɵelement", "UtilisateurComponent_div_8_Template", "UtilisateurComponent_Template_form_ngSubmit_9_listener", "UtilisateurComponent_Template_input_ngModelChange_24_listener", "UtilisateurComponent_div_26_Template", "UtilisateurComponent_Template_button_click_30_listener", "UtilisateurComponent_Template_button_click_32_listener", "UtilisateurComponent_div_34_Template", "UtilisateurComponent_div_35_Template", "UtilisateurComponent_p_38_Template", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c2"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  OnInit,\r\n  ViewChild,\r\n  ElementRef,\r\n  AfterViewInit,\r\n  OnChanges,\r\n  SimpleChanges\r\n} from '@angular/core';\r\nimport { UtilisateurService } from './utilisateur.service';\r\nimport { Router } from '@angular/router';\r\nimport { Utilisateur } from './utilisateur';\r\nimport { Subject } from 'rxjs';\r\nimport { WebcamImage } from 'ngx-webcam';\r\nimport { HttpClient } from '@angular/common/http';\r\n\r\n@Component({\r\n  selector: 'app-utilisateur',\r\n  templateUrl: './utilisateur.component.html',\r\n  styleUrls: ['./utilisateur.component.css']\r\n})\r\nexport class UtilisateurComponent implements OnInit, AfterViewInit, OnChanges {\r\n  @ViewChild('video') videoElementRef!: ElementRef<HTMLVideoElement>;\r\n\r\n  methode: 'password' | 'face' = 'password';\r\n  email: string = '';\r\n  password: string = '';\r\n  errorMessage: string = '';\r\n  cameraStarted = false;\r\n  cameraStream: MediaStream | null = null;\r\n\r\n  // Notification system\r\n  notification = {\r\n    show: false,\r\n    type: 'success', // 'success' or 'error'\r\n    message: ''\r\n  };\r\n  showWebcam: boolean = false;  \r\n  \r\n  resultMessage: string = '';\r\n\r\n  trigger: Subject<void> = new Subject<void>();\r\n webcamImage: WebcamImage | null = null;\r\n  user: Utilisateur = new Utilisateur();\r\n\r\n  constructor(private userService: UtilisateurService, private router: Router,private http: HttpClient) {}\r\n\r\n  ngOnInit(): void {\r\n    localStorage.clear();\r\n\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    if (this.methode === 'face') {\r\n      this.startCamera();\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges): void {\r\n    if (changes['methode']) {\r\n      const newValue = changes['methode'].currentValue;\r\n      if (newValue === 'face') {\r\n        this.startCamera();\r\n      } else if (newValue === 'password') {\r\n        this.stopCamera();\r\n      }\r\n    }\r\n  }\r\n\r\n  setMethode(type: 'password' | 'face') {\r\n    this.methode = type;\r\n\r\n    if (type === 'face') {\r\n      setTimeout(() => this.startCamera(), 0);\r\n    } else if (type === 'password') {\r\n      this.stopCamera();\r\n    }\r\n  }\r\n\r\n  onLogin(): void {\r\n    if (!this.email || !this.password) {\r\n      this.errorMessage = 'Veuillez remplir tous les champs';\r\n      return;\r\n    }\r\n\r\n    const user = { registrationNumber: this.email, password: this.password };\r\n\r\n    this.userService.login(user).subscribe(\r\n      (response) => {\r\n        sessionStorage.setItem('token', response.token);\r\n        sessionStorage.setItem('username', response.username);\r\n        sessionStorage.setItem('id', response.registrationNumber);\r\n        sessionStorage.setItem('role', response.role);\r\nconsole.log('User logged in successfully:', response);\r\nif(response.role === 'USER'){\r\n  this.router.navigate(['/equipementDSI']);\r\n}\r\n\r\n        this.router.navigate(['/dashboard']);\r\n      },\r\n      (error) => {\r\n        console.error('Erreur de connexion :', error);\r\n        this.errorMessage = 'Nom d’utilisateur ou mot de passe incorrect';\r\n      }\r\n    );\r\n  }\r\n\r\n  startCamera(): void {\r\n    if (this.cameraStarted || !this.videoElementRef) return;\r\n\r\n    const video = this.videoElementRef.nativeElement;\r\n\r\n    navigator.mediaDevices.getUserMedia({ video: true })\r\n      .then((stream) => {\r\n        video.srcObject = stream;\r\n        this.cameraStream = stream;\r\n        this.cameraStarted = true;\r\n      })\r\n      .catch((error) => {\r\n        console.error('Erreur accès caméra :', error);\r\n        this.showNotification('error', 'Impossible d\\'accéder à la caméra');\r\n      });\r\n  }\r\n\r\n  stopCamera(): void {\r\n    if (this.cameraStream) {\r\n      this.cameraStream.getTracks().forEach(track => track.stop());\r\n      this.cameraStream = null;\r\n      this.cameraStarted = false;\r\n\r\n      if (this.videoElementRef?.nativeElement) {\r\n        this.videoElementRef.nativeElement.srcObject = null;\r\n      }\r\n    }\r\n  }\r\ntriggerSnapshot(): void {\r\n  console.log(\"Snapshot triggered\");\r\n  this.trigger.next();\r\n}\r\n\r\n  handleImage(webcamImage: WebcamImage): void {\r\n    this.webcamImage = webcamImage;\r\n  console.log(\"Image captured:\", webcamImage);\r\n    const headers = {\r\n      'Content-Type': 'application/json'\r\n    };\r\n  \r\n    this.http.post('http://127.0.0.1:5000/verify-face', {\r\n      image: webcamImage.imageAsDataUrl\r\n    }, {\r\n      headers: headers,\r\n      withCredentials: true // Important for handling credentials\r\n    }).subscribe((res: any) => {\r\n      this.resultMessage = res.verified ? \"✅ Face Verified!\" : \"❌ Verification Failed\";\r\n  \r\n      if (res.verified) {\r\n        this.router.navigate(['/dashboard']); // Adjust route as needed\r\n      }\r\n    }, error => {\r\n      console.error(\"Verification error:\", error);\r\n      this.resultMessage = \"❌ Error during verification.\";\r\n    });\r\n  }\r\n}\r\n", "<!DOCTYPE html>\r\n<html lang=\"fr\">\r\n<head>\r\n  <meta charset=\"UTF-8\" />\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>\r\n  <title>Connexion</title>\r\n  <link rel=\"stylesheet\" href=\"utilisateur.component.css\" />\r\n  <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap\" rel=\"stylesheet\" />\r\n</head>\r\n<body>\r\n\r\n  <!-- Simple Notification Bar -->\r\n  <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n    {{ notification.message }}\r\n  </div>\r\n\r\n  <form #loginForm=\"ngForm\" (ngSubmit)=\"onLogin()\" novalidate>\r\n    <div class=\"login-container\">\r\n      <div class=\"login-header\">\r\n        <img src=\"assets/images/logos/esprit.png\" alt=\"\" style=\"width: 200px; height: auto; display: block; margin-left: 0px;\" />\r\n    \r\n      </div>\r\n\r\n      <div class=\"login-box\">\r\n        <h2>Authentification Sécurisée</h2>\r\n        <p class=\"subtitle\">Choisissez votre méthode de connexion préférée</p>\r\n\r\n        <!-- Email -->\r\n        <label for=\"email\">Adresse email</label>\r\n        <div class=\"input-group\">\r\n          <span class=\"icon\">@</span>\r\n          <input\r\n            type=\"email\"\r\n            [(ngModel)]=\"email\"\r\n            name=\"email\"\r\n            id=\"email\"\r\n            placeholder=\"<EMAIL>\"\r\n            required\r\n            email\r\n            #emailRef=\"ngModel\"\r\n          />\r\n        </div>\r\n        <div *ngIf=\"emailRef.invalid && emailRef.touched\" class=\"error-message\">\r\n          <span *ngIf=\"emailRef.errors?.['required']\">L'email est requis.</span>\r\n          <span *ngIf=\"emailRef.errors?.['email']\">Email invalide.</span>\r\n        </div>\r\n\r\n        <!-- Auth method -->\r\n        <p class=\"auth-title\">Méthode d'authentification</p>\r\n        <div class=\"auth-methods\">\r\n          <button type=\"button\"\r\n                  class=\"auth-btn\"\r\n                  [ngClass]=\"{ active: methode === 'password' }\"\r\n                  (click)=\"setMethode('password')\">\r\n            🔒 Mot de passe\r\n          </button>\r\n          <button type=\"button\"\r\n                  class=\"auth-btn\"\r\n                  [ngClass]=\"{ active: methode === 'face' }\"\r\n                  (click)=\"setMethode('face')\">\r\n            📷 Reconnaissance faciale\r\n          </button>\r\n        </div>\r\n\r\n        <!-- Mot de passe -->\r\n        <div class=\"password-box\" *ngIf=\"methode === 'password'\">\r\n          <label for=\"password\">Mot de passe</label>\r\n          <input\r\n            type=\"password\"\r\n            name=\"password\"\r\n            id=\"password\"\r\n            [(ngModel)]=\"password\"\r\n            placeholder=\"Entrez votre mot de passe\"\r\n            required\r\n            minlength=\"6\"\r\n            #passwordRef=\"ngModel\"\r\n          />\r\n          <div *ngIf=\"passwordRef.invalid && passwordRef.touched\" class=\"error-message\">\r\n            <span *ngIf=\"passwordRef.errors?.['required']\">Le mot de passe est requis.</span>\r\n            <span *ngIf=\"passwordRef.errors?.['minlength']\">\r\n              Le mot de passe doit contenir au moins 6 caractères.\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n<div class=\"face-box\" *ngIf=\"methode === 'face'\">\r\n  <!-- Webcam + face detection -->\r\n  <div class=\"face-frame\">\r\n    <webcam\r\n      class=\"my-webcam\"\r\n      [width]=\"400\"\r\n      [height]=\"350\"\r\n      [trigger]=\"trigger\"\r\n      (imageCapture)=\"handleImage($event)\">\r\n    </webcam>\r\n\r\n    <p>Positionnez votre visage et cliquez pour vérifier</p>\r\n\r\n<button (click)=\"triggerSnapshot()\" style=\"color: white;\">\r\n  Démarrer la vérification\r\n</button>\r\n\r\n\r\n\r\n    <p *ngIf=\"resultMessage\">{{ resultMessage }}</p>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n        <!-- Bouton Connexion -->\r\n        <button class=\"connect-btn\" type=\"submit\" >Se connecter</button>\r\n<!-- Message d'erreur global si les identifiants sont invalides -->\r\n <p *ngIf=\"errorMessage\"\r\n             class=\"error-message\"\r\n             style=\"color: red; margin-top: 10px; margin-bottom: 15px; font-size: 18px;\">\r\n            {{ errorMessage }}\r\n          </p>\r\n\r\n        <div class=\"forgot-link\">\r\n        <a [routerLink]=\"['/motpasseoublie']\">Mot de passe oublié ?</a>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </form>\r\n\r\n</body>\r\n</html>\r\n"], "mappings": "AAWA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,MAAM;;;;;;;;;;;ICA5BC,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFP,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAJ,MAAA,CAAAC,YAAA,CAAAI,OAAA,MACF;;;;;IA6BQV,EAAA,CAAAC,cAAA,WAA4C;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACtEH,EAAA,CAAAC,cAAA,WAAyC;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFjEH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAW,UAAA,IAAAC,2CAAA,mBAAsE;IACtEZ,EAAA,CAAAW,UAAA,IAAAE,2CAAA,mBAA+D;IACjEb,EAAA,CAAAG,YAAA,EAAM;;;;;IAFGH,EAAA,CAAAQ,SAAA,GAAmC;IAAnCR,EAAA,CAAAI,UAAA,SAAAU,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAmC;IACnCf,EAAA,CAAAQ,SAAA,GAAgC;IAAhCR,EAAA,CAAAI,UAAA,SAAAU,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,UAAgC;;;;;IAkCrCf,EAAA,CAAAC,cAAA,WAA+C;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACjFH,EAAA,CAAAC,cAAA,WAAgD;IAC9CD,EAAA,CAAAE,MAAA,kEACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAJTH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAW,UAAA,IAAAK,iDAAA,mBAAiF;IACjFhB,EAAA,CAAAW,UAAA,IAAAM,iDAAA,mBAEO;IACTjB,EAAA,CAAAG,YAAA,EAAM;;;;;IAJGH,EAAA,CAAAQ,SAAA,GAAsC;IAAtCR,EAAA,CAAAI,UAAA,SAAAc,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,aAAsC;IACtCf,EAAA,CAAAQ,SAAA,GAAuC;IAAvCR,EAAA,CAAAI,UAAA,SAAAc,GAAA,CAAAH,MAAA,kBAAAG,GAAA,CAAAH,MAAA,cAAuC;;;;;;IAdlDf,EAAA,CAAAC,cAAA,cAAyD;IACjCD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1CH,EAAA,CAAAC,cAAA,oBASE;IALAD,EAAA,CAAAmB,UAAA,2BAAAC,oEAAAC,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAxB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAAAF,OAAA,CAAAG,QAAA,GAAAN,MAAA;IAAA,EAAsB;IAJxBrB,EAAA,CAAAG,YAAA,EASE;IACFH,EAAA,CAAAW,UAAA,IAAAiB,0CAAA,kBAKM;IACR5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAZFH,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAI,UAAA,YAAAyB,MAAA,CAAAF,QAAA,CAAsB;IAMlB3B,EAAA,CAAAQ,SAAA,GAAgD;IAAhDR,EAAA,CAAAI,UAAA,SAAAc,GAAA,CAAAY,OAAA,IAAAZ,GAAA,CAAAa,OAAA,CAAgD;;;;;IA2B5D/B,EAAA,CAAAC,cAAA,QAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAvBH,EAAA,CAAAQ,SAAA,GAAmB;IAAnBR,EAAA,CAAAgC,iBAAA,CAAAC,OAAA,CAAAC,aAAA,CAAmB;;;;;;IAnBhDlC,EAAA,CAAAC,cAAA,cAAiD;IAQ3CD,EAAA,CAAAmB,UAAA,0BAAAgB,oEAAAd,MAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAArC,EAAA,CAAAyB,aAAA;MAAA,OAAgBzB,EAAA,CAAA0B,WAAA,CAAAW,OAAA,CAAAC,WAAA,CAAAjB,MAAA,CAAmB;IAAA,EAAC;IACtCrB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,6DAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE5DH,EAAA,CAAAC,cAAA,iBAA0D;IAAlDD,EAAA,CAAAmB,UAAA,mBAAAoB,6DAAA;MAAAvC,EAAA,CAAAsB,aAAA,CAAAc,IAAA;MAAA,MAAAI,OAAA,GAAAxC,EAAA,CAAAyB,aAAA;MAAA,OAASzB,EAAA,CAAA0B,WAAA,CAAAc,OAAA,CAAAC,eAAA,EAAiB;IAAA,EAAC;IACjCzC,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAILH,EAAA,CAAAW,UAAA,IAAA+B,wCAAA,gBAAgD;IAClD1C,EAAA,CAAAG,YAAA,EAAM;;;;IAfFH,EAAA,CAAAQ,SAAA,GAAa;IAAbR,EAAA,CAAAI,UAAA,cAAa,2BAAAuC,MAAA,CAAAC,OAAA;IAcX5C,EAAA,CAAAQ,SAAA,GAAmB;IAAnBR,EAAA,CAAAI,UAAA,SAAAuC,MAAA,CAAAT,aAAA,CAAmB;;;;;IAS1BlC,EAAA,CAAAC,cAAA,YAEwF;IAC7ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADFH,EAAA,CAAAQ,SAAA,GACF;IADER,EAAA,CAAAS,kBAAA,MAAAoC,MAAA,CAAAC,YAAA,MACF;;;;;;;;;;;;ADhGV,OAAM,MAAOC,oBAAoB;EAwB/BC,YAAoBC,WAA+B,EAAUC,MAAc,EAASC,IAAgB;IAAhF,KAAAF,WAAW,GAAXA,WAAW;IAA8B,KAAAC,MAAM,GAANA,MAAM;IAAiB,KAAAC,IAAI,GAAJA,IAAI;IArBxF,KAAAC,OAAO,GAAwB,UAAU;IACzC,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAA1B,QAAQ,GAAW,EAAE;IACrB,KAAAmB,YAAY,GAAW,EAAE;IACzB,KAAAQ,aAAa,GAAG,KAAK;IACrB,KAAAC,YAAY,GAAuB,IAAI;IAEvC;IACA,KAAAjD,YAAY,GAAG;MACbkD,IAAI,EAAE,KAAK;MACXjD,IAAI,EAAE,SAAS;MACfG,OAAO,EAAE;KACV;IACD,KAAA+C,UAAU,GAAY,KAAK;IAE3B,KAAAvB,aAAa,GAAW,EAAE;IAE1B,KAAAU,OAAO,GAAkB,IAAI7C,OAAO,EAAQ;IAC7C,KAAA2D,WAAW,GAAuB,IAAI;IACrC,KAAAC,IAAI,GAAgB,IAAI7D,WAAW,EAAE;EAEkE;EAEvG8D,QAAQA,CAAA;IACNC,YAAY,CAACC,KAAK,EAAE;EAEtB;EAEA;EACAC,gBAAgBA,CAACxD,IAAyB,EAAEG,OAAe;IACzD,IAAI,CAACJ,YAAY,GAAG;MAClBkD,IAAI,EAAE,IAAI;MACVjD,IAAI,EAAEA,IAAI;MACVG,OAAO,EAAEA;KACV;IAED;IACAsD,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC3D,YAAY,CAACkD,IAAI,GAAG,KAAK;EAChC;EAEAU,eAAeA,CAAA;IACb,IAAI,IAAI,CAACd,OAAO,KAAK,MAAM,EAAE;MAC3B,IAAI,CAACe,WAAW,EAAE;;EAEtB;EAEAC,WAAWA,CAACC,OAAsB;IAChC,IAAIA,OAAO,CAAC,SAAS,CAAC,EAAE;MACtB,MAAMC,QAAQ,GAAGD,OAAO,CAAC,SAAS,CAAC,CAACE,YAAY;MAChD,IAAID,QAAQ,KAAK,MAAM,EAAE;QACvB,IAAI,CAACH,WAAW,EAAE;OACnB,MAAM,IAAIG,QAAQ,KAAK,UAAU,EAAE;QAClC,IAAI,CAACE,UAAU,EAAE;;;EAGvB;EAEAC,UAAUA,CAAClE,IAAyB;IAClC,IAAI,CAAC6C,OAAO,GAAG7C,IAAI;IAEnB,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnByD,UAAU,CAAC,MAAM,IAAI,CAACG,WAAW,EAAE,EAAE,CAAC,CAAC;KACxC,MAAM,IAAI5D,IAAI,KAAK,UAAU,EAAE;MAC9B,IAAI,CAACiE,UAAU,EAAE;;EAErB;EAEAE,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACrB,KAAK,IAAI,CAAC,IAAI,CAAC1B,QAAQ,EAAE;MACjC,IAAI,CAACmB,YAAY,GAAG,kCAAkC;MACtD;;IAGF,MAAMa,IAAI,GAAG;MAAEgB,kBAAkB,EAAE,IAAI,CAACtB,KAAK;MAAE1B,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE;IAExE,IAAI,CAACsB,WAAW,CAAC2B,KAAK,CAACjB,IAAI,CAAC,CAACkB,SAAS,CACnCC,QAAQ,IAAI;MACXC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACG,KAAK,CAAC;MAC/CF,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEF,QAAQ,CAACI,QAAQ,CAAC;MACrDH,cAAc,CAACC,OAAO,CAAC,IAAI,EAAEF,QAAQ,CAACH,kBAAkB,CAAC;MACzDI,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEF,QAAQ,CAACK,IAAI,CAAC;MACrDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEP,QAAQ,CAAC;MACrD,IAAGA,QAAQ,CAACK,IAAI,KAAK,MAAM,EAAC;QAC1B,IAAI,CAACjC,MAAM,CAACoC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;MAGlC,IAAI,CAACpC,MAAM,CAACoC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;IACtC,CAAC,EACAC,KAAK,IAAI;MACRH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACzC,YAAY,GAAG,6CAA6C;IACnE,CAAC,CACF;EACH;EAEAqB,WAAWA,CAAA;IACT,IAAI,IAAI,CAACb,aAAa,IAAI,CAAC,IAAI,CAACkC,eAAe,EAAE;IAEjD,MAAMC,KAAK,GAAG,IAAI,CAACD,eAAe,CAACE,aAAa;IAEhDC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;MAAEJ,KAAK,EAAE;IAAI,CAAE,CAAC,CACjDK,IAAI,CAAEC,MAAM,IAAI;MACfN,KAAK,CAACO,SAAS,GAAGD,MAAM;MACxB,IAAI,CAACxC,YAAY,GAAGwC,MAAM;MAC1B,IAAI,CAACzC,aAAa,GAAG,IAAI;IAC3B,CAAC,CAAC,CACD2C,KAAK,CAAEV,KAAK,IAAI;MACfH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,IAAI,CAACxB,gBAAgB,CAAC,OAAO,EAAE,mCAAmC,CAAC;IACrE,CAAC,CAAC;EACN;EAEAS,UAAUA,CAAA;IACR,IAAI,IAAI,CAACjB,YAAY,EAAE;MACrB,IAAI,CAACA,YAAY,CAAC2C,SAAS,EAAE,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MAC5D,IAAI,CAAC9C,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,aAAa,GAAG,KAAK;MAE1B,IAAI,IAAI,CAACkC,eAAe,EAAEE,aAAa,EAAE;QACvC,IAAI,CAACF,eAAe,CAACE,aAAa,CAACM,SAAS,GAAG,IAAI;;;EAGzD;EACFvD,eAAeA,CAAA;IACb2C,OAAO,CAACC,GAAG,CAAC,oBAAoB,CAAC;IACjC,IAAI,CAACzC,OAAO,CAAC0D,IAAI,EAAE;EACrB;EAEEhE,WAAWA,CAACoB,WAAwB;IAClC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAChC0B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE3B,WAAW,CAAC;IACzC,MAAM6C,OAAO,GAAG;MACd,cAAc,EAAE;KACjB;IAED,IAAI,CAACpD,IAAI,CAACqD,IAAI,CAAC,mCAAmC,EAAE;MAClDC,KAAK,EAAE/C,WAAW,CAACgD;KACpB,EAAE;MACDH,OAAO,EAAEA,OAAO;MAChBI,eAAe,EAAE,IAAI,CAAC;KACvB,CAAC,CAAC9B,SAAS,CAAE+B,GAAQ,IAAI;MACxB,IAAI,CAAC1E,aAAa,GAAG0E,GAAG,CAACC,QAAQ,GAAG,kBAAkB,GAAG,uBAAuB;MAEhF,IAAID,GAAG,CAACC,QAAQ,EAAE;QAChB,IAAI,CAAC3D,MAAM,CAACoC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;;IAE1C,CAAC,EAAEC,KAAK,IAAG;MACTH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,IAAI,CAACrD,aAAa,GAAG,8BAA8B;IACrD,CAAC,CAAC;EACJ;;;uBA/JWa,oBAAoB,EAAA/C,EAAA,CAAA8G,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAhH,EAAA,CAAA8G,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAlH,EAAA,CAAA8G,iBAAA,CAAAK,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAApBrE,oBAAoB;MAAAsE,SAAA;MAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCpBjCxH,EAAA,CAAAC,cAAA,cAAgB;UAEdD,EAAA,CAAA0H,SAAA,cAAwB;UAExB1H,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAExBH,EAAA,CAAA0H,SAAA,cAA6G;UAC/G1H,EAAA,CAAAG,YAAA,EAAO;UACPH,EAAA,CAAAC,cAAA,WAAM;UAGJD,EAAA,CAAAW,UAAA,IAAAgH,mCAAA,iBAEM;UAEN3H,EAAA,CAAAC,cAAA,iBAA4D;UAAlCD,EAAA,CAAAmB,UAAA,sBAAAyG,uDAAA;YAAA,OAAYH,GAAA,CAAA/C,OAAA,EAAS;UAAA,EAAC;UAC9C1E,EAAA,CAAAC,cAAA,cAA6B;UAEzBD,EAAA,CAAA0H,SAAA,cAAyH;UAE3H1H,EAAA,CAAAG,YAAA,EAAM;UAENH,EAAA,CAAAC,cAAA,eAAuB;UACjBD,EAAA,CAAAE,MAAA,4CAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnCH,EAAA,CAAAC,cAAA,aAAoB;UAAAD,EAAA,CAAAE,MAAA,0EAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGtEH,EAAA,CAAAC,cAAA,iBAAmB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,eAAyB;UACJD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3BH,EAAA,CAAAC,cAAA,qBASE;UAPAD,EAAA,CAAAmB,UAAA,2BAAA0G,8DAAAxG,MAAA;YAAA,OAAAoG,GAAA,CAAApE,KAAA,GAAAhC,MAAA;UAAA,EAAmB;UAFrBrB,EAAA,CAAAG,YAAA,EASE;UAEJH,EAAA,CAAAW,UAAA,KAAAmH,oCAAA,kBAGM;UAGN9H,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAE,MAAA,uCAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACpDH,EAAA,CAAAC,cAAA,eAA0B;UAIhBD,EAAA,CAAAmB,UAAA,mBAAA4G,uDAAA;YAAA,OAASN,GAAA,CAAAhD,UAAA,CAAW,UAAU,CAAC;UAAA,EAAC;UACtCzE,EAAA,CAAAE,MAAA,mCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGqC;UAA7BD,EAAA,CAAAmB,UAAA,mBAAA6G,uDAAA;YAAA,OAASP,GAAA,CAAAhD,UAAA,CAAW,MAAM,CAAC;UAAA,EAAC;UAClCzE,EAAA,CAAAE,MAAA,6CACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAIXH,EAAA,CAAAW,UAAA,KAAAsH,oCAAA,kBAkBM;UAEdjI,EAAA,CAAAW,UAAA,KAAAuH,oCAAA,kBAqBM;UAKElI,EAAA,CAAAC,cAAA,kBAA2C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEvEH,EAAA,CAAAW,UAAA,KAAAwH,kCAAA,gBAIa;UAENnI,EAAA,CAAAC,cAAA,eAAyB;UACaD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;UA5G/DH,EAAA,CAAAQ,SAAA,GAAuB;UAAvBR,EAAA,CAAAI,UAAA,SAAAqH,GAAA,CAAAnH,YAAA,CAAAkD,IAAA,CAAuB;UAqBnBxD,EAAA,CAAAQ,SAAA,IAAmB;UAAnBR,EAAA,CAAAI,UAAA,YAAAqH,GAAA,CAAApE,KAAA,CAAmB;UASjBrD,EAAA,CAAAQ,SAAA,GAA0C;UAA1CR,EAAA,CAAAI,UAAA,SAAAU,GAAA,CAAAgB,OAAA,IAAAhB,GAAA,CAAAiB,OAAA,CAA0C;UAUtC/B,EAAA,CAAAQ,SAAA,GAA8C;UAA9CR,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoI,eAAA,IAAAC,GAAA,EAAAZ,GAAA,CAAArE,OAAA,iBAA8C;UAM9CpD,EAAA,CAAAQ,SAAA,GAA0C;UAA1CR,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAoI,eAAA,KAAAC,GAAA,EAAAZ,GAAA,CAAArE,OAAA,aAA0C;UAOzBpD,EAAA,CAAAQ,SAAA,GAA4B;UAA5BR,EAAA,CAAAI,UAAA,SAAAqH,GAAA,CAAArE,OAAA,gBAA4B;UAoBxCpD,EAAA,CAAAQ,SAAA,GAAwB;UAAxBR,EAAA,CAAAI,UAAA,SAAAqH,GAAA,CAAArE,OAAA,YAAwB;UA4B1CpD,EAAA,CAAAQ,SAAA,GAAkB;UAAlBR,EAAA,CAAAI,UAAA,SAAAqH,GAAA,CAAA3E,YAAA,CAAkB;UAOZ9C,EAAA,CAAAQ,SAAA,GAAkC;UAAlCR,EAAA,CAAAI,UAAA,eAAAJ,EAAA,CAAAsI,eAAA,KAAAC,GAAA,EAAkC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
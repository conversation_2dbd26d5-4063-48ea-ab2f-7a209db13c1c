{"ast": null, "code": "import { Equip } from 'src/app/equipement/equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Panne } from './Panne';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../Shared/layout/layout.component\";\nfunction EquipementsComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 62);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r5.firstName, \" \", user_r5.lastName, \" - \", user_r5.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.notification.message, \" \");\n  }\n}\nfunction EquipementsComponent_tr_72_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r7.NameUtilisateur[equip_r6.idEqui]), \" \");\n  }\n}\nfunction EquipementsComponent_tr_72_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 64);\n    i0.ɵɵelement(2, \"img\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 64)(4, \"div\", 66)(5, \"h6\", 67);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 68);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 64)(10, \"span\", 69);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 64)(13, \"span\", 69);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 64)(17, \"span\", 70);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementsComponent_tr_72_div_19_Template, 3, 3, \"div\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 64)(21, \"span\", 69);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 72)(24, \"button\", 73);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_72_Template_button_click_24_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const equip_r6 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.openPanneModal(equip_r6));\n    });\n    i0.ɵɵelement(25, \"i\", 74);\n    i0.ɵɵtext(26, \"Declare Panne \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const equip_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r6.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r6.model == null ? null : equip_r6.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r6.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 11, equip_r6.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r6.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r6.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r6.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r6.statut.toLowerCase() === \"affecte\" || equip_r6.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(equip_r6.panne == null ? null : equip_r6.panne.etatActuel == null ? null : equip_r6.panne.etatActuel.titre);\n  }\n}\nfunction EquipementsComponent_nav_73_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 78)(1, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_73_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const i_r13 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.loadEquipements(i_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.index;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r13 === ctx_r11.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r13 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementsComponent_nav_73_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 76)(1, \"ul\", 77)(2, \"li\", 78)(3, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_73_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.loadEquipements(ctx_r16.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementsComponent_nav_73_li_5_Template, 3, 3, \"li\", 80);\n    i0.ɵɵelementStart(6, \"li\", 78)(7, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_73_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadEquipements(ctx_r18.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r4.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages - 1);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      panne: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null,\n      panne: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    // Propriétés pour les pannes\n    this.selectedPanne = new Panne();\n    this.showPanneModal = false;\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.signupErrors = {};\n    // Initialisation du formulaire de panne\n    this.panneForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: ['', [Validators.required, Validators.minLength(10)]],\n      priorite: ['MOYENNE', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  openPanneModal(equipement) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n  /**\n   * Fermer le modal de panne\n   */\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n  /**\n   * Fermer le modal en cliquant à l'extérieur\n   */\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closePanneModal();\n    }\n  }\n  onSubmitPanne() {\n    const Etat = {\n      titre\n    };\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement\n      };\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: response => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          this.loadEquipements(this.currentPage);\n        },\n        error: error => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function EquipementsComponent_Factory(t) {\n      return new (t || EquipementsComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementsComponent,\n      selectors: [[\"app-equipements\"]],\n      decls: 117,\n      vars: 15,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [2, \"font-size\", \"14px\", \"color\", \"#666\", \"margin-bottom\", \"15px\"], [\"for\", \"titre\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"titre\", \"type\", \"text\", \"formControlName\", \"titre\", \"placeholder\", \"Ex: \\u00C9cran ne s'allume plus\", \"required\", \"\", 1, \"form-inputp\"], [\"for\", \"priorite\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"priorite\", \"formControlName\", \"priorite\", \"required\", \"\", 1, \"form-inputp\"], [\"value\", \"FAIBLE\"], [\"value\", \"MOYENNE\"], [\"value\", \"HAUTE\"], [\"value\", \"CRITIQUE\"], [\"for\", \"description\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez le probl\\u00E8me...\", \"required\", \"\", 1, \"form-inputp\"], [1, \"form-buttons\"], [\"type\", \"button\", 1, \"btn-cancel\", 3, \"click\"], [\"type\", \"submit\", 1, \"btn-submit\", 3, \"disabled\"], [3, \"value\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"px-1\", \"text-end\"], [\"title\", \"D\\u00E9clarer une panne\", 1, \"btn\", \"btn-dark\", \"btn-sm\", 3, \"click\"], [1, \"\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function EquipementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel Panne \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementsComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 24)(43, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementsComponent_Template_input_input_43_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"span\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"body\", 27)(46, \"div\", 28);\n          i0.ɵɵtemplate(47, EquipementsComponent_div_47_Template, 2, 2, \"div\", 29);\n          i0.ɵɵelementStart(48, \"div\", 7)(49, \"div\", 30)(50, \"div\", 31)(51, \"div\", 32)(52, \"div\", 14)(53, \"div\", 33)(54, \"table\", 34)(55, \"thead\")(56, \"tr\")(57, \"th\", 35);\n          i0.ɵɵtext(58, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 35);\n          i0.ɵɵtext(60, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 35);\n          i0.ɵɵtext(62, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 35);\n          i0.ɵɵtext(64, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 35);\n          i0.ɵɵtext(66, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 35);\n          i0.ɵɵtext(68, \"Etat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 36);\n          i0.ɵɵtext(70, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"tbody\");\n          i0.ɵɵtemplate(72, EquipementsComponent_tr_72_Template, 27, 14, \"tr\", 37);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(73, EquipementsComponent_nav_73_Template, 9, 6, \"nav\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(74, \"div\", 30)(75, \"div\", 39)(76, \"p\", 40);\n          i0.ɵɵtext(77, \"Design and Developed by \");\n          i0.ɵɵelementStart(78, \"a\", 41);\n          i0.ɵɵtext(79, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(80, \" Distributed by \");\n          i0.ɵɵelementStart(81, \"a\", 42);\n          i0.ɵɵtext(82, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(83, \"div\", 43);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_83_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(84, \"div\", 44);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_84_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(85, \"span\", 45);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_85_listener() {\n            return ctx.closePanneModal();\n          });\n          i0.ɵɵtext(86, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"h3\", 46);\n          i0.ɵɵtext(88, \"D\\u00E9clarer une panne\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"form\", 47);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_89_listener() {\n            return ctx.onSubmitPanne();\n          });\n          i0.ɵɵelement(90, \"br\");\n          i0.ɵɵelementStart(91, \"p\", 48)(92, \"strong\");\n          i0.ɵɵtext(93, \"\\u00C9quipement:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(94);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(95, \"label\", 49);\n          i0.ɵɵtext(96, \"Titre de la panne\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(97, \"input\", 50);\n          i0.ɵɵelementStart(98, \"label\", 51);\n          i0.ɵɵtext(99, \"Priorit\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"select\", 52)(101, \"option\", 53);\n          i0.ɵɵtext(102, \"Faible\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"option\", 54);\n          i0.ɵɵtext(104, \"Moyenne\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"option\", 55);\n          i0.ɵɵtext(106, \"Haute\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"option\", 56);\n          i0.ɵɵtext(108, \"Critique\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(109, \"label\", 57);\n          i0.ɵɵtext(110, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(111, \"textarea\", 58);\n          i0.ɵɵelementStart(112, \"div\", 59)(113, \"button\", 60);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_button_click_113_listener() {\n            return ctx.closePanneModal();\n          });\n          i0.ɵɵtext(114, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"button\", 61);\n          i0.ɵɵtext(116, \"D\\u00E9clarer\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(25);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, ctx.showPanneModal));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.panneForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate2(\" \", ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.model == null ? null : ctx.selectedPanne.equipement.model.nomModel, \" - \", ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.numSerie, \" \");\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"disabled\", !ctx.panneForm.valid);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.FormControlDirective, i3.FormGroupDirective, i3.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\"\\n\\n\\n\\n\\n.btn-danger.btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.5);\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 30px;\\n  border-radius: 10px;\\n  width: 90%;\\n  max-width: 500px;\\n  position: relative;\\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: 20px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n  color: #aaa;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.form-inputp[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px;\\n  margin: 50px 0 20px 0;\\n  border: 1px solid #ddd;\\n  border-radius: 5px;\\n  font-size: 14px;\\n}\\n\\n.form-inputp[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n}\\n\\n\\n\\n.form-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  justify-content: flex-end;\\n  margin-top: 20px;\\n}\\n\\n.btn-cancel[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  background-color: #6c757d;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n}\\n\\n.btn-cancel[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n\\n.btn-submit[_ngcontent-%COMP%] {\\n  padding: 10px 20px;\\n  background-color: #dc3545;\\n  color: white;\\n  border: none;\\n  border-radius: 5px;\\n  cursor: pointer;\\n}\\n\\n.btn-submit[_ngcontent-%COMP%]:hover {\\n  background-color: #c82333;\\n}\\n\\n.btn-submit[_ngcontent-%COMP%]:disabled {\\n  background-color: #6c757d;\\n  cursor: not-allowed;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r5", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "ctx_r2", "notification", "type", "ɵɵtextInterpolate1", "message", "ɵɵpipeBind1", "ctx_r7", "NameUtilisateur", "equip_r6", "idEqui", "ɵɵelement", "ɵɵtemplate", "EquipementsComponent_tr_72_div_19_Template", "ɵɵlistener", "EquipementsComponent_tr_72_Template_button_click_24_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "openPanneModal", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "model", "nomModel", "numSerie", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "panne", "etatActuel", "titre", "EquipementsComponent_nav_73_li_5_Template_a_click_1_listener", "_r15", "i_r13", "index", "ctx_r14", "loadEquipements", "ɵɵclassProp", "ctx_r11", "currentPage", "EquipementsComponent_nav_73_Template_a_click_3_listener", "_r17", "ctx_r16", "EquipementsComponent_nav_73_li_5_Template", "EquipementsComponent_nav_73_Template_a_click_7_listener", "ctx_r18", "ctx_r4", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "EquipementsComponent", "authservice", "http", "fb", "utilisateurService", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "description", "<PERSON><PERSON><PERSON><PERSON>", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "<PERSON><PERSON><PERSON>", "showPanneModal", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "signupErrors", "panneForm", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "priorite", "ngOnInit", "GetAllModels", "getFournisseur", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "searchUsers", "users", "displayUtilisateur", "displayModel", "getallFournisseur", "data", "onUserSearchSelected", "page", "keyword", "username", "userVal", "searchEquipements1", "getDSIEquipements", "log", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "onFileSelected", "event", "file", "target", "files", "formData", "FormData", "append", "post", "response", "imageUrl", "fullUrl", "form", "patchValue", "resetErrors", "getAllModel", "showNotification", "setTimeout", "hideNotification", "formatDateForInput", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "reset", "closePanneModal", "closeOnOutsideClick", "classList", "contains", "onSubmitPanne", "Etat", "valid", "panneData", "get", "declarer<PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementsComponent_Template", "rf", "ctx", "EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementsComponent_mat_option_40_Template", "EquipementsComponent_Template_input_ngModelChange_43_listener", "EquipementsComponent_Template_input_input_43_listener", "EquipementsComponent_div_47_Template", "EquipementsComponent_tr_72_Template", "EquipementsComponent_nav_73_Template", "EquipementsComponent_Template_div_click_83_listener", "EquipementsComponent_Template_div_click_84_listener", "stopPropagation", "EquipementsComponent_Template_span_click_85_listener", "EquipementsComponent_Template_form_ngSubmit_89_listener", "EquipementsComponent_Template_button_click_113_listener", "_r0", "ɵɵpureFunction1", "_c1", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.html"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\nimport { Panne } from './Panne';\nimport { EtatEqui } from './EtatEqui';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\n\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\npanne:null\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\npanne:null\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\n// Propriétés pour les pannes\nselectedPanne: Panne = new Panne();\nshowPanneModal: boolean = false;\npanneForm: FormGroup;\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[]; \n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) {\n  // Initialisation du formulaire de panne\n  this.panneForm = this.fb.group({\n    titre: ['', [Validators.required, Validators.minLength(3)]],\n    description: ['', [Validators.required, Validators.minLength(10)]],\n    priorite: ['MOYENNE', [Validators.required]],\n\n  });\n}\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\n\n\n\n\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\n\n\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\n\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n\n\n\n\n\n\n\n\n \n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n\n\n\n\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n  equiements.forEach(eq => {\n   \n    this.idsEqui[eq.idEqui]=eq.idEqui;\n     })\n     console.log(this.idsEqui); \n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n  \n\n\n\n\n\n   \n\n\n\n\n\n\n\n    \n\n\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n \n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n\n  openPanneModal(equipement: Equip) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n\n  /**\n   * Fermer le modal de panne\n   */\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n\n  /**\n   * Fermer le modal en cliquant à l'extérieur\n   */\n  closeOnOutsideClick(event: any) {\n    if (event.target.classList.contains('modal')) {\n      this.closePanneModal();\n    }\n  }\n\n  onSubmitPanne() {\nconst Etat={\ntitre\n\n\n\n\n}\n\n\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement,\n\n      };\n\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: (response: any) => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          this.loadEquipements(this.currentPage);\n        },\n        error: (error: any) => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n        }\n      });\n    }\n  }\n\n}\n\n  \n\n\n\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Équipements</h2>\n    <p>Gérez les différents types d'équipements informatiques\n\n</p>\n  </div>\n<button class=\"add-user-btn\" >\n  <span class=\"icon\">+</span>Nouvel Panne \n\n</button>\n</div>\n\n<!-- Formulaire de recherche simple -->\n<div class=\"card mt-3 mb-4\">\n  <div class=\"card-body\">\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\n\n    <div class=\"row g-3\">\n      <!-- Recherche par utilisateur -->\n      <div class=\"col-md-6\">\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n          <mat-label>Utilisateur</mat-label>\n          <input\n            type=\"text\"\n            matInput\n            [formControl]=\"utilisateurSearchCtrl\"\n            [matAutocomplete]=\"autoUserSearch\"\n            placeholder=\"Rechercher un utilisateur...\">\n\n          <mat-autocomplete\n            #autoUserSearch=\"matAutocomplete\"\n            [displayWith]=\"displayUtilisateur\"\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n            </mat-option>\n          </mat-autocomplete>\n        </mat-form-field>\n      </div>\n\n      <!-- Recherche par statut -->\n\n</div>\n  </div>\n</div>\n\n<div class=\"search-wrapper\">\n  <div class=\"custom-search\">\n    <input\n      type=\"text\"\n      placeholder=\"Rechercher un equipement...\"\n      [(ngModel)]=\"searchTerm\"\n      (input)=\"onSearchChange()\"\n      class=\"form-control\"\n    />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n\n</div>\n<!-- Modal -->\n\n<!-- Modal de modification -->\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<style>\n    .card-custom {\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    }\n\n    .btn-outline-lightblue {\n      border: 1px solid #cfe2ff;\n      color: #0d6efd;\n      background-color: #e7f1ff;\n    }\n\n    .tag {\n      background-color: #e7f1ff;\n      color: #0d6efd;\n      padding: 3px 10px;\n      font-size: 0.8rem;\n      border-radius: 15px;\n      position: absolute;\n      right: 20px;\n      top: 20px;\n    }\n\n.icon-box {\n  font-size: 48px; /* optional - for icon size */\n  width: 100px;     /* increase width */\n  height: 100px;    /* set height */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #0d6efd;\n  margin-right: 10px;\n border-radius: 0% !important;\n}\n\n    .btn-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n.card-custom {\n  border-radius: 12px;\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\n  background-color: #fff;\n  color: #212529; /* Darker text */\n  font-size: 0.95rem; /* Slightly larger base font */\n}\n\n.card-custom strong {\n  font-weight: 600; /* Heavier for labels */\n  color: #1a1a1a;\n}\n\n.card-custom h5 {\n  font-weight: 600;\n  color: #000;\n}\n\n.card-custom small,\n.text-muted {\n  color: #495057 !important; /* Less faded gray */\n}\n\n.icon-box {\n  font-size: 32px;\n  color: #0d6efd;\n  margin-right: 10px;\n}\n\n.tag {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n  padding: 3px 10px;\n  font-size: 0.8rem;\n  border-radius: 15px;\n  position: absolute;\n  right: 20px;\n  top: 20px;\n}\n\n\n\n  </style>\n\n<body class=\"bg-light\">\n  <div class=\"container my-2\">\n\n    <!-- Simple Notification Bar -->\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\n      {{ notification.message }}\n    </div>\n\n    <!-- Tableau des équipements -->\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <div class=\"table-responsive mt-1\">\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Etat</th>\n                     <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr *ngFor=\"let equip of equiements\">\n                      <!-- Image -->\n                      <td class=\"px-1\">\n                        <img [src]=\"equip.image\"\n                             alt=\"Équipement\"\n                             class=\"rounded-circle img-fluid\"\n                             width=\"40\" height=\"40\" />\n                      </td>\n\n                      <!-- Modèle -->\n                      <td class=\"px-1\">\n                        <div class=\"ms-3\">\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\n                          <span class=\"fw-normal text-muted\">Modèle</span>\n                        </div>\n                      </td>\n\n                      <!-- Numéro de série -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\n                      </td>\n\n                      <!-- Date d'acquisition -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\n                      </td>\n\n                      <!-- Statut -->\n                      <td class=\"px-1\">\n                        <span class=\"badge rounded-pill\"\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\n                              [style.color]=\"'white'\">\n                          {{ equip.statut }}\n                        </span>\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\n                             class=\"text-muted small mt-1\">\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\n                        </div>\n                      </td>\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.panne?.etatActuel?.titre }}</span>\n                      </td>\n \n            \n                      \n\n                      <!-- Actions -->\n                      <td class=\"px-1 text-end\">\n                        <button class=\"btn btn-dark btn-sm\"\n                                (click)=\"openPanneModal(equip)\"\n                                title=\"Déclarer une panne\">\n                          <i class=\"\"></i>Declare Panne\n                        </button>\n                      </td>\n\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n    <!-- Pagination Bootstrap -->\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\n  <ul class=\"pagination\">\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\n    </li>\n\n    <li class=\"page-item\"\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\n        [class.active]=\"i === currentPage\">\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\n    </li>\n\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\n    </li>\n  </ul>\n</nav>\n\n  </div>\n</body>\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            \n          <div class=\"py-6 px-6 text-center\">\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal de déclaration de panne -->\n  <div class=\"modal\" [ngClass]=\"{'show': showPanneModal}\" (click)=\"closeOnOutsideClick($event)\">\n    <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n      <span class=\"close\" (click)=\"closePanneModal()\">&times;</span>\n      <h3 style=\"font-size: 20px; margin-bottom: -10px;\">Déclarer une panne</h3>\n\n      <form [formGroup]=\"panneForm\" (ngSubmit)=\"onSubmitPanne()\" novalidate>\n        <br>\n\n        <!-- Équipement -->\n        <p style=\"font-size: 14px; color: #666; margin-bottom: 15px;\">\n          <strong>Équipement:</strong> {{ selectedPanne.equipement?.model?.nomModel }} - {{ selectedPanne.equipement?.numSerie }}\n        </p>\n\n        <!-- Titre -->\n        <label style=\"font-size: 14px; font-weight: 500; color: #000000; margin-bottom: -40px\" for=\"titre\">Titre de la panne</label>\n        <input\n          class=\"form-inputp\"\n          id=\"titre\"\n          type=\"text\"\n          formControlName=\"titre\"\n          placeholder=\"Ex: Écran ne s'allume plus\"\n          required>\n\n        <!-- Priorité -->\n        <label style=\"font-size: 14px; font-weight: 500; color: #000000; margin-bottom: -40px\" for=\"priorite\">Priorité</label>\n        <select\n          class=\"form-inputp\"\n          id=\"priorite\"\n          formControlName=\"priorite\"\n          required>\n          <option value=\"FAIBLE\">Faible</option>\n          <option value=\"MOYENNE\">Moyenne</option>\n          <option value=\"HAUTE\">Haute</option>\n          <option value=\"CRITIQUE\">Critique</option>\n        </select>\n\n        <!-- Description -->\n        <label style=\"font-size: 14px; font-weight: 500; color: #000000; margin-bottom: -40px\" for=\"description\">Description</label>\n        <textarea\n          class=\"form-inputp\"\n          id=\"description\"\n          formControlName=\"description\"\n          rows=\"4\"\n          placeholder=\"Décrivez le problème...\"\n          required></textarea>\n\n        <div class=\"form-buttons\">\n          <button type=\"button\" class=\"btn-cancel\" (click)=\"closePanneModal()\">Annuler</button>\n          <button type=\"submit\" class=\"btn-submit\" [disabled]=\"!panneForm.valid\">Déclarer</button>\n        </div>\n      </form>\n    </div>\n  </div>\n\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\n  <script src=\"./assets/js/app.min.js\"></script>\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\n  <script src=\"./assets/js/dashboard.js\"></script>\n  <!-- solar icons -->\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\n</body>\n\n</html>"], "mappings": "AAEA,SAASA,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAG7E,SAASC,KAAK,QAAQ,SAAS;;;;;;;;;;;;;;ICsDnBC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,SAAA,OAAAH,OAAA,CAAAI,QAAA,SAAAJ,OAAA,CAAAK,KAAA,MACF;;;;;IA2JRV,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFb,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAH,MAAA,CAAAC,YAAA,CAAAG,OAAA,MACF;;;;;IAwDoBf,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,0BAAAd,EAAA,CAAAgB,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,QAAA,CAAAC,MAAA,QACF;;;;;;IArCJpB,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAAqB,SAAA,cAG8B;IAChCrB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,aAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,aAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,cAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAsB,UAAA,KAAAC,0CAAA,kBAGM;IACRvB,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAOrEH,EAAA,CAAAC,cAAA,cAA0B;IAEhBD,EAAA,CAAAwB,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAT,QAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAA9B,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAd,QAAA,CAAqB;IAAA,EAAC;IAErCnB,EAAA,CAAAqB,SAAA,aAAgB;IAAArB,EAAA,CAAAE,MAAA,sBAClB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAjDJH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAe,QAAA,CAAAe,KAAA,EAAAlC,EAAA,CAAAmC,aAAA,CAAmB;IASYnC,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAoC,iBAAA,CAAAjB,QAAA,CAAAkB,KAAA,kBAAAlB,QAAA,CAAAkB,KAAA,CAAAC,QAAA,CAA2B;IAOvCtC,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAoC,iBAAA,CAAAjB,QAAA,CAAAoB,QAAA,CAAoB;IAKpBvC,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAAwC,WAAA,SAAArB,QAAA,CAAAsB,eAAA,gBAAgD;IAMlEzC,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAA0C,WAAA,qBAAAvB,QAAA,CAAAwB,MAAA,gCAAAxB,QAAA,CAAAwB,MAAA,uCAA2H;IAE/H3C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAK,QAAA,CAAAwB,MAAA,MACF;IACM3C,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAe,QAAA,CAAAwB,MAAA,CAAAC,WAAA,oBAAAzB,QAAA,CAAAwB,MAAA,CAAAC,WAAA,sBAA0F;IAMxE5C,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAoC,iBAAA,CAAAjB,QAAA,CAAA0B,KAAA,kBAAA1B,QAAA,CAAA0B,KAAA,CAAAC,UAAA,kBAAA3B,QAAA,CAAA0B,KAAA,CAAAC,UAAA,CAAAC,KAAA,CAAoC;;;;;;IAiChF/C,EAAA,CAAAC,cAAA,aAEuC;IAChBD,EAAA,CAAAwB,UAAA,mBAAAwB,6DAAA;MAAA,MAAAtB,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAsB,IAAA;MAAA,MAAAC,KAAA,GAAAxB,WAAA,CAAAyB,KAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAoB,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAAClD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAAsD,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACcxD,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAoC,iBAAA,CAAAc,KAAA,KAAW;;;;;;;;;IATnElD,EAAA,CAAAC,cAAA,cAAuE;IAG5CD,EAAA,CAAAwB,UAAA,mBAAAiC,wDAAA;MAAAzD,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA2B,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAsB,UAAA,IAAAsC,yCAAA,iBAIK;IAEL5D,EAAA,CAAAC,cAAA,aAAwE;IACjDD,EAAA,CAAAwB,UAAA,mBAAAqC,wDAAA;MAAA7D,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAI,OAAA,GAAA9D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA8B,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,OAAoC;IAKrCxD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,MAAA,CAAAI,UAAA,EAA+B;IAK9BnE,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAI,UAAA,KAAiD;;;;;;;;ADhT3E,OAAM,MAAOC,oBAAoB;EA+EjCF,YACUG,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA/E5B,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAApE,YAAY,GAAG;MACbqE,IAAI,EAAE,KAAK;MACXpE,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;IACD,KAAAyC,WAAW,GAAG,CAAC;IACf,KAAA0B,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBlE,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChB5C,KAAK,EAAC;KAEL;IACD,KAAA6C,cAAc,GAAO;MACrBtE,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC,IAAI;MAChB5C,KAAK,EAAC;KAEL;IAID,KAAA8C,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdpD,eAAe,EAAC,IAAI8C,IAAI,EAAE;MAC1BO,IAAI,EAAC,IAAIrG,WAAW,EAAE;MACtBsG,UAAU,EAAC,IAAIzG,KAAK,EAAE;MACtB0G,MAAM,EAAC;KAER;IACD,KAAA9E,eAAe,GAAU,EAAE;IAC3B,KAAA+E,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B;IACA,KAAAC,aAAa,GAAU,IAAIpG,KAAK,EAAE;IAClC,KAAAqG,cAAc,GAAY,KAAK;IAG/B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAIhH,WAAW,EAAE;IACnC,KAAAiH,qBAAqB,GAAG,IAAIjH,WAAW,EAAE;IACzC,KAAAkH,SAAS,GAAG,IAAIlH,WAAW,EAAE;IAgJ/B,KAAAmH,cAAc,GAAW,EAAE,CAAC,CAAC;IA+J7B,KAAAC,YAAY,GAAQ,EAAE;IAxSpB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACrC,EAAE,CAACsC,KAAK,CAAC;MAC7B9D,KAAK,EAAE,CAAC,EAAE,EAAE,CAACvD,UAAU,CAACsH,QAAQ,EAAEtH,UAAU,CAACuH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DvB,WAAW,EAAE,CAAC,EAAE,EAAE,CAAChG,UAAU,CAACsH,QAAQ,EAAEtH,UAAU,CAACuH,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClEC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAACxH,UAAU,CAACsH,QAAQ,CAAC;KAE5C,CAAC;EACJ;EACEG,QAAQA,CAAA;IACJ,IAAI,CAACzD,WAAW,GAAG,CAAC;IAEtB,IAAI,CAAC0D,YAAY,EAAE;IACnB,IAAI,CAAC7D,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAAC2D,cAAc,EAAE;IAUzB;IACA,IAAI,CAACV,SAAS,CAACW,YAAY,CACxBC,IAAI,CACH3H,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyH,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACnD,WAAW,CAACoD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO3H,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8H,SAAS,CAACjD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IAIA;IACA,IAAI,CAAC+B,qBAAqB,CAACY,YAAY,CACpCC,IAAI,CACH3H,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEwH,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACf,qBAAqB,CAACmB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAACvE,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFxD,SAAS,CAACyH,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACnD,WAAW,CAACwD,iBAAiB,CAAC,IAAI,CAAC1C,UAAU,EAAC,IAAI,CAACqB,qBAAqB,CAACc,KAAK,EAAC,CAAC,EAAC,IAAI,CAACpC,QAAQ,CAAC,CAACwC,SAAS,CAAC;YAC7GI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;cAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;cAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;YACzC,CAAC;YACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACgB,WAAW,CAACgE,WAAW,CAACf,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAO3H,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8H,SAAS,CAACY,KAAK,IAAG;MACjB,IAAI,CAACzD,0BAA0B,GAAGyD,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAC,kBAAkBA,CAACzC,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAACtF,SAAS,IAAIsF,IAAI,CAACrF,QAAQ,MAAMqF,IAAI,CAACpF,KAAK,EAAE,GAAG,EAAE;EACzE;EAGA8H,YAAYA,CAACnG,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACC,QAAQ,EAAE,GAAG,EAAE;EACzC;EAMC6E,cAAcA,CAAA;IAGb,IAAI,CAAC9C,WAAW,CAACoE,iBAAiB,EAAE,CAACf,SAAS,CAACgB,IAAI,IAAG;MACtD,IAAI,CAACpC,YAAY,GAAGoC,IAAI;IAE1B,CAAC,CAAC;EAGA;EAYAC,oBAAoBA,CAAC7C,IAAiB;IACpC,IAAI,CAACzC,eAAe,CAAC,CAAC,CAAC;EAEzB;EAQFA,eAAeA,CAACuF,IAAY;IAC1B,IAAI,CAACpF,WAAW,GAAGoF,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAAC1D,UAAU,CAACoC,IAAI,EAAE;IACtC,MAAM5E,MAAM,GAAG,IAAI,CAAC+D,cAAc,CAACa,IAAI,EAAE;IAEzC,IAAIuB,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAACvC,qBAAqB,CAACc,KAAK;IAEhD,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAACxB,IAAI,EAAE;KAC1B,MAAM,IAAIwB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACvB,IAAI,EAAE;;IAGpC;IACA,IAAIuB,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAACzE,WAAW,CAACwD,iBAAiB,CAACgB,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QACzFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAAC2E,kBAAkB,CAAC,EAAE,EAAErG,MAAM,EAAEiG,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAACwD,iBAAiB,CAACgB,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAAC2E,kBAAkB,CAACH,OAAO,EAAElG,MAAM,EAAEiG,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAClFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAAC9D,WAAW,CAAC4E,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;MAChEI,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;QAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;MACzC,CAAC;MACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACvD,UAAiB;IACzC0D,OAAO,CAACc,GAAG,CAACxE,UAAU,CAAC;IACvBA,UAAU,CAACyE,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAACnD,OAAO,CAACmD,EAAE,CAAChI,MAAM,CAAC,GAACgI,EAAE,CAAChI,MAAM;IAChC,CAAC,CAAC;IACFgH,OAAO,CAACc,GAAG,CAAC,IAAI,CAACjD,OAAO,CAAC;IAC1B,IAAI,CAAC5B,WAAW,CAACgF,oBAAoB,CAAC,IAAI,CAACpD,OAAO,CAAC,CAACyB,SAAS,CAACgB,IAAI,IAAG;MAEnEA,IAAI,CAACS,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAACpD,gBAAgB,CAACoD,WAAW,CAACvD,UAAU,CAAC3E,MAAM,CAAC,GAAGkI,WAAW;QAClE,IAAI,CAACpI,eAAe,CAACoI,WAAW,CAACvD,UAAU,CAAC3E,MAAM,CAAC,GAAGkI,WAAW,CAACxD,IAAI,CAACtF,SAAS,GAAG,GAAG,GAAG8I,WAAW,CAACxD,IAAI,CAACrF,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGE8I,cAAcA,CAAA;IAEZ,IAAI,CAAClG,eAAe,CAAC,CAAC,CAAC;EACzB;EAuBFmG,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,IAAI,CAACpF,IAAI,CAAC0F,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACnC,SAAS,CACpEuC,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBF,QAAQ,CAACC,QAAQ,EAAE;UAC3D9B,OAAO,CAACc,GAAG,CAAC,mBAAmB,EAAEiB,OAAO,CAAC;UAGzC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC;YACnBnI,KAAK,EAAEiI;WACR,CAAC;SACH,MAAM;UACL/B,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEoC,WAAWA,CAAA;IACT,IAAI,CAAC3D,YAAY,GAAG,EAAE;EACxB;EAEIO,YAAYA,CAAA;IAEZ,IAAI,CAAC7C,WAAW,CAACkG,WAAW,EAAE,CAAC7C,SAAS,CAACgB,IAAI,IAAG;MAChD,IAAI,CAACjE,MAAM,GAAGiE,IAAI;IAEpB,CAAC,CAAC;EACF;EAMF;EACA8B,gBAAgBA,CAAC3J,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBqE,IAAI,EAAE,IAAI;MACVpE,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAED;IACA0J,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC9J,YAAY,CAACqE,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACA0F,kBAAkBA,CAACC,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMC,OAAO,GAAG,IAAItF,IAAI,CAACqF,IAAI,CAAC;MAC9B,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAO/C,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAgD,QAAQA,CAACtC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAACzE,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAACuF,IAAI,CAAC;;EAE9B;EAEAuC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC3H,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA4H,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC5H,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACA6H,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAClI,WAAW,GAAGiI,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAC1H,UAAU,GAAG,CAAC,EAAEqH,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;EAGArJ,cAAcA,CAAC8D,UAAiB;IAC9B,IAAI,CAACI,aAAa,GAAG,IAAIpG,KAAK,EAAE;IAChC,IAAI,CAACoG,aAAa,CAACJ,UAAU,GAAGA,UAAU;IAC1C,IAAI,CAACa,SAAS,CAACoF,KAAK,EAAE;IACtB,IAAI,CAACpF,SAAS,CAACyD,UAAU,CAAC;MACxBrD,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAACZ,cAAc,GAAG,IAAI;EAC5B;EAEA;;;EAGA6F,eAAeA,CAAA;IACb,IAAI,CAAC7F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACD,aAAa,GAAG,IAAIpG,KAAK,EAAE;IAChC,IAAI,CAAC6G,SAAS,CAACoF,KAAK,EAAE;EACxB;EAEA;;;EAGAE,mBAAmBA,CAACzC,KAAU;IAC5B,IAAIA,KAAK,CAACE,MAAM,CAACwC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC5C,IAAI,CAACH,eAAe,EAAE;;EAE1B;EAEAI,aAAaA,CAAA;IACf,MAAMC,IAAI,GAAC;MACXvJ;KAKC;IAGG,IAAI,IAAI,CAAC6D,SAAS,CAAC2F,KAAK,EAAE;MACxB,MAAMC,SAAS,GAAG;QAChBzJ,KAAK,EAAE,IAAI,CAAC6D,SAAS,CAAC6F,GAAG,CAAC,OAAO,CAAC,EAAEnF,KAAK;QACzC9B,WAAW,EAAE,IAAI,CAACoB,SAAS,CAAC6F,GAAG,CAAC,aAAa,CAAC,EAAEnF,KAAK;QACrDN,QAAQ,EAAE,IAAI,CAACJ,SAAS,CAAC6F,GAAG,CAAC,UAAU,CAAC,EAAEnF,KAAK;QAC/CvB,UAAU,EAAE,IAAI,CAACI,aAAa,CAACJ;OAEhC;MAED,IAAI,CAAC1B,WAAW,CAACqI,aAAa,CAACF,SAAS,CAAC,CAAC9E,SAAS,CAAC;QAClDI,IAAI,EAAGmC,QAAa,IAAI;UACtB7B,OAAO,CAACc,GAAG,CAAC,6BAA6B,EAAEe,QAAQ,CAAC;UACpD,IAAI,CAACgC,eAAe,EAAE;UACtB,IAAI,CAAC5I,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;QACxC,CAAC;QACD0E,KAAK,EAAGA,KAAU,IAAI;UACpBE,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QACjE;OACD,CAAC;;EAEN;;;uBA7hBW9D,oBAAoB,EAAApE,EAAA,CAAA2M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7M,EAAA,CAAA2M,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA/M,EAAA,CAAA2M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjN,EAAA,CAAA2M,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApB/I,oBAAoB;MAAAgJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC1BjC1N,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAqB,SAAA,cAAsB;UAEtBrB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAqB,SAAA,iBAAyB;UAIrBrB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAqB,SAAA,iBAES;UAETrB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAA8B;UACTD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAqB,SAAA,iBAK6C;UAE7CrB,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAAwB,UAAA,4BAAAoM,0EAAAC,MAAA;YAAA,OAAkBF,GAAA,CAAAhF,oBAAA,CAAAkF,MAAA,CAAAC,MAAA,CAAAxG,KAAA,CAAyC;UAAA,EAAC;UAC5DtH,EAAA,CAAAsB,UAAA,KAAAyM,2CAAA,yBAEa;UACf/N,EAAA,CAAAG,YAAA,EAAmB;UAU7BH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAAwB,UAAA,2BAAAwM,8DAAAH,MAAA;YAAA,OAAAF,GAAA,CAAAxI,UAAA,GAAA0I,MAAA;UAAA,EAAwB,mBAAAI,sDAAA;YAAA,OACfN,GAAA,CAAApE,cAAA,EAAgB;UAAA,EADD;UAH1BvJ,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAqB,SAAA,gBAAiC;UACnCrB,EAAA,CAAAG,YAAA,EAAM;UAkIRH,EAAA,CAAAC,cAAA,gBAAuB;UAInBD,EAAA,CAAAsB,UAAA,KAAA4M,oCAAA,kBAEM;UAGNlO,EAAA,CAAAC,cAAA,cAA6B;UAS6BD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAiD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGhEH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAsB,UAAA,KAAA6M,mCAAA,mBAuDK;UACPnO,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAsB,UAAA,KAAA8M,oCAAA,kBAgBM;UAEJpO,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,eAAiB;UAGMD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAC,cAAA,aACW;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAAkD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAQnKH,EAAA,CAAAC,cAAA,eAA8F;UAAtCD,EAAA,CAAAwB,UAAA,mBAAA6M,oDAAAR,MAAA;YAAA,OAASF,GAAA,CAAAzB,mBAAA,CAAA2B,MAAA,CAA2B;UAAA,EAAC;UAC3F7N,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAwB,UAAA,mBAAA8M,oDAAAT,MAAA;YAAA,OAASA,MAAA,CAAAU,eAAA,EAAwB;UAAA,EAAC;UAC3DvO,EAAA,CAAAC,cAAA,gBAAgD;UAA5BD,EAAA,CAAAwB,UAAA,mBAAAgN,qDAAA;YAAA,OAASb,GAAA,CAAA1B,eAAA,EAAiB;UAAA,EAAC;UAACjM,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC9DH,EAAA,CAAAC,cAAA,cAAmD;UAAAD,EAAA,CAAAE,MAAA,+BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE1EH,EAAA,CAAAC,cAAA,gBAAsE;UAAxCD,EAAA,CAAAwB,UAAA,sBAAAiN,wDAAA;YAAA,OAAYd,GAAA,CAAAtB,aAAA,EAAe;UAAA,EAAC;UACxDrM,EAAA,CAAAqB,SAAA,UAAI;UAGJrB,EAAA,CAAAC,cAAA,aAA8D;UACpDD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,IAC/B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGJH,EAAA,CAAAC,cAAA,iBAAmG;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5HH,EAAA,CAAAqB,SAAA,iBAMW;UAGXrB,EAAA,CAAAC,cAAA,iBAAsG;UAAAD,EAAA,CAAAE,MAAA,qBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtHH,EAAA,CAAAC,cAAA,mBAIW;UACcD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACtCH,EAAA,CAAAC,cAAA,mBAAwB;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpCH,EAAA,CAAAC,cAAA,mBAAyB;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI5CH,EAAA,CAAAC,cAAA,kBAAyG;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5HH,EAAA,CAAAqB,SAAA,qBAMsB;UAEtBrB,EAAA,CAAAC,cAAA,gBAA0B;UACiBD,EAAA,CAAAwB,UAAA,mBAAAkN,wDAAA;YAAA,OAASf,GAAA,CAAA1B,eAAA,EAAiB;UAAA,EAAC;UAACjM,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,mBAAuE;UAAAD,EAAA,CAAAE,MAAA,sBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;;UApVtFH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAAuN,GAAA,CAAAnH,qBAAA,CAAqC,oBAAAmI,GAAA;UAMrC3O,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAuN,GAAA,CAAApF,kBAAA,CAAkC;UAELvI,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAuN,GAAA,CAAA9I,0BAAA,CAA6B;UAkBhE7E,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAAuN,GAAA,CAAAxI,UAAA,CAAwB;UA2IpBnF,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAAuN,GAAA,CAAA/M,YAAA,CAAAqE,IAAA,CAAuB;UAwBSjF,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAAuN,GAAA,CAAAjJ,UAAA,CAAa;UAmEN1E,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAuN,GAAA,CAAAxJ,UAAA,KAAoB;UAoChDnE,EAAA,CAAAM,SAAA,IAAoC;UAApCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA4O,eAAA,KAAAC,GAAA,EAAAlB,GAAA,CAAAvH,cAAA,EAAoC;UAK7CpG,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,cAAAuN,GAAA,CAAA/G,SAAA,CAAuB;UAKI5G,EAAA,CAAAM,SAAA,GAC/B;UAD+BN,EAAA,CAAA8O,kBAAA,MAAAnB,GAAA,CAAAxH,aAAA,CAAAJ,UAAA,kBAAA4H,GAAA,CAAAxH,aAAA,CAAAJ,UAAA,CAAA1D,KAAA,kBAAAsL,GAAA,CAAAxH,aAAA,CAAAJ,UAAA,CAAA1D,KAAA,CAAAC,QAAA,SAAAqL,GAAA,CAAAxH,aAAA,CAAAJ,UAAA,kBAAA4H,GAAA,CAAAxH,aAAA,CAAAJ,UAAA,CAAAxD,QAAA,MAC/B;UAqC2CvC,EAAA,CAAAM,SAAA,IAA6B;UAA7BN,EAAA,CAAAI,UAAA,cAAAuN,GAAA,CAAA/G,SAAA,CAAA2F,KAAA,CAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
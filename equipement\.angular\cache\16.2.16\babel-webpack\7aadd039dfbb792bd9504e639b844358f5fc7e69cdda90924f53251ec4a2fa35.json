{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./utilisateur/utilisateur.service\";\nimport * as i2 from \"@angular/router\";\nexport class AppComponent {\n  constructor(utilisateurService, router) {\n    this.utilisateurService = utilisateurService;\n    this.router = router;\n    this.title = 'conseil';\n  }\n  ngOnInit() {\n    // Simplified: Only redirect to login if not authenticated and trying to access protected routes\n    if (!this.utilisateurService.isAuthenticated()) {\n      const currentUrl = this.router.url;\n      const publicRoutes = ['/utilisateur', '/motpasseoublie', '/reset-password', '/user-registration'];\n      // Only redirect to login if not on a public route\n      if (!publicRoutes.includes(currentUrl)) {\n        console.log('User not authenticated, redirecting to login');\n        this.router.navigate(['/utilisateur']);\n      }\n    } else {\n      console.log('User is authenticated, allowing navigation to:', this.router.url);\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.UtilisateurService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "utilisateurService", "router", "title", "ngOnInit", "isAuthenticated", "currentUrl", "url", "publicRoutes", "includes", "console", "log", "navigate", "i0", "ɵɵdirectiveInject", "i1", "UtilisateurService", "i2", "Router", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UtilisateurService } from './utilisateur/utilisateur.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'conseil';\r\n\r\n  constructor(\r\n    private utilisateurService: UtilisateurService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Simplified: Only redirect to login if not authenticated and trying to access protected routes\r\n    if (!this.utilisateurService.isAuthenticated()) {\r\n      const currentUrl = this.router.url;\r\n      const publicRoutes = ['/utilisateur', '/motpasseoublie', '/reset-password', '/user-registration'];\r\n\r\n      // Only redirect to login if not on a public route\r\n      if (!publicRoutes.includes(currentUrl)) {\r\n        console.log('User not authenticated, redirecting to login');\r\n        this.router.navigate(['/utilisateur']);\r\n      }\r\n    } else {\r\n      console.log('User is authenticated, allowing navigation to:', this.router.url);\r\n    }\r\n  }\r\n}\r\n", "<router-outlet> </router-outlet>"], "mappings": ";;;AASA,OAAM,MAAOA,YAAY;EAGvBC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAC,KAAK,GAAG,SAAS;EAKd;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACH,kBAAkB,CAACI,eAAe,EAAE,EAAE;MAC9C,MAAMC,UAAU,GAAG,IAAI,CAACJ,MAAM,CAACK,GAAG;MAClC,MAAMC,YAAY,GAAG,CAAC,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;MAEjG;MACA,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACH,UAAU,CAAC,EAAE;QACtCI,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3D,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;;KAEzC,MAAM;MACLF,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE,IAAI,CAACT,MAAM,CAACK,GAAG,CAAC;;EAElF;;;uBAtBWR,YAAY,EAAAc,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZnB,YAAY;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzBX,EAAA,CAAAa,SAAA,oBAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
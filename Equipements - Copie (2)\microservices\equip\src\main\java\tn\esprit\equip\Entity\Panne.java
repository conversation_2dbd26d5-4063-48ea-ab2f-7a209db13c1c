package tn.esprit.equip.Entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Panne {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String description;
    private String Titre;
    private String Priorite;

    @OneToOne
    Equipement equipement;




    @OneToOne
    @JoinColumn(name = "etat_id")
    private EtatEqui etatActuel;

}

package tn.esprit.equip.Entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class EtatEqui {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
@Enumerated(EnumType.STRING)
    private TypeEtat titre;

    private String responsable;

    @ManyToOne
    @JoinColumn(name = "precedent_id")
    private EtatEqui precedent;

}

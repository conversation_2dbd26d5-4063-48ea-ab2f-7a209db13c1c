{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Equip } from './equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from '../utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Historique } from './Historique';\nexport let EquipementComponent = class EquipementComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.isModalOpen = false;\n    this.isEditModalOpen = false;\n    this.isAffectationModalOpen = false;\n    this.isAffectationEditModalOpen = false;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.imagePreview = null;\n    this.selectedImage = null;\n    this.signupErrors = {};\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    this.form = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    // FormGroup pour la modification\n    this.editForm = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    this.affectationForm = this.fb.group({\n      user: [null, Validators.required],\n      equipement: [null],\n      commentaire: [''],\n      dateAffectation: [new Date()],\n      verrou: ['']\n    });\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    this.form.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire de modification\n    this.editForm.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    this.utilisateurCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateurs = users;\n    });\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  onModelSelected(model) {\n    this.newEquipement1.model = model;\n  }\n  onModelSelectedForAdd(model) {\n    this.form.patchValue({\n      model: model\n    });\n  }\n  onModelSelectedForEdit(model) {\n    this.editForm.patchValue({\n      model: model\n    });\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onModelInputChange(value) {\n    if (!value || typeof value === 'string') {\n      this.newEquipement1.model = null;\n    }\n  }\n  onUserSelected(user) {\n    if (this.isAffectationModalOpen) {\n      this.affectationForm.patchValue({\n        user: user\n      });\n    } else if (this.isAffectationEditModalOpen) {\n      this.EditedAffectation.user = user;\n    }\n    console.log('Utilisateur sélectionné:', user);\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  openModal1(equipement) {\n    const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\n    // Trouver le fournisseur correspondant dans la liste des fournisseurs\n    const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\n    this.newEquipement1 = {\n      ...equipement,\n      model: matchedModel ?? null\n    };\n    // Initialiser le formulaire de modification avec les données de l'équipement\n    this.editForm.patchValue({\n      model: this.newEquipement1.model,\n      numSerie: this.newEquipement1.numSerie,\n      description: this.newEquipement1.description,\n      dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\n      statut: this.newEquipement1.statut,\n      image: null,\n      fournisseurs: matchedFournisseur || null\n    });\n    console.log('Données équipement:', this.newEquipement1);\n    console.log('Fournisseur original:', this.newEquipement1.fournisseur);\n    console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\n    console.log('Date:', this.newEquipement1.dateAffectation);\n    this.modelCtrl.setValue(this.newEquipement1.model);\n    // Affiche la modale\n    this.isEditModalOpen = true;\n  }\n  onEditSubmit() {\n    this.submitted = true;\n    if (this.editForm.invalid) {\n      this.editForm.markAllAsTouched();\n      return;\n    }\n    const equipementData = {\n      ...this.editForm.value,\n      idEqui: this.newEquipement1.idEqui,\n      statut: this.newEquipement1.statut,\n      fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\n    };\n\n    this.authservice.updateEquip(equipementData).subscribe({\n      next: response => {\n        console.log('Update successful:', response);\n        this.showNotification('success', 'Équipement modifié avec succès');\n        this.closeEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const historique = new Historique();\n        historique.date = new Date();\n        historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Update failed:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'équipement');\n      }\n    });\n  }\n  updateData() {\n    console.log('Payload envoyé:', this.newEquipement1);\n    this.authservice.updateEquip(this.newEquipement1).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Équipement modifié avec succès');\n      this.closeModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\n    });\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  deleteEquip(id) {\n    this.authservice.deleteEquip(id).subscribe(() => {\n      this.showNotification('success', 'Équipement supprimé avec succès');\n      this.loadEquipements(this.currentPage);\n    });\n  }\n  enregistrerHistorique(messaege, idEquipement) {\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\n      this.NomEqui = data.equipement.model?.nomModel ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` : null;\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\n      const historique = new Historique();\n      historique.date = data.dateAffectation;\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\n      this.authservice.addHistorique(historique).subscribe({\n        next: response => {\n          console.log('Historique enregistré:', response);\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n    });\n  }\n  desaffecterEquipement(equip) {\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\n    if (isConfirmed) {\n      // Enregistrer l'historique AVANT de supprimer l'affectation\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\n        next: () => {\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\n            next: () => {\n              this.showNotification('success', 'Équipement désaffecté avec succès');\n              this.loadEquipements(this.currentPage);\n              window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n              });\n            },\n            error: error => {\n              console.error('Erreur lors du changement de statut:', error);\n              this.showNotification('error', 'Erreur lors du changement de statut');\n            }\n          });\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\n          this.showNotification('error', 'Échec de la désaffectation');\n        }\n      });\n    }\n  }\n  confirmDelete(ModelId) {\n    console.log(ModelId);\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\n    if (isConfirmed) {\n      this.deleteEquip(ModelId);\n    }\n  }\n  onAffectationSubmit() {\n    if (this.isAffectationModalOpen) {\n      this.handleNewAffectation();\n    } else if (this.isAffectationEditModalOpen) {\n      this.handleEditAffectation();\n    }\n  }\n  handleNewAffectation() {\n    this.affectationFormSubmitted = true;\n    if (!this.affectationForm.get('user')?.value) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    if (!this.selectedEquipement) {\n      console.error('Aucun équipement sélectionné');\n      return;\n    }\n    // S'assurer que l'équipement a le statut DISPONIBLE par défaut\n    this.selectedEquipement.statut = 'DISPONIBLE';\n    this.affectationForm.patchValue({\n      equipement: this.selectedEquipement\n    });\n    this.affectationForm.patchValue({\n      verrou: 'affecter'\n    });\n    console.log('Form Value:', this.affectationForm.value);\n    this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\n      next: response => {\n        console.log('Statut mis à jour avec succès:', response);\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour du statut:', error);\n      }\n    });\n    this.authservice.addAff(this.affectationForm.value).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation créée avec succès !');\n        this.closeAffectationModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const utilisateur = this.affectationForm.get('user')?.value;\n        const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\n        const numSerie = this.selectedEquipement?.numSerie || 'N/A';\n        const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\n      },\n      error: error => {\n        this.showNotification('error', 'Échec de la création de l\\'affectation');\n      }\n    });\n  }\n  handleEditAffectation() {\n    // Validate the edit form - only user is required\n    if (!this.EditedAffectation.user) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    const affectationData = {\n      ...this.EditedAffectation,\n      dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\n    };\n    console.log('Updating affectation:', affectationData);\n    this.authservice.updateAff(affectationData).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation modifiée avec succès !');\n        this.closeAffectationEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique manuellement\n        const historique = new Historique();\n        historique.date = new Date();\n        const equipementNom = this.selectedEquipement.model?.nomModel ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})` : 'Équipement inconnu';\n        const utilisateurNom = this.EditedAffectation.user ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        const dateFormatted = new Date().toLocaleDateString('fr-FR');\n        historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique de réaffectation enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Error updating affectation:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\n      }\n    });\n  }\n  onRegister() {\n    this.submitted = true;\n    console.log(this.form.value.model);\n    if (this.form.invalid) {\n      this.form.markAllAsTouched(); // 🔥 Triggers all error messages\n      return;\n    }\n    const historique = new Historique();\n    const equipementData = {\n      ...this.form.value,\n      statut: 'DISPONIBLE',\n      fournisseur: this.form.value.fournisseurs || null\n    };\n    console.log(equipementData);\n    this.authservice.addEquipement(equipementData).subscribe({\n      next: response => {\n        historique.date = new Date();\n        historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Équipement ajouté avec succès');\n        this.closeModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\n      },\n\n      error: error => {\n        console.error('Registration failed:', error);\n        alert('Échec de l’enregistrement');\n      }\n    });\n  }\n  onImageSelected(event) {\n    const file = event.target.files?.[0];\n    if (file) {\n      this.form.patchValue({\n        image: file\n      });\n      this.form.get('image')?.updateValueAndValidity();\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imagePreview = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n    this.resetForm();\n  }\n  closeEditModal() {\n    this.isEditModalOpen = false;\n    this.editForm.reset();\n    this.submitted = false;\n  }\n  closeOnOutsideClickEdit(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeEditModal();\n    }\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire\n  resetForm() {\n    this.form.reset();\n    // Réinitialiser avec le statut par défaut\n    this.form.patchValue({\n      statut: 'DISPONIBLE'\n    });\n    this.submitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"DISPONIBLE\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"DISPONIBLE\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n  }\n  // Méthodes pour l'affectation\n  openAffectationModal(equipement) {\n    this.selectedEquipement = equipement;\n    // Définir automatiquement le statut comme DISPONIBLE\n    this.selectedEquipement.statut = 'DISPONIBLE';\n    this.isAffectationModalOpen = true;\n    this.affectationFormSubmitted = false; // Reset submission state\n    // Réinitialiser le formulaire d'affectation\n    this.affectationForm.patchValue({\n      utilisateur: null,\n      equipement: this.selectedEquipement,\n      commentaire: '',\n      dateAffectation: new Date().toISOString().split('T')[0]\n    });\n  }\n  openEditedModal(equipement) {\n    // Set the selected equipment for the modal\n    this.selectedEquipement = equipement;\n    this.editAffectationFormSubmitted = false; // Reset submission state\n    this.isAffectationEditModalOpen = true;\n  }\n  closeAffectationModal() {\n    this.isAffectationModalOpen = false;\n    this.affectationForm.reset();\n  }\n  updateReaffication(equip) {\n    this.editAffectationFormSubmitted = true;\n    // Check if user is required and missing\n    if (!this.EditedAffectation.user) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    this.EditedAffectation.equipement = equip;\n    this.EditedAffectation.verrou = 'affecter';\n    this.authservice.updateAff(this.EditedAffectation).subscribe({\n      next: data => {\n        console.log(\"Affectation mise à jour avec succès\", data);\n        this.showNotification('success', 'Affectation modifiée avec succès !');\n        this.closeAffectationEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n      },\n\n      error: error => {\n        console.error(\"Erreur lors de la mise à jour de l'affectation\", error);\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\n      }\n    });\n  }\n  closeAffectationEditModal() {\n    this.isAffectationEditModalOpen = false;\n    this.utilisateurCtrl.setValue(null);\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip()\n    };\n  }\n  closeOnOutsideClickAffectation(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeAffectationModal();\n    }\n  }\n  closeOnOutsideClickAffectationEdit(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeAffectationEditModal();\n    }\n  }\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n};\nEquipementComponent = __decorate([Component({\n  selector: 'app-equipement',\n  templateUrl: './equipement.component.html',\n  styleUrls: ['./equipement.component.css']\n})], EquipementComponent);", "map": {"version": 3, "names": ["Component", "Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "Historique", "EquipementComponent", "constructor", "authservice", "http", "fb", "utilisateurService", "isModalOpen", "isEditModalOpen", "isAffectationModalOpen", "isAffectationEditModalOpen", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "notification", "show", "type", "message", "currentPage", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "idEqui", "numSerie", "statut", "image", "model", "dateAffectation", "Date", "description", "<PERSON><PERSON><PERSON><PERSON>", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "NameUtilisateur", "idsEqui", "tableAffectation", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "imagePreview", "selectedImage", "signupErrors", "ngOnInit", "GetAllModels", "loadEquipements", "getFournisseur", "form", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "editForm", "affectationForm", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "get", "searchUsers", "users", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "totalPages", "fetchUtilisateurs", "error", "err", "console", "displayUtilisateur", "firstName", "lastName", "email", "displayModel", "nomModel", "onModelSelected", "onModelSelectedForAdd", "patchValue", "onModelSelectedForEdit", "getallFournisseur", "data", "onModelInputChange", "onUserSelected", "log", "onUserSearchSelected", "closeOnOutsideClick", "event", "target", "classList", "contains", "closeModal", "openModal1", "matchedModel", "find", "m", "idModel", "matchedFournisseur", "f", "idFournisseur", "formatDateForInput", "onEditSubmit", "invalid", "mark<PERSON>llAsTouched", "equipementData", "updateEquip", "response", "showNotification", "closeEditModal", "historique", "date", "addHistorique", "updateData", "page", "keyword", "username", "userVal", "searchEquipements1", "getAllEquipements", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "deleteEquip", "enregistrerHistorique", "messaege", "idEquipement", "getAffectationById", "dateFormatted", "toLocaleDateString", "desaffecterEquipement", "equip", "isConfirmed", "window", "confirm", "deleteAff", "addStatutDisponible", "scrollTo", "top", "behavior", "confirmDelete", "ModelId", "onAffectationSubmit", "handleNewAffectation", "handleEditAffectation", "selectedEquipement", "addStatutAffecte", "addAff", "closeAffectationModal", "utilisateur", "equipementNom", "utilisateurNom", "affectationData", "updateAff", "closeAffectationEditModal", "onRegister", "addEquipement", "alert", "onImageSelected", "file", "files", "updateValueAndValidity", "reader", "FileReader", "onload", "result", "readAsDataURL", "onFileSelected", "formData", "FormData", "append", "post", "imageUrl", "fullUrl", "resetErrors", "getAllModel", "openModal", "resetForm", "reset", "closeOnOutsideClickEdit", "setTimeout", "hideNotification", "openAffectationModal", "toISOString", "split", "openEditedModal", "updateReaffication", "closeOnOutsideClickAffectation", "closeOnOutsideClickAffectationEdit", "date<PERSON><PERSON>j", "isNaN", "getTime", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\equipement\\equipement.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Equip } from './equip';\r\nimport { Model } from '../model/Model';\r\nimport { TypeService } from '../dashboard/type.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\r\nimport * as bootstrap from 'bootstrap';\r\n// or for just Modal:\r\nimport { Modal } from 'bootstrap';\r\nimport { Fournisseur } from '../fournisseur/Fournisseur';\r\nimport { Utilisateur } from '../utilisateur/utilisateur';\r\nimport { UtilisateurService } from '../utilisateur/utilisateur.service';\r\nimport { AffectationEquipement } from '../affecta/AffectationEquipement';\r\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\r\nimport { Affectation } from '../affecta/Affectation';\r\nimport { Historique } from './Historique';\r\n@Component({\r\n  selector: 'app-equipement',\r\n  templateUrl: './equipement.component.html',\r\n  styleUrls: ['./equipement.component.css']\r\n})\r\nexport class EquipementComponent implements OnInit {\r\nisModalOpen = false;\r\nisEditModalOpen = false;\r\nisAffectationModalOpen = false;\r\nisAffectationEditModalOpen = false;\r\nmodels:Model[]=[];\r\nequiements:Equip[]=[];\r\nutilisateurs: Utilisateur[] = [];\r\n\r\nfilteredUtilisateurs: Utilisateur[] = [];\r\nfilteredUtilisateursSearch: Utilisateur[] = [];\r\nmodelet: Model[] = [];\r\n  NomEqui:String|null=null;\r\n    NomUser:String|null=null;\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\ncurrentPage = 0;\r\npageSize = 4;\r\nsearchTerm: string = '';\r\n\r\n// Affectation form\r\naffectationForm!: FormGroup;\r\nselectedEquipement!: Equip \r\naffectationFormSubmitted = false;\r\neditAffectationFormSubmitted = false;\r\n\r\nnewEquipement:Equip={\r\nidEqui:0,\r\nnumSerie:\"\",\r\nstatut:\"\",\r\nimage:\"\",\r\nmodel:null,\r\ndateAffectation:new Date,\r\ndescription:\"\",\r\nfournisseur:null,\r\n\r\n};\r\nnewEquipement1:Equip={\r\nidEqui:0,\r\nnumSerie:\"\",\r\nstatut:\"\",\r\nimage:\"\",\r\nmodel:null,\r\ndateAffectation:new Date,\r\ndescription:\"\",\r\nfournisseur:null,\r\n\r\n};\r\nform!: FormGroup;\r\neditForm!: FormGroup;\r\n\r\nEditedAffectation:Affectation={\r\n  id:0,\r\n  commentaire:\"\",\r\n  dateAffectation:new Date(),\r\n  user:new Utilisateur(),\r\n  equipement:new Equip(),\r\n  verrou:\"\"\r\n\r\n}\r\nNameUtilisateur:string[]=[];\r\nidsEqui:number[]=[];\r\ntableAffectation: any = {};\r\n\r\nsubmitted = false;\r\nfournisseurs:Fournisseur[]=[]; \r\n  totalPages: any;\r\n  utilisateurCtrl = new FormControl();\r\n  utilisateurSearchCtrl = new FormControl();\r\n  modelCtrl = new FormControl();\r\nconstructor(\r\n  private authservice:TypeService,\r\n  private http:HttpClient,\r\n  private fb: FormBuilder,\r\n  private utilisateurService: UtilisateurService\r\n) { }\r\n  ngOnInit(): void {\r\n      this.currentPage = 0;\r\n\r\n    this.GetAllModels();\r\n    this.loadEquipements(this.currentPage);\r\n    this.getFournisseur();\r\n \r\n\r\nthis.form = this.fb.group({\r\n  model: [null, Validators.required],\r\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\r\n  description: [''], \r\n  dateAffectation: ['', Validators.required],\r\n  statut: ['DISPONIBLE'], \r\n  image: [null], \r\n  fournisseurs: [null, Validators.required]\r\n});\r\n\r\n// FormGroup pour la modification\r\nthis.editForm = this.fb.group({\r\n  model: [null, Validators.required],\r\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\r\n  description: [''], // Description optionnelle - pas de validation\r\n  dateAffectation: ['', Validators.required],\r\n  statut: ['DISPONIBLE'], // Statut par défaut, pas de validation requise\r\n  image: [null], // Image optionnelle - pas de validation\r\n  fournisseurs: [null, Validators.required]\r\n});\r\n\r\n\r\n\r\n\r\nthis.affectationForm = this.fb.group({\r\n  user: [null, Validators.required],\r\n  equipement: [null],\r\n  commentaire: [''], // No validation - optional\r\n  dateAffectation: [new Date()], // No validation - optional\r\n  verrou: ['']\r\n});\r\n\r\n\r\n\r\n// Autocomplete pour le modal de modification\r\nthis.modelCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n// Autocomplete pour le formulaire d'ajout\r\nthis.form.get('model')?.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n// Autocomplete pour le formulaire de modification\r\nthis.editForm.get('model')?.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchModels(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(models => {\r\n    this.modelet = models;\r\n  });\r\n\r\n\r\nthis.utilisateurCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    switchMap(value => {\r\n\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n          return this.authservice.searchUsers(value.trim());\r\n        } else {\r\n\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(users => {\r\n    this.filteredUtilisateurs = users;\r\n  });\r\n\r\n// Autocomplete pour la recherche\r\nthis.utilisateurSearchCtrl.valueChanges\r\n  .pipe(\r\n    debounceTime(300),\r\n    distinctUntilChanged(),\r\n    tap((value: any) => {\r\n\r\n      if (typeof value === 'string' && value.trim() === '') {\r\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\r\n        this.loadEquipements(0);\r\n      }\r\n    }),\r\n    switchMap(value => {\r\n      if (typeof value === 'string') {\r\n        if (value.trim().length > 0) {\r\n\r\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\r\n  next: (res) => {\r\n    this.equiements = res.content;\r\n    this.totalPages = res.totalPages;\r\n    this.fetchUtilisateurs(this.equiements);\r\n  },\r\n  error: (err) => console.error(err)\r\n});\r\nthis.loadEquipements(0);\r\n          \r\n          return this.authservice.searchUsers(value.trim());\r\n        } else {\r\n          return of([]);\r\n        }\r\n      }\r\n      return of([]);\r\n    })\r\n  )\r\n  .subscribe(users => {\r\n    this.filteredUtilisateursSearch = users;\r\n  });\r\n\r\n\r\n\r\n}\r\n\r\n\r\n\r\n\r\n  \r\n\r\ndisplayUtilisateur(user: any): string {\r\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\r\n}\r\n\r\n\r\ndisplayModel(model: any): string {\r\n  return  model? `${model.nomModel}` : '';\r\n}\r\n\r\nonModelSelected(model: any): void {\r\n  this.newEquipement1.model = model;\r\n}\r\n\r\nonModelSelectedForAdd(model: any): void {\r\n  this.form.patchValue({ model: model });\r\n}\r\n\r\nonModelSelectedForEdit(model: any): void {\r\n  this.editForm.patchValue({ model: model });\r\n}\r\n\r\n\r\n\r\n getFournisseur()\r\n  {\r\n\r\n  this.authservice.getallFournisseur().subscribe(data => {\r\n  this.fournisseurs = data;\r\n\r\n});\r\n\r\n\r\n  }\r\n\r\n  onModelInputChange(value: string) {\r\n\r\n  if (!value || typeof value === 'string') {\r\n    this.newEquipement1.model = null;\r\n  }\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n  onUserSelected(user: Utilisateur) {\r\n\r\n    if (this.isAffectationModalOpen) {\r\n\r\n      this.affectationForm.patchValue({\r\n        user: user\r\n      });\r\n    } else if (this.isAffectationEditModalOpen) {\r\n\r\n      this.EditedAffectation.user = user;\r\n    }\r\n    console.log('Utilisateur sélectionné:', user);\r\n  }\r\n\r\n  onUserSearchSelected(user: Utilisateur) {\r\n    this.loadEquipements(0);\r\n \r\n  }\r\n\r\n  closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\nopenModal1(equipement: Equip) {\r\n  const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\r\n\r\n  // Trouver le fournisseur correspondant dans la liste des fournisseurs\r\n  const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\r\n\r\n  this.newEquipement1 = {\r\n    ...equipement,\r\n    model: matchedModel ?? null\r\n  };\r\n\r\n  // Initialiser le formulaire de modification avec les données de l'équipement\r\n  this.editForm.patchValue({\r\n    model: this.newEquipement1.model,\r\n    numSerie: this.newEquipement1.numSerie,\r\n    description: this.newEquipement1.description,\r\n    dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\r\n    statut: this.newEquipement1.statut,\r\n    image: null,\r\n    fournisseurs: matchedFournisseur || null\r\n  });\r\n\r\n  console.log('Données équipement:', this.newEquipement1);\r\n  console.log('Fournisseur original:', this.newEquipement1.fournisseur);\r\n  console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\r\n  console.log('Date:', this.newEquipement1.dateAffectation);\r\n\r\n  this.modelCtrl.setValue(this.newEquipement1.model);\r\n\r\n  // Affiche la modale\r\n  this.isEditModalOpen = true;\r\n}\r\n\r\nonEditSubmit(): void {\r\n  this.submitted = true;\r\n\r\n  if (this.editForm.invalid) {\r\n    this.editForm.markAllAsTouched(); \r\n    return;\r\n  }\r\n\r\n  const equipementData = {\r\n    ...this.editForm.value,\r\n    idEqui: this.newEquipement1.idEqui, // Garder l'ID original\r\n    statut: this.newEquipement1.statut, // Préserver le statut original\r\n    fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\r\n  };\r\n\r\n  this.authservice.updateEquip(equipementData).subscribe({\r\n    next: (response) => {\r\n      console.log('Update successful:', response);\r\n      this.showNotification('success', 'Équipement modifié avec succès');\r\n      this.closeEditModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n      const historique = new Historique();\r\n      historique.date = new Date();\r\n      historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n    },\r\n    error: (error) => {\r\n      console.error('Update failed:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\r\n    }\r\n  });\r\n}\r\n\r\nupdateData() {\r\n  console.log('Payload envoyé:', this.newEquipement1);\r\n  this.authservice.updateEquip(this.newEquipement1).subscribe(\r\n    (response) => {\r\n      console.log('Update successful:', response);\r\n      this.showNotification('success', 'Équipement modifié avec succès');\r\n      this.closeModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n\r\n    },\r\n    (error) => {\r\n      console.error('Update failed:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\r\n    }\r\n  );\r\n}\r\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\r\n\r\nloadEquipements(page: number): void {\r\n  this.currentPage = page;\r\n\r\n  const keyword = this.searchTerm.trim();\r\n  const statut = this.selectedStatut.trim();\r\n\r\n  let username = '';\r\n  const userVal = this.utilisateurSearchCtrl.value;\r\n\r\n  if (typeof userVal === 'string') {\r\n    username = userVal.trim();\r\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\r\n    username = userVal.username.trim();\r\n  }\r\n\r\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\r\n  if (username !== '') {\r\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 2 : Statut seul (sans username)\r\n  if (keyword === '' && statut !== '') {\r\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 3 : Keyword seul (sans username ni statut)\r\n  if (keyword !== '' && statut === '') {\r\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 4 : Keyword + Statut (sans username)\r\n  if (keyword !== '' && statut !== '') {\r\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\r\n      next: (res) => {\r\n        this.equiements = res.content;\r\n        this.totalPages = res.totalPages;\r\n        this.fetchUtilisateurs(this.equiements);\r\n      },\r\n      error: (err) => console.error(err)\r\n    });\r\n    return;\r\n  }\r\n\r\n  // Cas 5 : Aucun filtre → tout afficher\r\n  this.authservice.getAllEquipements(page, this.pageSize).subscribe({\r\n    next: (res) => {\r\n      this.equiements = res.content;\r\n      this.totalPages = res.totalPages;\r\n      this.fetchUtilisateurs(this.equiements);\r\n    },\r\n    error: (err) => console.error(err)\r\n  });\r\n}\r\n\r\n\r\nprivate fetchUtilisateurs(equiements: any[]): void {\r\n  console.log(equiements);\r\n  equiements.forEach(eq => {\r\n   \r\n    this.idsEqui[eq.idEqui]=eq.idEqui;\r\n     })\r\n     console.log(this.idsEqui); \r\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\r\n\r\n      data.forEach(affectation => {\r\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\r\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\r\n      \r\n      });\r\n    });\r\n\r\n\r\n\r\n\r\n}\r\n\r\n\r\n  onSearchChange(): void {\r\n\r\n    this.loadEquipements(0);\r\n  }\r\n\r\n  \r\n   deleteEquip(id: number) {\r\n    this.authservice.deleteEquip(id).subscribe(() => {\r\n      this.showNotification('success', 'Équipement supprimé avec succès');\r\n      this.loadEquipements(this.currentPage); \r\n\r\n\r\n    });\r\n  }\r\n\r\n\r\n\r\n\r\n  private enregistrerHistorique(messaege: string, idEquipement: number) {\r\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\r\n    this.NomEqui = data.equipement.model?.nomModel \r\n  ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` \r\n  : null;\r\n\r\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\r\n\r\n      const historique = new Historique();\r\n      historique.date = data.dateAffectation;\r\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\r\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\r\n\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n    });\r\n  }\r\n\r\n  desaffecterEquipement(equip: Equip) {\r\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\r\n    if (isConfirmed) {\r\n      // Enregistrer l'historique AVANT de supprimer l'affectation\r\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\r\n\r\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\r\n        next: () => {\r\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\r\n            next: () => {\r\n              this.showNotification('success', 'Équipement désaffecté avec succès');\r\n              this.loadEquipements(this.currentPage);\r\n              window.scrollTo({ top: 0, behavior: 'smooth' });\r\n            },\r\n            error: (error) => {\r\n              console.error('Erreur lors du changement de statut:', error);\r\n              this.showNotification('error', 'Erreur lors du changement de statut');\r\n            }\r\n          });\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\r\n          this.showNotification('error', 'Échec de la désaffectation');\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n\r\n   confirmDelete(ModelId: number): void {\r\n    console.log(ModelId);\r\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\r\n    if (isConfirmed) {\r\n      this.deleteEquip(ModelId);\r\n    }\r\n  }\r\nonAffectationSubmit() {\r\n  \r\n  if (this.isAffectationModalOpen) {\r\n\r\n    this.handleNewAffectation();\r\n  } else if (this.isAffectationEditModalOpen) {\r\n   \r\n    this.handleEditAffectation();\r\n  }\r\n}\r\n\r\nprivate handleNewAffectation() {\r\n  this.affectationFormSubmitted = true;\r\n\r\n  if (!this.affectationForm.get('user')?.value) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  if (!this.selectedEquipement) {\r\n    console.error('Aucun équipement sélectionné');\r\n    return;\r\n  }\r\n\r\n  // S'assurer que l'équipement a le statut DISPONIBLE par défaut\r\n  this.selectedEquipement.statut = 'DISPONIBLE';\r\n  this.affectationForm.patchValue({ equipement: this.selectedEquipement });\r\n  this.affectationForm.patchValue({ verrou:'affecter' });\r\n\r\n  console.log('Form Value:', this.affectationForm.value);\r\n  this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\r\n    next: (response) => {\r\n      console.log('Statut mis à jour avec succès:', response);\r\n    },\r\n    error: (error) => {\r\n      console.error('Erreur lors de la mise à jour du statut:', error);\r\n    }\r\n  });\r\n  this.authservice.addAff(this.affectationForm.value).subscribe({\r\n    next: (response: any) => {\r\n      this.showNotification('success', 'Affectation créée avec succès !');\r\n      this.closeAffectationModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      // Enregistrer dans l'historique\r\n      const utilisateur = this.affectationForm.get('user')?.value;\r\n      const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\r\n      const numSerie = this.selectedEquipement?.numSerie || 'N/A';\r\n      const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\r\n      this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\r\n    },\r\n    error: (error) => {\r\n      this.showNotification('error', 'Échec de la création de l\\'affectation');\r\n    }\r\n  });\r\n}\r\n\r\nprivate handleEditAffectation() {\r\n  // Validate the edit form - only user is required\r\n  if (!this.EditedAffectation.user) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  \r\n  const affectationData = {\r\n    ...this.EditedAffectation,\r\n    dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\r\n  };\r\n\r\n  console.log('Updating affectation:', affectationData);\r\n\r\n  this.authservice.updateAff(affectationData).subscribe({\r\n    next: (response: any) => {\r\n      this.showNotification('success', 'Affectation modifiée avec succès !');\r\n      this.closeAffectationEditModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n\r\n      // Enregistrer dans l'historique manuellement\r\n      const historique = new Historique();\r\n      historique.date = new Date();\r\n\r\n      const equipementNom = this.selectedEquipement.model?.nomModel\r\n        ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})`\r\n        : 'Équipement inconnu';\r\n\r\n      const utilisateurNom = this.EditedAffectation.user\r\n        ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim()\r\n        : 'Utilisateur inconnu';\r\n\r\n      const dateFormatted = new Date().toLocaleDateString('fr-FR');\r\n      historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\r\n\r\n      this.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique de réaffectation enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\r\n        }\r\n      });\r\n    },\r\n    error: (error) => {\r\n      console.error('Error updating affectation:', error);\r\n      this.showNotification('error', 'Échec de la modification de l\\'affectation');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n\r\n\r\n    \r\n  onRegister(): void {\r\n  this.submitted = true;\r\n\r\nconsole.log(this.form.value.model);\r\nif (this.form.invalid) {\r\n    this.form.markAllAsTouched(); // 🔥 Triggers all error messages\r\n    return;\r\n  }\r\nconst historique = new Historique();\r\n\r\n  const equipementData = {\r\n    ...this.form.value,\r\n    statut: 'DISPONIBLE', \r\n    fournisseur: this.form.value.fournisseurs || null \r\n  };\r\nconsole.log(equipementData);\r\n  this.authservice.addEquipement(equipementData).subscribe({\r\n    next: (response) => {\r\n      historique.date = new Date();\r\n      historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\r\nthis.authservice.addHistorique(historique).subscribe({\r\n        next: (response) => {\r\n          console.log('Historique enregistré:', response);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\r\n        }\r\n      });\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Équipement ajouté avec succès');\r\n      this.closeModal();\r\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      alert('Échec de l’enregistrement');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n  imagePreview: string | ArrayBuffer | null = null;\r\nselectedImage: File | null = null;\r\n\r\nonImageSelected(event: Event): void {\r\n  const file = (event.target as HTMLInputElement).files?.[0];\r\n  if (file) {\r\n    this.form.patchValue({ image: file });\r\n    this.form.get('image')?.updateValueAndValidity();\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      this.imagePreview = reader.result;\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n}\r\n\r\n\r\n  \r\nonFileSelected(event: any) {\r\n  const file = event.target.files[0];\r\n\r\n  if (file) {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\r\n      (response) => {\r\n        if (response && response.imageUrl) {\r\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\r\n          console.log('Image URL saved: ', fullUrl);\r\n\r\n         \r\n          this.form.patchValue({\r\n            image: fullUrl\r\n          });\r\n        } else {\r\n          console.error('Invalid response from API');\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error during image upload', error);\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\n\r\n\r\nsignupErrors: any = {};\r\n    \r\n  resetErrors() {\r\n    this.signupErrors = {};\r\n  }\r\n\r\n      GetAllModels()\r\n    {\r\n      this.authservice.getAllModel().subscribe(data => {\r\n      this.models = data;\r\n   \r\n    });\r\n    }\r\n\r\n\r\n\r\n\r\n  openModal() {\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n    this.resetForm();\r\n  }\r\n\r\n  closeEditModal() {\r\n    this.isEditModalOpen = false;\r\n    this.editForm.reset();\r\n    this.submitted = false;\r\n  }\r\n\r\n  closeOnOutsideClickEdit(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeEditModal();\r\n    }\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n\r\n  // Méthode pour réinitialiser le formulaire\r\n  resetForm(): void {\r\n    this.form.reset();\r\n    // Réinitialiser avec le statut par défaut\r\n    this.form.patchValue({\r\n      statut: 'DISPONIBLE'\r\n    });\r\n    this.submitted = false;\r\n    this.newEquipement = {\r\n      idEqui: 0,\r\n      numSerie: \"\",\r\n      statut: \"DISPONIBLE\",\r\n      image: \"\",\r\n      model: null,\r\n      dateAffectation: new Date(),\r\n      description: \"\",\r\n      fournisseur:null\r\n    };\r\n    this.newEquipement1 = {\r\n      idEqui: 0,\r\n      numSerie: \"\",\r\n      statut: \"DISPONIBLE\",\r\n      image: \"\",\r\n      model: null,\r\n      dateAffectation: new Date(),\r\n      description: \"\",\r\n      fournisseur:null\r\n    };\r\n  }\r\n\r\n  // Méthodes pour l'affectation\r\n  openAffectationModal(equipement: Equip) {\r\n    this.selectedEquipement = equipement;\r\n    // Définir automatiquement le statut comme DISPONIBLE\r\n    this.selectedEquipement.statut = 'DISPONIBLE';\r\n    this.isAffectationModalOpen = true;\r\n    this.affectationFormSubmitted = false; // Reset submission state\r\n\r\n    // Réinitialiser le formulaire d'affectation\r\n    this.affectationForm.patchValue({\r\n      utilisateur: null,\r\n      equipement: this.selectedEquipement, // Définir l'équipement avec le statut DISPONIBLE\r\n      commentaire: '',\r\n      dateAffectation: new Date().toISOString().split('T')[0]\r\n    });\r\n  }\r\n\r\n\r\nopenEditedModal(equipement: Equip) {\r\n\r\n  // Set the selected equipment for the modal\r\n  this.selectedEquipement = equipement;\r\n  this.editAffectationFormSubmitted = false; // Reset submission state\r\n  \r\n    this.isAffectationEditModalOpen = true;\r\n\r\n}\r\n\r\n\r\n  closeAffectationModal() {\r\n    this.isAffectationModalOpen = false;\r\n\r\n    this.affectationForm.reset();\r\n  }\r\n\r\n\r\nupdateReaffication(equip: Equip) {\r\n  this.editAffectationFormSubmitted = true;\r\n\r\n  // Check if user is required and missing\r\n  if (!this.EditedAffectation.user) {\r\n    console.log('Form validation failed: User is required');\r\n    return;\r\n  }\r\n\r\n  this.EditedAffectation.equipement = equip;\r\n  this.EditedAffectation.verrou = 'affecter';\r\n\r\n  this.authservice.updateAff(this.EditedAffectation)\r\n    .subscribe({\r\n      next: (data) => {\r\n        console.log(\"Affectation mise à jour avec succès\", data);\r\n        this.showNotification('success', 'Affectation modifiée avec succès !');\r\n        this.closeAffectationEditModal();\r\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\r\n      },\r\n      error: (error) => {\r\n        console.error(\"Erreur lors de la mise à jour de l'affectation\", error);\r\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\r\n      }\r\n    });\r\n}\r\n\r\n\r\n  closeAffectationEditModal() {\r\n    this.isAffectationEditModalOpen = false;\r\n\r\n    this.utilisateurCtrl.setValue(null);\r\n\r\n    this.EditedAffectation = {\r\n      id: 0,\r\n      commentaire: \"\",\r\n      dateAffectation: new Date(),\r\n      user: new Utilisateur(),\r\n      equipement: new Equip()\r\n    };\r\n  }\r\n\r\n  closeOnOutsideClickAffectation(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeAffectationModal();\r\n    }\r\n  }\r\n\r\n  closeOnOutsideClickAffectationEdit(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeAffectationEditModal();\r\n    }\r\n  }\r\n\r\n  // Méthode pour formater la date pour les inputs HTML\r\n  formatDateForInput(date: any): string | null {\r\n    if (!date) return null;\r\n\r\n    try {\r\n      const dateObj = new Date(date);\r\n      if (isNaN(dateObj.getTime())) return null;\r\n\r\n      // Format YYYY-MM-DD pour les inputs de type date\r\n      return dateObj.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error('Erreur lors du formatage de la date:', error);\r\n      return null;\r\n    }\r\n  }\r\n\r\n  // Méthodes pour la pagination\r\n  goToPage(page: number): void {\r\n    if (page >= 0 && page < this.totalPages) {\r\n      this.loadEquipements(page);\r\n    }\r\n  }\r\n\r\n  nextPage(): void {\r\n    if (this.currentPage < this.totalPages - 1) {\r\n      this.loadEquipements(this.currentPage + 1);\r\n    }\r\n  }\r\n\r\n  previousPage(): void {\r\n    if (this.currentPage > 0) {\r\n      this.loadEquipements(this.currentPage - 1);\r\n    }\r\n  }\r\n\r\n  // Méthode pour générer les numéros de pages\r\n  getPageNumbers(): number[] {\r\n    const pages: number[] = [];\r\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\r\n\r\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\r\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\r\n\r\n    // Ajuster startPage si on est près de la fin\r\n    if (endPage - startPage < maxPagesToShow - 1) {\r\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\r\n    }\r\n\r\n    for (let i = startPage; i <= endPage; i++) {\r\n      pages.push(i);\r\n    }\r\n\r\n    return pages;\r\n  }\r\n\r\n}\r\n\r\n  \r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAASC,KAAK,QAAQ,SAAS;AAI/B,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,4BAA4B;AAGxD,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAE7E,SAASC,UAAU,QAAQ,cAAc;AAMlC,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAyEhCC,YACUC,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA5E5B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAAC,YAAY,GAAG;MACbC,IAAI,EAAE,KAAK;MACXC,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBC,MAAM,EAAC,CAAC;MACRC,QAAQ,EAAC,EAAE;MACXC,MAAM,EAAC,EAAE;MACTC,KAAK,EAAC,EAAE;MACRC,KAAK,EAAC,IAAI;MACVC,eAAe,EAAC,IAAIC,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IACD,KAAAC,cAAc,GAAO;MACrBT,MAAM,EAAC,CAAC;MACRC,QAAQ,EAAC,EAAE;MACXC,MAAM,EAAC,EAAE;MACTC,KAAK,EAAC,EAAE;MACRC,KAAK,EAAC,IAAI;MACVC,eAAe,EAAC,IAAIC,IAAI,CAAJ,CAAI;MACxBC,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IAID,KAAAE,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdP,eAAe,EAAC,IAAIC,IAAI,EAAE;MAC1BO,IAAI,EAAC,IAAIhD,WAAW,EAAE;MACtBiD,UAAU,EAAC,IAAIpD,KAAK,EAAE;MACtBqD,MAAM,EAAC;KAER;IACD,KAAAC,eAAe,GAAU,EAAE;IAC3B,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAI1D,WAAW,EAAE;IACnC,KAAA2D,qBAAqB,GAAG,IAAI3D,WAAW,EAAE;IACzC,KAAA4D,SAAS,GAAG,IAAI5D,WAAW,EAAE;IAsV/B,KAAA6D,cAAc,GAAW,EAAE,CAAC,CAAC;IA+U3B,KAAAC,YAAY,GAAgC,IAAI;IAClD,KAAAC,aAAa,GAAgB,IAAI;IAgDjC,KAAAC,YAAY,GAAQ,EAAE;EAhtBlB;EACFC,QAAQA,CAAA;IACJ,IAAI,CAAClC,WAAW,GAAG,CAAC;IAEtB,IAAI,CAACmC,YAAY,EAAE;IACnB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC;IACtC,IAAI,CAACqC,cAAc,EAAE;IAGzB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACxD,EAAE,CAACyD,KAAK,CAAC;MACxB7B,KAAK,EAAE,CAAC,IAAI,EAAExC,UAAU,CAACsE,QAAQ,CAAC;MAClCjC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACsE,QAAQ,EAAEtE,UAAU,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D5B,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBF,eAAe,EAAE,CAAC,EAAE,EAAEzC,UAAU,CAACsE,QAAQ,CAAC;MAC1ChC,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBC,KAAK,EAAE,CAAC,IAAI,CAAC;MACbiB,YAAY,EAAE,CAAC,IAAI,EAAExD,UAAU,CAACsE,QAAQ;KACzC,CAAC;IAEF;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAAC5D,EAAE,CAACyD,KAAK,CAAC;MAC5B7B,KAAK,EAAE,CAAC,IAAI,EAAExC,UAAU,CAACsE,QAAQ,CAAC;MAClCjC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACsE,QAAQ,EAAEtE,UAAU,CAACuE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9D5B,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBF,eAAe,EAAE,CAAC,EAAE,EAAEzC,UAAU,CAACsE,QAAQ,CAAC;MAC1ChC,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBC,KAAK,EAAE,CAAC,IAAI,CAAC;MACbiB,YAAY,EAAE,CAAC,IAAI,EAAExD,UAAU,CAACsE,QAAQ;KACzC,CAAC;IAKF,IAAI,CAACG,eAAe,GAAG,IAAI,CAAC7D,EAAE,CAACyD,KAAK,CAAC;MACnCpB,IAAI,EAAE,CAAC,IAAI,EAAEjD,UAAU,CAACsE,QAAQ,CAAC;MACjCpB,UAAU,EAAE,CAAC,IAAI,CAAC;MAClBF,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBP,eAAe,EAAE,CAAC,IAAIC,IAAI,EAAE,CAAC;MAC7BS,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;IAIF;IACA,IAAI,CAACQ,SAAS,CAACe,YAAY,CACxBC,IAAI,CACHzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACuE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACpE,WAAW,CAACqE,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOzE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA4E,SAAS,CAAC9D,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACkD,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACjCC,IAAI,CACHzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACuE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACpE,WAAW,CAACqE,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOzE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA4E,SAAS,CAAC9D,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACsD,QAAQ,CAACS,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACrCC,IAAI,CACHzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACuE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACpE,WAAW,CAACqE,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOzE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA4E,SAAS,CAAC9D,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAGJ,IAAI,CAACuC,eAAe,CAACiB,YAAY,CAC9BC,IAAI,CACHzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACuE,KAAK,IAAG;MAEhB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACpE,WAAW,CAACwE,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UAEL,OAAOzE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA4E,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAC9D,oBAAoB,GAAG8D,KAAK;IACnC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACzB,qBAAqB,CAACgB,YAAY,CACpCC,IAAI,CACHzE,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEsE,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACnB,qBAAqB,CAAC0B,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAACnB,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACF7D,SAAS,CAACuE,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACpE,WAAW,CAAC4E,iBAAiB,CAAC,IAAI,CAACtD,UAAU,EAAC,IAAI,CAAC0B,qBAAqB,CAACkB,KAAK,EAAC,CAAC,EAAC,IAAI,CAAC7C,QAAQ,CAAC,CAACiD,SAAS,CAAC;YAC7GO,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;cAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;cAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;YACzC,CAAC;YACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAAC3B,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACxD,WAAW,CAACwE,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAOzE,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA4E,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAC7D,0BAA0B,GAAG6D,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAY,kBAAkBA,CAAC9C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAAC+C,SAAS,IAAI/C,IAAI,CAACgD,QAAQ,MAAMhD,IAAI,CAACiD,KAAK,EAAE,GAAG,EAAE;EACzE;EAGAC,YAAYA,CAAC3D,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAAC4D,QAAQ,EAAE,GAAG,EAAE;EACzC;EAEAC,eAAeA,CAAC7D,KAAU;IACxB,IAAI,CAACK,cAAc,CAACL,KAAK,GAAGA,KAAK;EACnC;EAEA8D,qBAAqBA,CAAC9D,KAAU;IAC9B,IAAI,CAAC4B,IAAI,CAACmC,UAAU,CAAC;MAAE/D,KAAK,EAAEA;IAAK,CAAE,CAAC;EACxC;EAEAgE,sBAAsBA,CAAChE,KAAU;IAC/B,IAAI,CAACgC,QAAQ,CAAC+B,UAAU,CAAC;MAAE/D,KAAK,EAAEA;IAAK,CAAE,CAAC;EAC5C;EAIC2B,cAAcA,CAAA;IAGb,IAAI,CAACzD,WAAW,CAAC+F,iBAAiB,EAAE,CAACzB,SAAS,CAAC0B,IAAI,IAAG;MACtD,IAAI,CAAClD,YAAY,GAAGkD,IAAI;IAE1B,CAAC,CAAC;EAGA;EAEAC,kBAAkBA,CAAC/B,KAAa;IAEhC,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,IAAI,CAAC/B,cAAc,CAACL,KAAK,GAAG,IAAI;;EAEpC;EASEoE,cAAcA,CAAC3D,IAAiB;IAE9B,IAAI,IAAI,CAACjC,sBAAsB,EAAE;MAE/B,IAAI,CAACyD,eAAe,CAAC8B,UAAU,CAAC;QAC9BtD,IAAI,EAAEA;OACP,CAAC;KACH,MAAM,IAAI,IAAI,CAAChC,0BAA0B,EAAE;MAE1C,IAAI,CAAC6B,iBAAiB,CAACG,IAAI,GAAGA,IAAI;;IAEpC6C,OAAO,CAACe,GAAG,CAAC,0BAA0B,EAAE5D,IAAI,CAAC;EAC/C;EAEA6D,oBAAoBA,CAAC7D,IAAiB;IACpC,IAAI,CAACiB,eAAe,CAAC,CAAC,CAAC;EAEzB;EAEA6C,mBAAmBA,CAACC,KAAiB;IACnC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACC,UAAU,EAAE;;EAErB;EAEFC,UAAUA,CAACnE,UAAiB;IAC1B,MAAMoE,YAAY,GAAG,IAAI,CAACpG,MAAM,CAACqG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKvE,UAAU,CAACV,KAAK,EAAEiF,OAAO,CAAC;IAEnF;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAClE,YAAY,CAAC+D,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK1E,UAAU,CAACN,WAAW,EAAEgF,aAAa,CAAC;IAEjH,IAAI,CAAC/E,cAAc,GAAG;MACpB,GAAGK,UAAU;MACbV,KAAK,EAAE8E,YAAY,IAAI;KACxB;IAED;IACA,IAAI,CAAC9C,QAAQ,CAAC+B,UAAU,CAAC;MACvB/D,KAAK,EAAE,IAAI,CAACK,cAAc,CAACL,KAAK;MAChCH,QAAQ,EAAE,IAAI,CAACQ,cAAc,CAACR,QAAQ;MACtCM,WAAW,EAAE,IAAI,CAACE,cAAc,CAACF,WAAW;MAC5CF,eAAe,EAAE,IAAI,CAACoF,kBAAkB,CAAC,IAAI,CAAChF,cAAc,CAACJ,eAAe,CAAC;MAC7EH,MAAM,EAAE,IAAI,CAACO,cAAc,CAACP,MAAM;MAClCC,KAAK,EAAE,IAAI;MACXiB,YAAY,EAAEkE,kBAAkB,IAAI;KACrC,CAAC;IAEF5B,OAAO,CAACe,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAChE,cAAc,CAAC;IACvDiD,OAAO,CAACe,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAChE,cAAc,CAACD,WAAW,CAAC;IACrEkD,OAAO,CAACe,GAAG,CAAC,mCAAmC,EAAEa,kBAAkB,CAAC;IACpE5B,OAAO,CAACe,GAAG,CAAC,OAAO,EAAE,IAAI,CAAChE,cAAc,CAACJ,eAAe,CAAC;IAEzD,IAAI,CAACkB,SAAS,CAACyB,QAAQ,CAAC,IAAI,CAACvC,cAAc,CAACL,KAAK,CAAC;IAElD;IACA,IAAI,CAACzB,eAAe,GAAG,IAAI;EAC7B;EAEA+G,YAAYA,CAAA;IACV,IAAI,CAACvE,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACiB,QAAQ,CAACuD,OAAO,EAAE;MACzB,IAAI,CAACvD,QAAQ,CAACwD,gBAAgB,EAAE;MAChC;;IAGF,MAAMC,cAAc,GAAG;MACrB,GAAG,IAAI,CAACzD,QAAQ,CAACI,KAAK;MACtBxC,MAAM,EAAE,IAAI,CAACS,cAAc,CAACT,MAAM;MAClCE,MAAM,EAAE,IAAI,CAACO,cAAc,CAACP,MAAM;MAClCM,WAAW,EAAE,IAAI,CAAC4B,QAAQ,CAACI,KAAK,CAACpB,YAAY,IAAI,IAAI,CAAC;KACvD;;IAED,IAAI,CAAC9C,WAAW,CAACwH,WAAW,CAACD,cAAc,CAAC,CAACjD,SAAS,CAAC;MACrDO,IAAI,EAAG4C,QAAQ,IAAI;QACjBrC,OAAO,CAACe,GAAG,CAAC,oBAAoB,EAAEsB,QAAQ,CAAC;QAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;QAClE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACnE,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAMwG,UAAU,GAAG,IAAI/H,UAAU,EAAE;QACnC+H,UAAU,CAACC,IAAI,GAAG,IAAI7F,IAAI,EAAE;QAC5B4F,UAAU,CAACtF,WAAW,GAAG,iCAAiCiF,cAAc,CAACzF,KAAK,EAAE4D,QAAQ,eAAe6B,cAAc,CAAC5F,QAAQ,GAAG;QACjI,IAAI,CAAC3B,WAAW,CAAC8H,aAAa,CAACF,UAAU,CAAC,CAACtD,SAAS,CAAC;UACnDO,IAAI,EAAG4C,QAAQ,IAAI;YACjBrC,OAAO,CAACe,GAAG,CAAC,wBAAwB,EAAEsB,QAAQ,CAAC;UACjD,CAAC;UACDvC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAK,UAAUA,CAAA;IACR3C,OAAO,CAACe,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAChE,cAAc,CAAC;IACnD,IAAI,CAACnC,WAAW,CAACwH,WAAW,CAAC,IAAI,CAACrF,cAAc,CAAC,CAACmC,SAAS,CACxDmD,QAAQ,IAAI;MACXrC,OAAO,CAACe,GAAG,CAAC,oBAAoB,EAAEsB,QAAQ,CAAC;MAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;MAClE,IAAI,CAAChB,UAAU,EAAE;MACjB,IAAI,CAAClD,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;MACxC;IAEF,CAAC,EACA8D,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;IAC7E,CAAC,CACF;EACH;EAGAlE,eAAeA,CAACwE,IAAY;IAC1B,IAAI,CAAC5G,WAAW,GAAG4G,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAAC3G,UAAU,CAAC6C,IAAI,EAAE;IACtC,MAAMvC,MAAM,GAAG,IAAI,CAACsB,cAAc,CAACiB,IAAI,EAAE;IAEzC,IAAI+D,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAACnF,qBAAqB,CAACkB,KAAK;IAEhD,IAAI,OAAOiE,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAAChE,IAAI,EAAE;KAC1B,MAAM,IAAIgE,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAAC/D,IAAI,EAAE;;IAGpC;IACA,IAAI+D,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAClI,WAAW,CAAC4E,iBAAiB,CAACqD,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAAC3G,QAAQ,CAAC,CAACiD,SAAS,CAAC;QACzFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;QACzC,CAAC;QACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI8C,OAAO,KAAK,EAAE,IAAIrG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC5B,WAAW,CAACoI,kBAAkB,CAAC,EAAE,EAAExG,MAAM,EAAEoG,IAAI,EAAE,IAAI,CAAC3G,QAAQ,CAAC,CAACiD,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;QACzC,CAAC;QACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI8C,OAAO,KAAK,EAAE,IAAIrG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC5B,WAAW,CAAC4E,iBAAiB,CAACqD,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC3G,QAAQ,CAAC,CAACiD,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;QACzC,CAAC;QACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI8C,OAAO,KAAK,EAAE,IAAIrG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC5B,WAAW,CAACoI,kBAAkB,CAACH,OAAO,EAAErG,MAAM,EAAEoG,IAAI,EAAE,IAAI,CAAC3G,QAAQ,CAAC,CAACiD,SAAS,CAAC;QAClFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;UAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;QACzC,CAAC;QACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAACnF,WAAW,CAACqI,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAAC3G,QAAQ,CAAC,CAACiD,SAAS,CAAC;MAChEO,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACrE,UAAU,GAAGqE,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACC,UAAU,GAAGF,GAAG,CAACE,UAAU;QAChC,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACxE,UAAU,CAAC;MACzC,CAAC;MACDyE,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACxE,UAAiB;IACzC2E,OAAO,CAACe,GAAG,CAAC1F,UAAU,CAAC;IACvBA,UAAU,CAAC6H,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAAC5F,OAAO,CAAC4F,EAAE,CAAC7G,MAAM,CAAC,GAAC6G,EAAE,CAAC7G,MAAM;IAChC,CAAC,CAAC;IACF0D,OAAO,CAACe,GAAG,CAAC,IAAI,CAACxD,OAAO,CAAC;IAC1B,IAAI,CAAC3C,WAAW,CAACwI,oBAAoB,CAAC,IAAI,CAAC7F,OAAO,CAAC,CAAC2B,SAAS,CAAC0B,IAAI,IAAG;MAEnEA,IAAI,CAACsC,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAAC7F,gBAAgB,CAAC6F,WAAW,CAACjG,UAAU,CAACd,MAAM,CAAC,GAAG+G,WAAW;QAClE,IAAI,CAAC/F,eAAe,CAAC+F,WAAW,CAACjG,UAAU,CAACd,MAAM,CAAC,GAAG+G,WAAW,CAAClG,IAAI,CAAC+C,SAAS,GAAG,GAAG,GAAGmD,WAAW,CAAClG,IAAI,CAACgD,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGEmD,cAAcA,CAAA;IAEZ,IAAI,CAAClF,eAAe,CAAC,CAAC,CAAC;EACzB;EAGCmF,WAAWA,CAACtG,EAAU;IACrB,IAAI,CAACrC,WAAW,CAAC2I,WAAW,CAACtG,EAAE,CAAC,CAACiC,SAAS,CAAC,MAAK;MAC9C,IAAI,CAACoD,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;MACnE,IAAI,CAAClE,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC;IAGxC,CAAC,CAAC;EACJ;EAKQwH,qBAAqBA,CAACC,QAAgB,EAAEC,YAAoB;IAClE,IAAI,CAAC9I,WAAW,CAAC+I,kBAAkB,CAACD,YAAY,CAAC,CAACxE,SAAS,CAAC0B,IAAI,IAAG;MACnE,IAAI,CAAClF,OAAO,GAAGkF,IAAI,CAACxD,UAAU,CAACV,KAAK,EAAE4D,QAAQ,GAC9C,GAAGM,IAAI,CAACxD,UAAU,CAACV,KAAK,CAAC4D,QAAQ,cAAcM,IAAI,CAACxD,UAAU,CAACb,QAAQ,GAAG,GAC1E,IAAI;MAEF,IAAI,CAACZ,OAAO,GAAGiF,IAAI,CAACzD,IAAI,CAAC+C,SAAS,GAAG,GAAG,GAAGU,IAAI,CAACzD,IAAI,CAACgD,QAAQ;MAE7D,MAAMqC,UAAU,GAAG,IAAI/H,UAAU,EAAE;MACnC+H,UAAU,CAACC,IAAI,GAAG7B,IAAI,CAACjE,eAAe;MACtC,MAAMiH,aAAa,GAAGhD,IAAI,CAACjE,eAAe,GAAG,IAAIC,IAAI,CAACgE,IAAI,CAACjE,eAAe,CAAC,CAACkH,kBAAkB,CAAC,OAAO,CAAC,GAAG,IAAIjH,IAAI,EAAE,CAACiH,kBAAkB,CAAC,OAAO,CAAC;MAChJrB,UAAU,CAACtF,WAAW,GAAG,GAAG,IAAI,CAACxB,OAAO,IAAI+H,QAAQ,IAAI,IAAI,CAAC9H,OAAO,OAAOiI,aAAa,EAAE;MAE1F,IAAI,CAAChJ,WAAW,CAAC8H,aAAa,CAACF,UAAU,CAAC,CAACtD,SAAS,CAAC;QACnDO,IAAI,EAAG4C,QAAQ,IAAI;UACjBrC,OAAO,CAACe,GAAG,CAAC,wBAAwB,EAAEsB,QAAQ,CAAC;QACjD,CAAC;QACDvC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;QAC5E;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAgE,qBAAqBA,CAACC,KAAY;IAChC,MAAMC,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,uDAAuD,CAAC;IAC3F,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAACR,qBAAqB,CAAC,sBAAsB,EAAEO,KAAK,CAACzH,MAAM,CAAC;MAEhE,IAAI,CAAC1B,WAAW,CAACuJ,SAAS,CAAC,IAAI,CAAC3G,gBAAgB,CAACuG,KAAK,CAACzH,MAAM,CAAC,CAACW,EAAE,CAAC,CAACiC,SAAS,CAAC;QAC3EO,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC7E,WAAW,CAACwJ,mBAAmB,CAACL,KAAK,CAACzH,MAAM,CAAC,CAAC4C,SAAS,CAAC;YAC3DO,IAAI,EAAEA,CAAA,KAAK;cACT,IAAI,CAAC6C,gBAAgB,CAAC,SAAS,EAAE,mCAAmC,CAAC;cACrE,IAAI,CAAClE,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC;cACtCiI,MAAM,CAACI,QAAQ,CAAC;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAQ,CAAE,CAAC;YACjD,CAAC;YACDzE,KAAK,EAAGA,KAAK,IAAI;cACfE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;cAC5D,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,qCAAqC,CAAC;YACvE;WACD,CAAC;QACJ,CAAC;QACDxC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,CAAC;QAC9D;OACD,CAAC;;EAEN;EAGCkC,aAAaA,CAACC,OAAe;IAC5BzE,OAAO,CAACe,GAAG,CAAC0D,OAAO,CAAC;IACpB,MAAMT,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC;IAChF,IAAIF,WAAW,EAAE;MACf,IAAI,CAACT,WAAW,CAACkB,OAAO,CAAC;;EAE7B;EACFC,mBAAmBA,CAAA;IAEjB,IAAI,IAAI,CAACxJ,sBAAsB,EAAE;MAE/B,IAAI,CAACyJ,oBAAoB,EAAE;KAC5B,MAAM,IAAI,IAAI,CAACxJ,0BAA0B,EAAE;MAE1C,IAAI,CAACyJ,qBAAqB,EAAE;;EAEhC;EAEQD,oBAAoBA,CAAA;IAC1B,IAAI,CAACxI,wBAAwB,GAAG,IAAI;IAEpC,IAAI,CAAC,IAAI,CAACwC,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK,EAAE;MAC5CkB,OAAO,CAACe,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAAC,IAAI,CAAC8D,kBAAkB,EAAE;MAC5B7E,OAAO,CAACF,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAI,CAAC+E,kBAAkB,CAACrI,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACmC,eAAe,CAAC8B,UAAU,CAAC;MAAErD,UAAU,EAAE,IAAI,CAACyH;IAAkB,CAAE,CAAC;IACxE,IAAI,CAAClG,eAAe,CAAC8B,UAAU,CAAC;MAAEpD,MAAM,EAAC;IAAU,CAAE,CAAC;IAEtD2C,OAAO,CAACe,GAAG,CAAC,aAAa,EAAE,IAAI,CAACpC,eAAe,CAACG,KAAK,CAAC;IACtD,IAAI,CAAClE,WAAW,CAACkK,gBAAgB,CAAC,IAAI,CAACD,kBAAkB,CAACvI,MAAM,CAAC,CAAC4C,SAAS,CAAC;MAC1EO,IAAI,EAAG4C,QAAQ,IAAI;QACjBrC,OAAO,CAACe,GAAG,CAAC,gCAAgC,EAAEsB,QAAQ,CAAC;MACzD,CAAC;MACDvC,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;IACF,IAAI,CAAClF,WAAW,CAACmK,MAAM,CAAC,IAAI,CAACpG,eAAe,CAACG,KAAK,CAAC,CAACI,SAAS,CAAC;MAC5DO,IAAI,EAAG4C,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;QACnE,IAAI,CAAC0C,qBAAqB,EAAE;QAC5B,IAAI,CAAC5G,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAMiJ,WAAW,GAAG,IAAI,CAACtG,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK;QAC3D,MAAMoG,aAAa,GAAG,IAAI,CAACL,kBAAkB,EAAEnI,KAAK,EAAE4D,QAAQ,IAAI,oBAAoB;QACtF,MAAM/D,QAAQ,GAAG,IAAI,CAACsI,kBAAkB,EAAEtI,QAAQ,IAAI,KAAK;QAC3D,MAAM4I,cAAc,GAAGF,WAAW,GAAG,GAAGA,WAAW,CAAC/E,SAAS,IAAI,EAAE,IAAI+E,WAAW,CAAC9E,QAAQ,IAAI,EAAE,EAAE,CAACpB,IAAI,EAAE,GAAG,qBAAqB;QAClI,IAAI,CAACyE,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAACqB,kBAAkB,CAACvI,MAAM,CAAC;MAC9E,CAAC;MACDwD,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,wCAAwC,CAAC;MAC1E;KACD,CAAC;EACJ;EAEQsC,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC,IAAI,CAAC5H,iBAAiB,CAACG,IAAI,EAAE;MAChC6C,OAAO,CAACe,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAIF,MAAMqE,eAAe,GAAG;MACtB,GAAG,IAAI,CAACpI,iBAAiB;MACzBL,eAAe,EAAE,IAAI,CAACK,iBAAiB,CAACL,eAAe,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACI,iBAAiB,CAACL,eAAe,CAAC,GAAG,IAAIC,IAAI;KACtH;IAEDoD,OAAO,CAACe,GAAG,CAAC,uBAAuB,EAAEqE,eAAe,CAAC;IAErD,IAAI,CAACxK,WAAW,CAACyK,SAAS,CAACD,eAAe,CAAC,CAAClG,SAAS,CAAC;MACpDO,IAAI,EAAG4C,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAACgD,yBAAyB,EAAE;QAChC,IAAI,CAAClH,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;QAExC;QACA,MAAMwG,UAAU,GAAG,IAAI/H,UAAU,EAAE;QACnC+H,UAAU,CAACC,IAAI,GAAG,IAAI7F,IAAI,EAAE;QAE5B,MAAMsI,aAAa,GAAG,IAAI,CAACL,kBAAkB,CAACnI,KAAK,EAAE4D,QAAQ,GACzD,GAAG,IAAI,CAACuE,kBAAkB,CAACnI,KAAK,CAAC4D,QAAQ,eAAe,IAAI,CAACuE,kBAAkB,CAACtI,QAAQ,GAAG,GAC3F,oBAAoB;QAExB,MAAM4I,cAAc,GAAG,IAAI,CAACnI,iBAAiB,CAACG,IAAI,GAC9C,GAAG,IAAI,CAACH,iBAAiB,CAACG,IAAI,CAAC+C,SAAS,IAAI,EAAE,IAAI,IAAI,CAAClD,iBAAiB,CAACG,IAAI,CAACgD,QAAQ,IAAI,EAAE,EAAE,CAACpB,IAAI,EAAE,GACrG,qBAAqB;QAEzB,MAAM6E,aAAa,GAAG,IAAIhH,IAAI,EAAE,CAACiH,kBAAkB,CAAC,OAAO,CAAC;QAC5DrB,UAAU,CAACtF,WAAW,GAAG,GAAGgI,aAAa,sBAAsBC,cAAc,OAAOvB,aAAa,EAAE;QAEnG,IAAI,CAAChJ,WAAW,CAAC8H,aAAa,CAACF,UAAU,CAAC,CAACtD,SAAS,CAAC;UACnDO,IAAI,EAAG4C,QAAQ,IAAI;YACjBrC,OAAO,CAACe,GAAG,CAAC,yCAAyC,EAAEsB,QAAQ,CAAC;UAClE,CAAC;UACDvC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,qEAAqE,EAAEA,KAAK,CAAC;UAC7F;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,4CAA4C,CAAC;MAC9E;KACD,CAAC;EACJ;EAMEiD,UAAUA,CAAA;IACV,IAAI,CAAC9H,SAAS,GAAG,IAAI;IAEvBuC,OAAO,CAACe,GAAG,CAAC,IAAI,CAACzC,IAAI,CAACQ,KAAK,CAACpC,KAAK,CAAC;IAClC,IAAI,IAAI,CAAC4B,IAAI,CAAC2D,OAAO,EAAE;MACnB,IAAI,CAAC3D,IAAI,CAAC4D,gBAAgB,EAAE,CAAC,CAAC;MAC9B;;IAEJ,MAAMM,UAAU,GAAG,IAAI/H,UAAU,EAAE;IAEjC,MAAM0H,cAAc,GAAG;MACrB,GAAG,IAAI,CAAC7D,IAAI,CAACQ,KAAK;MAClBtC,MAAM,EAAE,YAAY;MACpBM,WAAW,EAAE,IAAI,CAACwB,IAAI,CAACQ,KAAK,CAACpB,YAAY,IAAI;KAC9C;IACHsC,OAAO,CAACe,GAAG,CAACoB,cAAc,CAAC;IACzB,IAAI,CAACvH,WAAW,CAAC4K,aAAa,CAACrD,cAAc,CAAC,CAACjD,SAAS,CAAC;MACvDO,IAAI,EAAG4C,QAAQ,IAAI;QACjBG,UAAU,CAACC,IAAI,GAAG,IAAI7F,IAAI,EAAE;QAC5B4F,UAAU,CAACtF,WAAW,GAAG,iCAAiCiF,cAAc,CAACzF,KAAK,EAAE4D,QAAQ,eAAe6B,cAAc,CAAC5F,QAAQ,GAAG;QACvI,IAAI,CAAC3B,WAAW,CAAC8H,aAAa,CAACF,UAAU,CAAC,CAACtD,SAAS,CAAC;UAC7CO,IAAI,EAAG4C,QAAQ,IAAI;YACjBrC,OAAO,CAACe,GAAG,CAAC,wBAAwB,EAAEsB,QAAQ,CAAC;UACjD,CAAC;UACDvC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;QACFE,OAAO,CAACe,GAAG,CAAC,8BAA8B,EAAEsB,QAAQ,CAAC;QACrD,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,+BAA+B,CAAC;QACjE,IAAI,CAAChB,UAAU,EAAE;QACjB,IAAI,CAAClD,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;QACxC;MACF,CAAC;;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C2F,KAAK,CAAC,2BAA2B,CAAC;MACpC;KACD,CAAC;EACJ;EAMAC,eAAeA,CAACxE,KAAY;IAC1B,MAAMyE,IAAI,GAAIzE,KAAK,CAACC,MAA2B,CAACyE,KAAK,GAAG,CAAC,CAAC;IAC1D,IAAID,IAAI,EAAE;MACR,IAAI,CAACrH,IAAI,CAACmC,UAAU,CAAC;QAAEhE,KAAK,EAAEkJ;MAAI,CAAE,CAAC;MACrC,IAAI,CAACrH,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAE0G,sBAAsB,EAAE;MAEhD,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAACjI,YAAY,GAAG+H,MAAM,CAACG,MAAM;MACnC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAIAQ,cAAcA,CAACjF,KAAU;IACvB,MAAMyE,IAAI,GAAGzE,KAAK,CAACC,MAAM,CAACyE,KAAK,CAAC,CAAC,CAAC;IAElC,IAAID,IAAI,EAAE;MACR,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEX,IAAI,CAAC;MAE7B,IAAI,CAAC9K,IAAI,CAAC0L,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAAClH,SAAS,CACpEmD,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACmE,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBpE,QAAQ,CAACmE,QAAQ,EAAE;UAC3DxG,OAAO,CAACe,GAAG,CAAC,mBAAmB,EAAE0F,OAAO,CAAC;UAGzC,IAAI,CAACnI,IAAI,CAACmC,UAAU,CAAC;YACnBhE,KAAK,EAAEgK;WACR,CAAC;SACH,MAAM;UACLzG,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAME4G,WAAWA,CAAA;IACT,IAAI,CAACzI,YAAY,GAAG,EAAE;EACxB;EAEIE,YAAYA,CAAA;IAEZ,IAAI,CAACvD,WAAW,CAAC+L,WAAW,EAAE,CAACzH,SAAS,CAAC0B,IAAI,IAAG;MAChD,IAAI,CAACxF,MAAM,GAAGwF,IAAI;IAEpB,CAAC,CAAC;EACF;EAKFgG,SAASA,CAAA;IACP,IAAI,CAAC5L,WAAW,GAAG,IAAI;EACzB;EAEAsG,UAAUA,CAAA;IACR,IAAI,CAACtG,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC6L,SAAS,EAAE;EAClB;EAEAtE,cAAcA,CAAA;IACZ,IAAI,CAACtH,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACyD,QAAQ,CAACoI,KAAK,EAAE;IACrB,IAAI,CAACrJ,SAAS,GAAG,KAAK;EACxB;EAEAsJ,uBAAuBA,CAAC7F,KAAiB;IACvC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACkB,cAAc,EAAE;;EAEzB;EAEA;EACAD,gBAAgBA,CAACxG,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACAiL,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACrL,YAAY,CAACC,IAAI,GAAG,KAAK;EAChC;EAEA;EACAgL,SAASA,CAAA;IACP,IAAI,CAACvI,IAAI,CAACwI,KAAK,EAAE;IACjB;IACA,IAAI,CAACxI,IAAI,CAACmC,UAAU,CAAC;MACnBjE,MAAM,EAAE;KACT,CAAC;IACF,IAAI,CAACiB,SAAS,GAAG,KAAK;IACtB,IAAI,CAACpB,aAAa,GAAG;MACnBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,IAAIC,IAAI,EAAE;MAC3BC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAC;KACb;IACD,IAAI,CAACC,cAAc,GAAG;MACpBT,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,YAAY;MACpBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,IAAI;MACXC,eAAe,EAAE,IAAIC,IAAI,EAAE;MAC3BC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAC;KACb;EACH;EAEA;EACAoK,oBAAoBA,CAAC9J,UAAiB;IACpC,IAAI,CAACyH,kBAAkB,GAAGzH,UAAU;IACpC;IACA,IAAI,CAACyH,kBAAkB,CAACrI,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACtB,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACiB,wBAAwB,GAAG,KAAK,CAAC,CAAC;IAEvC;IACA,IAAI,CAACwC,eAAe,CAAC8B,UAAU,CAAC;MAC9BwE,WAAW,EAAE,IAAI;MACjB7H,UAAU,EAAE,IAAI,CAACyH,kBAAkB;MACnC3H,WAAW,EAAE,EAAE;MACfP,eAAe,EAAE,IAAIC,IAAI,EAAE,CAACuK,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;KACvD,CAAC;EACJ;EAGFC,eAAeA,CAACjK,UAAiB;IAE/B;IACA,IAAI,CAACyH,kBAAkB,GAAGzH,UAAU;IACpC,IAAI,CAAChB,4BAA4B,GAAG,KAAK,CAAC,CAAC;IAEzC,IAAI,CAACjB,0BAA0B,GAAG,IAAI;EAE1C;EAGE6J,qBAAqBA,CAAA;IACnB,IAAI,CAAC9J,sBAAsB,GAAG,KAAK;IAEnC,IAAI,CAACyD,eAAe,CAACmI,KAAK,EAAE;EAC9B;EAGFQ,kBAAkBA,CAACvD,KAAY;IAC7B,IAAI,CAAC3H,4BAA4B,GAAG,IAAI;IAExC;IACA,IAAI,CAAC,IAAI,CAACY,iBAAiB,CAACG,IAAI,EAAE;MAChC6C,OAAO,CAACe,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAAC/D,iBAAiB,CAACI,UAAU,GAAG2G,KAAK;IACzC,IAAI,CAAC/G,iBAAiB,CAACK,MAAM,GAAG,UAAU;IAE1C,IAAI,CAACzC,WAAW,CAACyK,SAAS,CAAC,IAAI,CAACrI,iBAAiB,CAAC,CAC/CkC,SAAS,CAAC;MACTO,IAAI,EAAGmB,IAAI,IAAI;QACbZ,OAAO,CAACe,GAAG,CAAC,qCAAqC,EAAEH,IAAI,CAAC;QACxD,IAAI,CAAC0B,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAACgD,yBAAyB,EAAE;QAChC,IAAI,CAAClH,eAAe,CAAC,IAAI,CAACpC,WAAW,CAAC,CAAC,CAAC;MAC1C,CAAC;;MACD8D,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACtE,IAAI,CAACwC,gBAAgB,CAAC,OAAO,EAAE,4CAA4C,CAAC;MAC9E;KACD,CAAC;EACN;EAGEgD,yBAAyBA,CAAA;IACvB,IAAI,CAACnK,0BAA0B,GAAG,KAAK;IAEvC,IAAI,CAACwC,eAAe,CAAC2B,QAAQ,CAAC,IAAI,CAAC;IAEnC,IAAI,CAACtC,iBAAiB,GAAG;MACvBC,EAAE,EAAE,CAAC;MACLC,WAAW,EAAE,EAAE;MACfP,eAAe,EAAE,IAAIC,IAAI,EAAE;MAC3BO,IAAI,EAAE,IAAIhD,WAAW,EAAE;MACvBiD,UAAU,EAAE,IAAIpD,KAAK;KACtB;EACH;EAEAuN,8BAA8BA,CAACrG,KAAiB;IAC9C,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAAC2D,qBAAqB,EAAE;;EAEhC;EAEAwC,kCAAkCA,CAACtG,KAAiB;IAClD,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACiE,yBAAyB,EAAE;;EAEpC;EAEA;EACAvD,kBAAkBA,CAACU,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMgF,OAAO,GAAG,IAAI7K,IAAI,CAAC6F,IAAI,CAAC;MAC9B,IAAIiF,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACN,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAOtH,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACA8H,QAAQA,CAAChF,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAChD,UAAU,EAAE;MACvC,IAAI,CAACxB,eAAe,CAACwE,IAAI,CAAC;;EAE9B;EAEAiF,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC7L,WAAW,GAAG,IAAI,CAAC4D,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACxB,eAAe,CAAC,IAAI,CAACpC,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA8L,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC9L,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACoC,eAAe,CAAC,IAAI,CAACpC,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACA+L,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACpM,WAAW,GAAGmM,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAC3I,UAAU,GAAG,CAAC,EAAEsI,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;CAED;AArgCYtN,mBAAmB,GAAAgO,UAAA,EAL/B3O,SAAS,CAAC;EACT4O,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,6BAA6B;EAC1CC,SAAS,EAAE,CAAC,4BAA4B;CACzC,CAAC,C,EACWnO,mBAAmB,CAqgC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
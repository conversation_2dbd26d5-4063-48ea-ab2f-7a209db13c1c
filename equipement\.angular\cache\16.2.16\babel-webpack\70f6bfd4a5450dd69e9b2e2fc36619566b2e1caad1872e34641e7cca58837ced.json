{"ast": null, "code": "export class Panne {\n  constructor() {\n    this.description = \"\";\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON>", "constructor", "description"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\Panne.ts"], "sourcesContent": ["import { Equip } from \"src/app/equipement/equip\";\r\n\r\nexport class Panne {\r\n\r\n\r\nid!:number;\r\ndescription:string=\"\";\r\nTitre!:string;\r\nPriorite!:string;\r\nequipement!:Equip;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n}"], "mappings": "AAEA,OAAM,MAAOA,KAAK;EAAlBC,YAAA;IAIA,KAAAC,WAAW,GAAQ,EAAE;EAWrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
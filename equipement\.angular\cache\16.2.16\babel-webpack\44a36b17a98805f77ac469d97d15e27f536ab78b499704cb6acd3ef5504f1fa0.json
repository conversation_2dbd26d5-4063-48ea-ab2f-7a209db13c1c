{"ast": null, "code": "export class EtatEqui {}", "map": {"version": 3, "names": ["EtatEqui"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\EtatEqui.ts"], "sourcesContent": ["export class EtatEqui {\r\n\r\nid!:number;\r\ntitre!:string;\r\nresponsable!:string;\r\nprecedent!:EtatEqui;\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n}"], "mappings": "AAAA,OAAM,MAAOA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\nimport { MotpasseComponent } from './motpasse/motpasse.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport { MarqueComponent } from './marque/marque.component';\nimport { ModelComponent } from './model/model.component';\nimport { EquipementComponent } from './equipement/equipement.component';\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\nimport { AffectaComponent } from './affecta/affecta.component';\nimport { AgentComponent } from './agent/agent.component';\nimport { HistoriqueComponent } from './historique/historique.component';\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\nimport { AuthGuard } from './guards/auth.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [\n// Public routes (no auth required)\n{\n  path: 'utilisateur',\n  component: UtilisateurComponent\n}, {\n  path: 'motpasseoublie',\n  component: MotpasseComponent\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}, {\n  path: 'user-registration',\n  component: UserRegistrationComponent\n},\n// Protected routes (auth required)\n{\n  path: 'dashboard',\n  component: DashboardComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'marque',\n  component: MarqueComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'model',\n  component: ModelComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'equipement',\n  component: EquipementComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'fournisseur',\n  component: FournisseurComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'utilisateur-equipement',\n  component: UtilisateurEquipementComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'affecta',\n  component: AffectaComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'agent',\n  component: AgentComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'historique',\n  component: HistoriqueComponent,\n  canActivate: [AuthGuard]\n}, {\n  path: 'equipementDSI',\n  component: EquipementsComponent,\n  canActivate: [AuthGuard]\n},\n// Default route - redirect based on authentication\n{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DashboardComponent", "UtilisateurComponent", "MotpasseComponent", "ResetPasswordComponent", "MarqueComponent", "ModelComponent", "EquipementComponent", "FournisseurComponent", "UtilisateurEquipementComponent", "AffectaComponent", "AgentComponent", "HistoriqueComponent", "UserRegistrationComponent", "EquipementsComponent", "<PERSON><PERSON><PERSON><PERSON>", "routes", "path", "component", "canActivate", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\r\nimport { MotpasseComponent } from './motpasse/motpasse.component';\r\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\r\nimport { MarqueComponent } from './marque/marque.component';\r\nimport { ModelComponent } from './model/model.component';\r\nimport { EquipementComponent } from './equipement/equipement.component';\r\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\r\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\r\nimport { AffectaComponent } from './affecta/affecta.component';\r\nimport { AgentComponent } from './agent/agent.component';\r\nimport { HistoriqueComponent } from './historique/historique.component';\r\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\r\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\r\nimport { AuthGuard } from './guards/auth.guard';\r\n\r\nconst routes: Routes = [\r\n  // Public routes (no auth required)\r\n  {path:'utilisateur',component:UtilisateurComponent},\r\n  {path:'motpasseoublie',component:MotpasseComponent},\r\n  {path:'reset-password', component:ResetPasswordComponent},\r\n  {path:'user-registration',component:UserRegistrationComponent},\r\n\r\n  // Protected routes (auth required)\r\n  {path:'dashboard',component:DashboardComponent, canActivate: [AuthGuard]},\r\n  {path:'marque',component:MarqueComponent, canActivate: [AuthGuard]},\r\n  {path:'model',component:ModelComponent, canActivate: [AuthGuard]},\r\n  {path:'equipement',component:EquipementComponent, canActivate: [AuthGuard]},\r\n  {path:'fournisseur',component:FournisseurComponent, canActivate: [AuthGuard]},\r\n  {path:'utilisateur-equipement',component:UtilisateurEquipementComponent, canActivate: [AuthGuard]},\r\n  {path:'affecta',component:AffectaComponent, canActivate: [AuthGuard]},\r\n  {path:'agent',component:AgentComponent, canActivate: [AuthGuard]},\r\n  {path:'historique',component:HistoriqueComponent, canActivate: [AuthGuard]},\r\n  {path:'equipementDSI',component:EquipementsComponent, canActivate: [AuthGuard]},\r\n\r\n  // Default route - redirect based on authentication\r\n  {path:'', redirectTo:'/dashboard', pathMatch:'full'}\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,8BAA8B,QAAQ,2DAA2D;AAC1G,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,oBAAoB,QAAQ,yCAAyC;AAC9E,SAASC,SAAS,QAAQ,qBAAqB;;;AAE/C,MAAMC,MAAM,GAAW;AACrB;AACA;EAACC,IAAI,EAAC,aAAa;EAACC,SAAS,EAAChB;AAAoB,CAAC,EACnD;EAACe,IAAI,EAAC,gBAAgB;EAACC,SAAS,EAACf;AAAiB,CAAC,EACnD;EAACc,IAAI,EAAC,gBAAgB;EAAEC,SAAS,EAACd;AAAsB,CAAC,EACzD;EAACa,IAAI,EAAC,mBAAmB;EAACC,SAAS,EAACL;AAAyB,CAAC;AAE9D;AACA;EAACI,IAAI,EAAC,WAAW;EAACC,SAAS,EAACjB,kBAAkB;EAAEkB,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EACzE;EAACE,IAAI,EAAC,QAAQ;EAACC,SAAS,EAACb,eAAe;EAAEc,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EACnE;EAACE,IAAI,EAAC,OAAO;EAACC,SAAS,EAACZ,cAAc;EAAEa,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EACjE;EAACE,IAAI,EAAC,YAAY;EAACC,SAAS,EAACX,mBAAmB;EAAEY,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EAC3E;EAACE,IAAI,EAAC,aAAa;EAACC,SAAS,EAACV,oBAAoB;EAAEW,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EAC7E;EAACE,IAAI,EAAC,wBAAwB;EAACC,SAAS,EAACT,8BAA8B;EAAEU,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EAClG;EAACE,IAAI,EAAC,SAAS;EAACC,SAAS,EAACR,gBAAgB;EAAES,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EACrE;EAACE,IAAI,EAAC,OAAO;EAACC,SAAS,EAACP,cAAc;EAAEQ,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EACjE;EAACE,IAAI,EAAC,YAAY;EAACC,SAAS,EAACN,mBAAmB;EAAEO,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC,EAC3E;EAACE,IAAI,EAAC,eAAe;EAACC,SAAS,EAACJ,oBAAoB;EAAEK,WAAW,EAAE,CAACJ,SAAS;AAAC,CAAC;AAE/E;AACA;EAACE,IAAI,EAAC,EAAE;EAAEG,UAAU,EAAC,YAAY;EAAEC,SAAS,EAAC;AAAM,CAAC,CACrD;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBtB,YAAY,CAACuB,OAAO,CAACP,MAAM,CAAC,EAC5BhB,YAAY;IAAA;EAAA;;;2EAEXsB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAzB,YAAA;IAAA0B,OAAA,GAFjB1B,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
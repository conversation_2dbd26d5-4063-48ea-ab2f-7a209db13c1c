{"ast": null, "code": "import * as bootstrap from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./type.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../Shared/layout/layout.component\";\nfunction DashboardComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r0.signupErrors.nomType);\n  }\n}\nfunction DashboardComponent_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r2.signupErrors.nomType);\n  }\n}\nfunction DashboardComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.notification.message, \"\\n\");\n  }\n}\nfunction DashboardComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 55);\n    i0.ɵɵelement(4, \"path\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"div\")(6, \"p\", 57);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 58);\n    i0.ɵɵtext(9, \"Cr\\u00E9\\u00E9 le 15/01/2024\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"p\", 59);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 60)(13, \"span\", 61);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_78_Template_button_click_15_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const type_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r6.openModal1(type_r5));\n    });\n    i0.ɵɵtext(16, \"Modifier\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_78_Template_button_click_17_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const type_r5 = restoredCtx.$implicit;\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.confirmDelete(type_r5.idType));\n    });\n    i0.ɵɵtext(18, \"supprimer\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const type_r5 = ctx.$implicit;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(type_r5.nomType);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(type_r5.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", type_r5.marques.length, \" marques\");\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class DashboardComponent {\n  constructor(http, authservice, router) {\n    this.http = http;\n    this.authservice = authservice;\n    this.router = router;\n    this.Types = [];\n    this.searchText = '';\n    this.isModalOpen = false;\n    this.newType = {\n      idType: 0,\n      nomType: '',\n      description: '',\n      marques: [] // ajoute la liste vide par défaut\n    };\n    // Notification system\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.signupErrors = {};\n  }\n  openModal() {\n    this.resetErrors();\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  updateData() {\n    if (!this.validateSignup()) {\n      return; // formulaire invalide => on ne continue pas\n    }\n\n    this.authservice.updateType(this.newType).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Type modifié avec succès');\n      ;\n      this.GetALLTypes();\n      this.closeModal1();\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification du type');\n    });\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  closeModal1() {\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\n      modal.hide();\n    }\n  }\n  GetALLTypes() {\n    this.authservice.getAllTypes().subscribe(data => {\n      this.Types = data;\n      console.log(\"Types reçus : \", JSON.stringify(this.Types, null, 2));\n    });\n  }\n  onRegister() {\n    if (!this.validateSignup()) {\n      return; // formulaire invalide => on ne continue pas\n    }\n\n    console.log('User Data:', this.newType);\n    this.authservice.addType(this.newType).subscribe({\n      next: response => {\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Type ajouté avec succès');\n        this.Types.push(response);\n        this.closeModal();\n        window.scrollTo({\n          top: 0,\n          behavior: 'smooth'\n        });\n      },\n      error: error => {\n        console.error('Registration failed:', error);\n        this.showNotification('error', 'Échec de l\\'ajout du type');\n      }\n    });\n  }\n  filterTypes() {\n    return this.Types.filter(r => r.nomType.toLowerCase().includes(this.searchText.toLowerCase()));\n  }\n  openModal1(Type) {\n    this.resetErrors;\n    this.newType = {\n      ...Type\n    };\n    console.log('Données mises à jour:', this.newType);\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = new bootstrap.Modal(modalElement);\n      modal.show();\n    } else {\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\n    }\n  }\n  deleteType(id) {\n    this.authservice.deleteType(id).subscribe(() => {\n      this.Types = this.Types.filter(Type => Type.idType !== id);\n    });\n    this.showNotification('success', 'Type supprimé avec succès');\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  confirmDelete(TypeId) {\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\n    if (isConfirmed) {\n      this.deleteType(TypeId);\n    }\n  }\n  validateSignup() {\n    this.resetErrors();\n    let isValid = true;\n    // Username\n    if (!this.newType.nomType || this.newType.nomType.trim().length === 0) {\n      this.signupErrors.nomType = 'Le nom de type est requis';\n      isValid = false;\n    } else if (this.newType.nomType.length < 3) {\n      this.signupErrors.nomType = 'Le nom de type doit contenir au moins 3 caractères';\n      isValid = false;\n    }\n    return isValid;\n  }\n  submitForm() {\n    if (this.validateSignup()) {\n      // Ici vous pouvez envoyer newUser au backend ou autre traitement\n      console.log('Formulaire valide', this.newType);\n    } else {\n      console.log('Formulaire invalide', this.signupErrors);\n    }\n  }\n  ngOnInit() {\n    this.GetALLTypes();\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.TypeService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 87,\n      vars: 12,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\", 3, \"click\"], [1, \"icon\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un type...\", 3, \"ngModel\", \"ngModelChange\"], [1, \"icon-search\"], [\"id\", \"updateModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"updateModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [1, \"modal-content\", \"shadow\", \"rounded-4\"], [\"id\", \"updateModalLabel\", 1, \"modal-title\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [1, \"mb-4\"], [\"for\", \"nomType\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"type\", \"text\", \"id\", \"nomType\", \"name\", \"nomType\", \"required\", \"\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"style\", \"color:red\", 4, \"ngIf\"], [\"for\", \"description\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"id\", \"description\", \"name\", \"description\", \"rows\", \"3\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"px-4\", 3, \"click\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"ngSubmit\"], [\"userForm\", \"ngForm\"], [\"for\", \"email\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"nomType\", \"type\", \"text\", \"name\", \"nomType\", \"placeholder\", \"Ex: Ordinateur portable\", \"required\", \"\", 1, \"form-inputp\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Description de type d'equipement\", \"rows\", \"3\", \"cols\", \"60\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\"], [1, \"row\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"card\", 4, \"ngFor\", \"ngForOf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [2, \"color\", \"red\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"card\"], [1, \"card-flex\"], [1, \"card-icon\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 20 20\"], [\"d\", \"M17.707 10.293l-7-7A1 1 0 0 0 10 3H4a1 1 0 0 0-1 1v6c0 .265.105.52.293.707l7 7a1 1 0 0 0 1.414 0l6-6a1 1 0 0 0 0-1.414zM6.5 7A1.5 1.5 0 1 1 9 7a1.5 1.5 0 0 1-2.5 0z\"], [1, \"card-title\"], [1, \"card-date\"], [1, \"card-desc\"], [1, \"card-footer\"], [1, \"card-badge\"], [1, \"card-button\", 3, \"click\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtext(18, \"\\n<\\n\");\n          i0.ɵɵelementStart(19, \"div\", 9)(20, \"div\", 10)(21, \"h2\");\n          i0.ɵɵtext(22, \"Types d'\\u00E9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\");\n          i0.ɵɵtext(24, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_25_listener() {\n            return ctx.openModal();\n          });\n          i0.ɵɵelementStart(26, \"span\", 12);\n          i0.ɵɵtext(27, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(28, \"Nouveau Type \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"div\", 13)(30, \"div\", 14)(31, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_Template_input_ngModelChange_31_listener($event) {\n            return ctx.searchText = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"span\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"div\", 17)(34, \"div\", 18)(35, \"div\", 19)(36, \"h5\", 20);\n          i0.ɵɵtext(37, \"\\uD83D\\uDCDD Modifier les informations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(38, \"button\", 21);\n          i0.ɵɵelementStart(39, \"div\", 22)(40, \"form\")(41, \"div\", 23)(42, \"label\", 24);\n          i0.ɵɵtext(43, \"Nom du type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_Template_input_ngModelChange_44_listener($event) {\n            return ctx.newType.nomType = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, DashboardComponent_div_45_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelement(46, \"br\");\n          i0.ɵɵelementStart(47, \"div\", 23)(48, \"label\", 27);\n          i0.ɵɵtext(49, \"Description \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"textarea\", 28);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_Template_textarea_ngModelChange_50_listener($event) {\n            return ctx.newType.description = $event;\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 29)(52, \"button\", 30);\n          i0.ɵɵtext(53, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_54_listener() {\n            return ctx.updateData();\n          });\n          i0.ɵɵtext(55, \" \\uD83D\\uDCBE Sauvegarder \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(56, \"div\", 32);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_56_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(57, \"div\", 33);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_div_click_57_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(58, \"span\", 34);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_span_click_58_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(59, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"h3\", 35);\n          i0.ɵɵtext(61, \"Ajouter un nouveau Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"form\", 36, 37);\n          i0.ɵɵlistener(\"ngSubmit\", function DashboardComponent_Template_form_ngSubmit_62_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(64, \"br\");\n          i0.ɵɵelementStart(65, \"label\", 38);\n          i0.ɵɵtext(66, \"Nom du type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"input\", 39);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_Template_input_ngModelChange_67_listener($event) {\n            return ctx.newType.nomType = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(68, DashboardComponent_div_68_Template, 2, 1, \"div\", 26);\n          i0.ɵɵelement(69, \"br\")(70, \"br\");\n          i0.ɵɵelementStart(71, \"label\", 40);\n          i0.ɵɵtext(72, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"textarea\", 41);\n          i0.ɵɵlistener(\"ngModelChange\", function DashboardComponent_Template_textarea_ngModelChange_73_listener($event) {\n            return ctx.newType.description = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"button\", 42);\n          i0.ɵɵtext(75, \"Enregistrer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(76, \"div\", 43);\n          i0.ɵɵtemplate(77, DashboardComponent_div_77_Template, 2, 2, \"div\", 44);\n          i0.ɵɵtemplate(78, DashboardComponent_div_78_Template, 19, 3, \"div\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 46)(80, \"p\", 47);\n          i0.ɵɵtext(81, \"Design and Developed by \");\n          i0.ɵɵelementStart(82, \"a\", 48);\n          i0.ɵɵtext(83, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(84, \" Distributed by \");\n          i0.ɵɵelementStart(85, \"a\", 49);\n          i0.ɵɵtext(86, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(31);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchText);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngModel\", ctx.newType.nomType);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.signupErrors.nomType);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.newType.description);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.isModalOpen));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.newType.nomType);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.signupErrors.nomType);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.newType.description);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filterTypes());\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.RequiredValidator, i5.NgModel, i5.NgForm, i6.LayoutComponent],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #0051ff; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #d1d5db;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \".card[_ngcontent-%COMP%] {\\n    width: 385px;\\n    height: 190px;\\n    padding: 20px;\\n    background-color: #fff;\\n    border-radius: 12px;\\n    box-shadow: 0 0 0 1px #e5e7eb;\\n    font-family: 'Segoe UI', sans-serif;\\n    font-size: 14px;\\n    display: flex;\\n    flex-direction: column;\\n    gap: 14px;\\n    margin-top: 20px;\\n    margin-left: 20px;\\n  }\\n\\n  .card-icon[_ngcontent-%COMP%] {\\n    background-color: #e0edff;\\n    color: #2563eb;\\n    padding: 6px;\\n    border-radius: 8px;\\n    width: 43px;\\n    height: 43px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .card-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 25px;\\n    height: 25px;\\n  }\\n\\n  .card-title[_ngcontent-%COMP%] {\\n    font-weight: 600;\\n    color: #111827;\\n    font-size: 20px;\\n    margin: 0;\\n  }\\n\\n  .card-date[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n    color: #9ca3af;\\n    margin: 2px 0 0 0;\\n  }\\n\\n  .card-desc[_ngcontent-%COMP%] {\\n    color: #4b5563;\\n    margin: 0;\\n    font-size: 16px;\\n  }\\n\\n  .card-badge[_ngcontent-%COMP%] {\\n    background-color: #e0edff;\\n    color: #0d00ff;\\n    padding: 4px 10px;\\n    border-radius: 990px;\\n    font-size: 12px;\\n  }\\n\\n.card-button[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  cursor: pointer;\\n  color: #000000; \\n\\n  font-weight: 500;;      \\n\\n\\n\\n}\\n\\n\\n  .card-flex[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n  }\\n\\n  .card-footer[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    background-color: white;\\n    padding: 10px;\\n    border-radius: 6px;\\n    border: 0px solid #e5e7eb;\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["bootstrap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "signupErrors", "nomType", "ctx_r2", "ɵɵproperty", "ctx_r3", "notification", "type", "ɵɵtextInterpolate1", "message", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵnamespaceHTML", "ɵɵlistener", "DashboardComponent_div_78_Template_button_click_15_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "type_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "openModal1", "DashboardComponent_div_78_Template_button_click_17_listener", "ctx_r8", "confirmDelete", "idType", "description", "marques", "length", "DashboardComponent", "constructor", "http", "authservice", "router", "Types", "searchText", "isModalOpen", "newType", "show", "openModal", "resetErrors", "closeModal", "showNotification", "setTimeout", "hideNotification", "updateData", "validateSignup", "updateType", "subscribe", "response", "console", "log", "GetALLTypes", "closeModal1", "window", "scrollTo", "top", "behavior", "error", "closeOnOutsideClick", "event", "target", "classList", "contains", "modalElement", "document", "getElementById", "modal", "Modal", "getInstance", "hide", "getAllTypes", "data", "JSON", "stringify", "onRegister", "addType", "next", "push", "filterTypes", "filter", "r", "toLowerCase", "includes", "Type", "deleteType", "id", "TypeId", "isConfirmed", "confirm", "<PERSON><PERSON><PERSON><PERSON>", "trim", "submitForm", "ngOnInit", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "TypeService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_Template_button_click_25_listener", "DashboardComponent_Template_input_ngModelChange_31_listener", "$event", "DashboardComponent_Template_input_ngModelChange_44_listener", "ɵɵtemplate", "DashboardComponent_div_45_Template", "DashboardComponent_Template_textarea_ngModelChange_50_listener", "DashboardComponent_Template_button_click_54_listener", "DashboardComponent_Template_div_click_56_listener", "DashboardComponent_Template_div_click_57_listener", "stopPropagation", "DashboardComponent_Template_span_click_58_listener", "DashboardComponent_Template_form_ngSubmit_62_listener", "DashboardComponent_Template_input_ngModelChange_67_listener", "DashboardComponent_div_68_Template", "DashboardComponent_Template_textarea_ngModelChange_73_listener", "DashboardComponent_div_77_Template", "DashboardComponent_div_78_Template", "ɵɵpureFunction1", "_c0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component,OnInit } from '@angular/core';\r\n\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { UtilisateurService } from '../utilisateur/utilisateur.service';\r\nimport { Router } from '@angular/router';\r\nimport { TypeEqui } from './TypeEqui';\r\nimport { TypeService } from './type.service';\r\nimport * as bootstrap from 'bootstrap';\r\n// or for just Modal:\r\nimport { Modal } from 'bootstrap';\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.css']\r\n})\r\nexport class DashboardComponent implements OnInit {\r\nTypes:TypeEqui[]=[];\r\n  searchText: string = '';\r\nisModalOpen = false;\r\nnewType: TypeEqui = {\r\n  idType: 0,\r\n  nomType: '',\r\n  description: '',\r\n  marques: []  // ajoute la liste vide par défaut\r\n};\r\n\r\n// Notification system\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\n\r\n \r\n  constructor(private http: HttpClient,private authservice:TypeService,private router: Router) {}\r\n\r\n  openModal() {\r\n    this.resetErrors();\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n  updateData() {\r\n  if (!this.validateSignup()) {\r\n    return; // formulaire invalide => on ne continue pas\r\n  }\r\n    this.authservice.updateType(this.newType).subscribe(\r\n      (response) => {\r\n        console.log('Update successful:', response);\r\n        this.showNotification('success', 'Type modifié avec succès');\r\n ;\r\n        this.GetALLTypes();\r\n           this.closeModal1();\r\n                window.scrollTo({ top: 0, behavior: 'smooth' });\r\n      },\r\n      (error) => {\r\n        console.error('Update failed:', error);\r\n        this.showNotification('error', 'Échec de la modification du type');\r\n      }\r\n    );\r\n  }\r\n\r\n  closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\n  signupErrors: any = {};\r\n\r\n  resetErrors() {\r\n    this.signupErrors = {};\r\n  }\r\n\r\ncloseModal1() {\r\n  const modalElement = document.getElementById('updateModal');\r\n  if (modalElement) {\r\n    const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\r\n    modal.hide();\r\n  }\r\n}\r\n\r\n  GetALLTypes(){\r\n  this.authservice.getAllTypes().subscribe(data => {\r\n  this.Types = data;\r\n  console.log(\"Types reçus : \", JSON.stringify(this.Types, null, 2));\r\n});\r\n\r\n  }\r\n  onRegister(): void {\r\n  if (!this.validateSignup()) {\r\n    return; // formulaire invalide => on ne continue pas\r\n  }\r\n  console.log('User Data:', this.newType);\r\n\r\n  this.authservice.addType(this.newType).subscribe({\r\n\r\n    next: (response) => {\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Type ajouté avec succès');\r\n\r\n      this.Types.push(response);\r\n\r\n\r\n     \r\n      this.closeModal();\r\n        window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      this.showNotification('error', 'Échec de l\\'ajout du type');\r\n    }\r\n  });\r\n}\r\n\r\n  filterTypes() {\r\n    return this.Types.filter((r) => r.nomType.toLowerCase().includes(this.searchText.toLowerCase()));\r\n  }\r\n  openModal1(Type: TypeEqui) {\r\n    this.resetErrors\r\n    this.newType = { ...Type };\r\n    console.log('Données mises à jour:', this.newType);\r\n    const modalElement = document.getElementById('updateModal');\r\n    if (modalElement) {\r\n      const modal = new bootstrap.Modal(modalElement);\r\n      modal.show();\r\n    } else {\r\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\r\n    }\r\n  }\r\n\r\n  deleteType(id: number) {\r\n    this.authservice.deleteType(id).subscribe(() => {\r\n      this.Types = this.Types.filter(Type => Type.idType !== id);\r\n    });\r\n            this.showNotification('success', 'Type supprimé avec succès');\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n  confirmDelete(TypeId: number): void {\r\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\r\n    if (isConfirmed) {\r\n      this.deleteType(TypeId);\r\n    }\r\n  }\r\n\r\n\r\n  validateSignup(): boolean {\r\n    this.resetErrors();\r\n    let isValid = true;\r\n\r\n    // Username\r\n    if (!this.newType.nomType || this.newType.nomType.trim().length === 0) {\r\n      this.signupErrors.nomType = 'Le nom de type est requis';\r\n      isValid = false;\r\n    } else if (this.newType.nomType.length < 3) {\r\n      this.signupErrors.nomType = 'Le nom de type doit contenir au moins 3 caractères';\r\n      isValid = false;\r\n    }\r\n\r\n\r\n  \r\n  \r\n\r\n    \r\n\r\n    return isValid;\r\n  }\r\n\r\n  submitForm() {\r\n    if (this.validateSignup()) {\r\n      // Ici vous pouvez envoyer newUser au backend ou autre traitement\r\n      console.log('Formulaire valide', this.newType);\r\n    } else {\r\n      console.log('Formulaire invalide', this.signupErrors);\r\n    }\r\n  }\r\n  ngOnInit(): void {\r\n    this.GetALLTypes();\r\n   \r\n  }\r\n\r\n  \r\n\r\n\r\n  \r\n\r\n}\r\n", "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\r\n  \r\n</head>\r\n\r\n<body>\r\n  <!--  Body Wrapper -->\r\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\r\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\r\n\r\n    <!--  App Topstrip -->\r\n    \r\n    <!-- Sidebar Start -->\r\n<app-layout></app-layout>\r\n    <!--  Sidebar End -->\r\n    <!--  Main wrapper -->\r\n    <div class=\"body-wrapper\">\r\n      <!--  Header Start -->\r\n      <header class=\"app-header\">\r\n\r\n      </header>\r\n      <!--  Header End -->\r\n      <div class=\"body-wrapper-inner\">\r\n        <div class=\"container-fluid\">\r\n                <div class=\"welcome-header\">\r\n  <h1>Bienvenue dans votre espace</h1>\r\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\r\n\r\n</div>\r\n<!-- Bouton pour ouvrir la modale -->\r\n<\r\n<div class=\"header-container\">\r\n  <div class=\"header-text\">\r\n    <h2>Types d'équipements</h2>\r\n    <p>Gérez les différents types d'équipements informatiques\r\n\r\n</p>\r\n  </div>\r\n<button class=\"add-user-btn\" (click)=\"openModal()\">\r\n  <span class=\"icon\">+</span>Nouveau Type\r\n\r\n</button>\r\n</div>\r\n<div class=\"search-wrapper\">\r\n  <div class=\"custom-search\">\r\n    <input type=\"text\" placeholder=\"Rechercher un type...\" [(ngModel)]=\"searchText\" />\r\n    <span class=\"icon-search\"></span>\r\n  </div>\r\n</div>\r\n<!-- Modal -->\r\n<div class=\"modal fade\" id=\"updateModal\" tabindex=\"-1\" aria-labelledby=\"updateModalLabel\"\r\n     aria-hidden=\"true\" data-bs-backdrop=\"false\">\r\n\r\n  <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n    <div class=\"modal-content shadow rounded-4\">\r\n   \r\n        <h5 class=\"modal-title\" id=\"updateModalLabel\">📝 Modifier les informations</h5>\r\n        <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n   \r\n      <div class=\"modal-body\">\r\n        <form>\r\n          <!-- Nom Type -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"nomType\" class=\"form-label fw-semibold fs-5\">Nom du type</label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              id=\"nomType\"\r\n              name=\"nomType\"\r\n              [(ngModel)]=\"newType.nomType\"\r\n              required\r\n            />\r\n          </div>\r\n          <div *ngIf=\"signupErrors.nomType\" style=\"color:red\">{{ signupErrors.nomType }}</div>\r\n<br>\r\n          <!-- Description -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"description\" class=\"form-label fw-semibold fs-5\">Description </label>\r\n            <textarea\r\n              class=\"form-control\"\r\n              id=\"description\"\r\n              name=\"description\"\r\n              rows=\"3\"\r\n              [(ngModel)]=\"newType.description\"\r\n\r\n            ></textarea>\r\n          </div>\r\n          \r\n        </form>\r\n      </div>\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">\r\n          Annuler\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-success px-4\" (click)=\"updateData()\">\r\n          💾 Sauvegarder\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n    \r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<!-- MODAL -->\r\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\" >Ajouter un nouveau Type</h3>\r\n\r\n   <form (ngSubmit)=\"onRegister()\" #userForm=\"ngForm\" novalidate>\r\n<br>\r\n\r\n  <label style=\"font-size: 14px;   font-weight: 500; color: #000000; margin-bottom:-40px\" for=\"email\">Nom du type</label>\r\n<input\r\nclass=\"form-inputp\"\r\n  id=\"nomType\"\r\n  type=\"text\"\r\n  name=\"nomType\"\r\n  [(ngModel)]=\"newType.nomType\"\r\n  placeholder=\"Ex: Ordinateur portable\"\r\n  required\r\n  \r\n>\r\n<div *ngIf=\"signupErrors.nomType\" style=\"color:red\">{{ signupErrors.nomType }}</div>\r\n<br>\r\n<br>\r\n\r\n  <label style=\"font-size: 14px;   font-weight: 500; color: #000000;\" for=\"email\">Description</label>\r\n<textarea\r\n  id=\"description\"\r\n  name=\"description\"\r\n  [(ngModel)]=\"newType.description\"\r\n  placeholder=\"Description de type d'equipement\"\r\n  rows=\"3\"\r\n  cols=\"60\"\r\n  \r\n  \r\n></textarea>\r\n\r\n\r\n\r\n\r\n\r\n  <button type=\"submit\">Enregistrer</button>\r\n</form>\r\n\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n          <!--  Row 1 -->\r\n          <div class=\"row\">\r\n           \r\n    <style>\r\n  .card {\r\n    width: 385px;\r\n    height: 190px;\r\n    padding: 20px;\r\n    background-color: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 0 0 1px #e5e7eb;\r\n    font-family: 'Segoe UI', sans-serif;\r\n    font-size: 14px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 14px;\r\n    margin-top: 20px;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .card-icon {\r\n    background-color: #e0edff;\r\n    color: #2563eb;\r\n    padding: 6px;\r\n    border-radius: 8px;\r\n    width: 43px;\r\n    height: 43px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .card-icon svg {\r\n    width: 25px;\r\n    height: 25px;\r\n  }\r\n\r\n  .card-title {\r\n    font-weight: 600;\r\n    color: #111827;\r\n    font-size: 20px;\r\n    margin: 0;\r\n  }\r\n\r\n  .card-date {\r\n    font-size: 15px;\r\n    color: #9ca3af;\r\n    margin: 2px 0 0 0;\r\n  }\r\n\r\n  .card-desc {\r\n    color: #4b5563;\r\n    margin: 0;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .card-badge {\r\n    background-color: #e0edff;\r\n    color: #0d00ff;\r\n    padding: 4px 10px;\r\n    border-radius: 990px;\r\n    font-size: 12px;\r\n  }\r\n\r\n.card-button {\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background-color: #fff;\r\n  cursor: pointer;\r\n  color: #000000; /* Darker gray (high opacity) */\r\n  font-weight: 500;;      /* ⬅️ makes text bold */\r\n\r\n\r\n}\r\n\r\n\r\n  .card-flex {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .card-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: white;\r\n    padding: 10px;\r\n    border-radius: 6px;\r\n    border: 0px solid #e5e7eb;\r\n  }\r\n  \r\n</style>\r\n\r\n<!-- Simple Notification Bar -->\r\n<div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n  {{ notification.message }}\r\n</div>\r\n\r\n<div *ngFor=\"let type of filterTypes()\" class=\"card\">\r\n  <div class=\"card-flex\">\r\n    <div class=\"card-icon\">\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\r\n        <path d=\"M17.707 10.293l-7-7A1 1 0 0 0 10 3H4a1 1 0 0 0-1 1v6c0 .265.105.52.293.707l7 7a1 1 0 0 0 1.414 0l6-6a1 1 0 0 0 0-1.414zM6.5 7A1.5 1.5 0 1 1 9 7a1.5 1.5 0 0 1-2.5 0z\"/>\r\n      </svg>\r\n    </div>\r\n\r\n    <div>\r\n      <p class=\"card-title\">{{ type.nomType }}</p>\r\n      <p class=\"card-date\">Créé le 15/01/2024</p> <!-- ou {{ type.dateCreation }} si tu veux le rendre dynamique -->\r\n    </div>\r\n  </div>\r\n\r\n  <p class=\"card-desc\">{{ type.description }}</p>\r\n\r\n  <div class=\"card-footer\">\r\n   <span class=\"card-badge\">{{ type.marques.length }} marques</span>\r\n\r\n    <button class=\"card-button\" (click)=\"openModal1(type)\">Modifier</button>\r\n    <button class=\"card-button\"(click)=\"confirmDelete(type.idType)\">supprimer</button>\r\n  </div>\r\n</div>\r\n\r\n\r\n       \r\n          </div>\r\n          <div class=\"py-6 px-6 text-center\">\r\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\r\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\r\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\r\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\r\n  <script src=\"./assets/js/app.min.js\"></script>\r\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\r\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\r\n  <script src=\"./assets/js/dashboard.js\"></script>\r\n  <!-- solar icons -->\r\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\r\n</body>\r\n\r\n</html>"], "mappings": "AAOA,OAAO,KAAKA,SAAS,MAAM,WAAW;;;;;;;;;;ICuE5BC,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAAC,OAAA,CAA0B;;;;;IA0DxFR,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAhCH,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAAI,MAAA,CAAAF,YAAA,CAAAC,OAAA,CAA0B;;;;;IA8H9ER,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAU,UAAA,YAAAC,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFb,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAc,kBAAA,MAAAH,MAAA,CAAAC,YAAA,CAAAG,OAAA,OACF;;;;;;IAEAf,EAAA,CAAAC,cAAA,cAAqD;IAG/CD,EAAA,CAAAgB,cAAA,EAAgF;IAAhFhB,EAAA,CAAAC,cAAA,cAAgF;IAC9ED,EAAA,CAAAiB,SAAA,eAAgL;IAClLjB,EAAA,CAAAG,YAAA,EAAM;IAGRH,EAAA,CAAAkB,eAAA,EAAK;IAALlB,EAAA,CAAAC,cAAA,UAAK;IACmBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5CH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,mCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI/CH,EAAA,CAAAC,cAAA,aAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE/CH,EAAA,CAAAC,cAAA,eAAyB;IACCD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEhEH,EAAA,CAAAC,cAAA,kBAAuD;IAA3BD,EAAA,CAAAmB,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,WAAA,GAAArB,EAAA,CAAAsB,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAA1B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAL,OAAA,CAAgB;IAAA,EAAC;IAACxB,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxEH,EAAA,CAAAC,cAAA,kBAAgE;IAArCD,EAAA,CAAAmB,UAAA,mBAAAW,4DAAA;MAAA,MAAAT,WAAA,GAAArB,EAAA,CAAAsB,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAM,MAAA,GAAA/B,EAAA,CAAA2B,aAAA;MAAA,OAAS3B,EAAA,CAAA4B,WAAA,CAAAG,MAAA,CAAAC,aAAA,CAAAR,OAAA,CAAAS,MAAA,CAA0B;IAAA,EAAC;IAACjC,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAX1DH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAmB,OAAA,CAAAhB,OAAA,CAAkB;IAKvBR,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAmB,OAAA,CAAAU,WAAA,CAAsB;IAGjBlC,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAc,kBAAA,KAAAU,OAAA,CAAAW,OAAA,CAAAC,MAAA,aAAiC;;;;;;;;AD5Q7D,OAAM,MAAOC,kBAAkB;EAmB7BC,YAAoBC,IAAgB,EAASC,WAAuB,EAASC,MAAc;IAAvE,KAAAF,IAAI,GAAJA,IAAI;IAAqB,KAAAC,WAAW,GAAXA,WAAW;IAAqB,KAAAC,MAAM,GAANA,MAAM;IAlBrF,KAAAC,KAAK,GAAY,EAAE;IACjB,KAAAC,UAAU,GAAW,EAAE;IACzB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,OAAO,GAAa;MAClBZ,MAAM,EAAE,CAAC;MACTzB,OAAO,EAAE,EAAE;MACX0B,WAAW,EAAE,EAAE;MACfC,OAAO,EAAE,EAAE,CAAE;KACd;IAED;IACA,KAAAvB,YAAY,GAAG;MACbkC,IAAI,EAAE,KAAK;MACXjC,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;IAyDC,KAAAR,YAAY,GAAQ,EAAE;EAtDwE;EAE9FwC,SAASA,CAAA;IACP,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACJ,WAAW,GAAG,IAAI;EACzB;EAEAK,UAAUA,CAAA;IACR,IAAI,CAACL,WAAW,GAAG,KAAK;EAC1B;EAEA;EACAM,gBAAgBA,CAACrC,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBkC,IAAI,EAAE,IAAI;MACVjC,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAED;IACAoC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACxC,YAAY,CAACkC,IAAI,GAAG,KAAK;EAChC;EACAO,UAAUA,CAAA;IACV,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE,EAAE;MAC1B,OAAO,CAAC;;;IAER,IAAI,CAACd,WAAW,CAACe,UAAU,CAAC,IAAI,CAACV,OAAO,CAAC,CAACW,SAAS,CAChDC,QAAQ,IAAI;MACXC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,QAAQ,CAAC;MAC3C,IAAI,CAACP,gBAAgB,CAAC,SAAS,EAAE,0BAA0B,CAAC;MACnE;MACO,IAAI,CAACU,WAAW,EAAE;MACf,IAAI,CAACC,WAAW,EAAE;MACbC,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;IACzD,CAAC,EACAC,KAAK,IAAI;MACRR,OAAO,CAACQ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAAChB,gBAAgB,CAAC,OAAO,EAAE,kCAAkC,CAAC;IACpE,CAAC,CACF;EACH;EAEAiB,mBAAmBA,CAACC,KAAiB;IACnC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACtB,UAAU,EAAE;;EAErB;EAIAD,WAAWA,CAAA;IACT,IAAI,CAACzC,YAAY,GAAG,EAAE;EACxB;EAEFsD,WAAWA,CAAA;IACT,MAAMW,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG5E,SAAS,CAAC6E,KAAK,CAACC,WAAW,CAACL,YAAY,CAAC,IAAI,IAAIzE,SAAS,CAAC6E,KAAK,CAACJ,YAAY,CAAC;MAC5FG,KAAK,CAACG,IAAI,EAAE;;EAEhB;EAEElB,WAAWA,CAAA;IACX,IAAI,CAACpB,WAAW,CAACuC,WAAW,EAAE,CAACvB,SAAS,CAACwB,IAAI,IAAG;MAChD,IAAI,CAACtC,KAAK,GAAGsC,IAAI;MACjBtB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsB,IAAI,CAACC,SAAS,CAAC,IAAI,CAACxC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EAEA;EACAyC,UAAUA,CAAA;IACV,IAAI,CAAC,IAAI,CAAC7B,cAAc,EAAE,EAAE;MAC1B,OAAO,CAAC;;;IAEVI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACd,OAAO,CAAC;IAEvC,IAAI,CAACL,WAAW,CAAC4C,OAAO,CAAC,IAAI,CAACvC,OAAO,CAAC,CAACW,SAAS,CAAC;MAE/C6B,IAAI,EAAG5B,QAAQ,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,QAAQ,CAAC;QACrD,IAAI,CAACP,gBAAgB,CAAC,SAAS,EAAE,yBAAyB,CAAC;QAE3D,IAAI,CAACR,KAAK,CAAC4C,IAAI,CAAC7B,QAAQ,CAAC;QAIzB,IAAI,CAACR,UAAU,EAAE;QACfa,MAAM,CAACC,QAAQ,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;MACnD,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfR,OAAO,CAACQ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAChB,gBAAgB,CAAC,OAAO,EAAE,2BAA2B,CAAC;MAC7D;KACD,CAAC;EACJ;EAEEqC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC7C,KAAK,CAAC8C,MAAM,CAAEC,CAAC,IAAKA,CAAC,CAACjF,OAAO,CAACkF,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAChD,UAAU,CAAC+C,WAAW,EAAE,CAAC,CAAC;EAClG;EACA7D,UAAUA,CAAC+D,IAAc;IACvB,IAAI,CAAC5C,WAAW;IAChB,IAAI,CAACH,OAAO,GAAG;MAAE,GAAG+C;IAAI,CAAE;IAC1BlC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACd,OAAO,CAAC;IAClD,MAAM2B,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAI5E,SAAS,CAAC6E,KAAK,CAACJ,YAAY,CAAC;MAC/CG,KAAK,CAAC7B,IAAI,EAAE;KACb,MAAM;MACLY,OAAO,CAACQ,KAAK,CAAC,0DAA0D,CAAC;;EAE7E;EAEA2B,UAAUA,CAACC,EAAU;IACnB,IAAI,CAACtD,WAAW,CAACqD,UAAU,CAACC,EAAE,CAAC,CAACtC,SAAS,CAAC,MAAK;MAC7C,IAAI,CAACd,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC8C,MAAM,CAACI,IAAI,IAAIA,IAAI,CAAC3D,MAAM,KAAK6D,EAAE,CAAC;IAC5D,CAAC,CAAC;IACM,IAAI,CAAC5C,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,CAAC;IACnEY,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACnD;EACAjC,aAAaA,CAAC+D,MAAc;IAC1B,MAAMC,WAAW,GAAGlC,MAAM,CAACmC,OAAO,CAAC,4CAA4C,CAAC;IAChF,IAAID,WAAW,EAAE;MACf,IAAI,CAACH,UAAU,CAACE,MAAM,CAAC;;EAE3B;EAGAzC,cAAcA,CAAA;IACZ,IAAI,CAACN,WAAW,EAAE;IAClB,IAAIkD,OAAO,GAAG,IAAI;IAElB;IACA,IAAI,CAAC,IAAI,CAACrD,OAAO,CAACrC,OAAO,IAAI,IAAI,CAACqC,OAAO,CAACrC,OAAO,CAAC2F,IAAI,EAAE,CAAC/D,MAAM,KAAK,CAAC,EAAE;MACrE,IAAI,CAAC7B,YAAY,CAACC,OAAO,GAAG,2BAA2B;MACvD0F,OAAO,GAAG,KAAK;KAChB,MAAM,IAAI,IAAI,CAACrD,OAAO,CAACrC,OAAO,CAAC4B,MAAM,GAAG,CAAC,EAAE;MAC1C,IAAI,CAAC7B,YAAY,CAACC,OAAO,GAAG,oDAAoD;MAChF0F,OAAO,GAAG,KAAK;;IASjB,OAAOA,OAAO;EAChB;EAEAE,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC9C,cAAc,EAAE,EAAE;MACzB;MACAI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACd,OAAO,CAAC;KAC/C,MAAM;MACLa,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACpD,YAAY,CAAC;;EAEzD;EACA8F,QAAQA,CAAA;IACN,IAAI,CAACzC,WAAW,EAAE;EAEpB;;;uBAxLWvB,kBAAkB,EAAArC,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1G,EAAA,CAAAsG,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAlBvE,kBAAkB;MAAAwE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCd/BnH,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAiB,SAAA,cAAsB;UAEtBjB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAiB,SAAA,iBAAyB;UAGrBjB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAiB,SAAA,iBAES;UAETjB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAI/EH,EAAA,CAAAE,MAAA,aACA;UAAAF,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,gCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAAmD;UAAtBD,EAAA,CAAAmB,UAAA,mBAAAkG,qDAAA;YAAA,OAASD,GAAA,CAAArE,SAAA,EAAW;UAAA,EAAC;UAChD/C,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAA4B;UAE+BD,EAAA,CAAAmB,UAAA,2BAAAmG,4DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAzE,UAAA,GAAA4E,MAAA;UAAA,EAAwB;UAA/EvH,EAAA,CAAAG,YAAA,EAAkF;UAClFH,EAAA,CAAAiB,SAAA,gBAAiC;UACnCjB,EAAA,CAAAG,YAAA,EAAM;UAGRH,EAAA,CAAAC,cAAA,eACiD;UAKKD,EAAA,CAAAE,MAAA,8CAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/EH,EAAA,CAAAiB,SAAA,kBAA6G;UAE/GjB,EAAA,CAAAC,cAAA,eAAwB;UAIuCD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5EH,EAAA,CAAAC,cAAA,iBAOE;UAFAD,EAAA,CAAAmB,UAAA,2BAAAqG,4DAAAD,MAAA;YAAA,OAAAH,GAAA,CAAAvE,OAAA,CAAArC,OAAA,GAAA+G,MAAA;UAAA,EAA6B;UAL/BvH,EAAA,CAAAG,YAAA,EAOE;UAEJH,EAAA,CAAAyH,UAAA,KAAAC,kCAAA,kBAAoF;UAC9F1H,EAAA,CAAAiB,SAAA,UAAI;UAEMjB,EAAA,CAAAC,cAAA,eAAkB;UAC6CD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACjFH,EAAA,CAAAC,cAAA,oBAOC;UAFCD,EAAA,CAAAmB,UAAA,2BAAAwG,+DAAAJ,MAAA;YAAA,OAAAH,GAAA,CAAAvE,OAAA,CAAAX,WAAA,GAAAqF,MAAA;UAAA,EAAiC;UAElCvH,EAAA,CAAAG,YAAA,EAAW;UAKlBH,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAA0E;UAAvBD,EAAA,CAAAmB,UAAA,mBAAAyG,qDAAA;YAAA,OAASR,GAAA,CAAA/D,UAAA,EAAY;UAAA,EAAC;UACvErD,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAgBjBH,EAAA,CAAAC,cAAA,eAA2F;UAAtCD,EAAA,CAAAmB,UAAA,mBAAA0G,kDAAAN,MAAA;YAAA,OAASH,GAAA,CAAAjD,mBAAA,CAAAoD,MAAA,CAA2B;UAAA,EAAC;UACxFvH,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAmB,UAAA,mBAAA2G,kDAAAP,MAAA;YAAA,OAASA,MAAA,CAAAQ,eAAA,EAAwB;UAAA,EAAC;UAC3D/H,EAAA,CAAAC,cAAA,gBAA2C;UAAvBD,EAAA,CAAAmB,UAAA,mBAAA6G,mDAAA;YAAA,OAASZ,GAAA,CAAAnE,UAAA,EAAY;UAAA,EAAC;UAACjD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,cAAoD;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjFH,EAAA,CAAAC,cAAA,oBAA8D;UAAxDD,EAAA,CAAAmB,UAAA,sBAAA8G,sDAAA;YAAA,OAAYb,GAAA,CAAAjC,UAAA,EAAY;UAAA,EAAC;UAClCnF,EAAA,CAAAiB,SAAA,UAAI;UAEFjB,EAAA,CAAAC,cAAA,iBAAoG;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzHH,EAAA,CAAAC,cAAA,iBASC;UAJCD,EAAA,CAAAmB,UAAA,2BAAA+G,4DAAAX,MAAA;YAAA,OAAAH,GAAA,CAAAvE,OAAA,CAAArC,OAAA,GAAA+G,MAAA;UAAA,EAA6B;UAL/BvH,EAAA,CAAAG,YAAA,EASC;UACDH,EAAA,CAAAyH,UAAA,KAAAU,kCAAA,kBAAoF;UACpFnI,EAAA,CAAAiB,SAAA,UAAI;UAGFjB,EAAA,CAAAC,cAAA,iBAAgF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrGH,EAAA,CAAAC,cAAA,oBASC;UANCD,EAAA,CAAAmB,UAAA,2BAAAiH,+DAAAb,MAAA;YAAA,OAAAH,GAAA,CAAAvE,OAAA,CAAAX,WAAA,GAAAqF,MAAA;UAAA,EAAiC;UAMlCvH,EAAA,CAAAG,YAAA,EAAW;UAMVH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAUlCH,EAAA,CAAAC,cAAA,eAAiB;UAgG3BD,EAAA,CAAAyH,UAAA,KAAAY,kCAAA,kBAEM;UAENrI,EAAA,CAAAyH,UAAA,KAAAa,kCAAA,mBAsBM;UAIItI,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAmC;UACZD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAC,cAAA,aACW;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAAkD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;UArP1GH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAAzE,UAAA,CAAwB;UAwBrE3C,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAAvE,OAAA,CAAArC,OAAA,CAA6B;UAI3BR,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAU,UAAA,SAAA0G,GAAA,CAAA7G,YAAA,CAAAC,OAAA,CAA0B;UAU5BR,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAAvE,OAAA,CAAAX,WAAA,CAAiC;UA6B5BlC,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAuI,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAAxE,WAAA,EAAiC;UAclD5C,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAAvE,OAAA,CAAArC,OAAA,CAA6B;UAKzBR,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAU,UAAA,SAAA0G,GAAA,CAAA7G,YAAA,CAAAC,OAAA,CAA0B;UAQ9BR,EAAA,CAAAI,SAAA,GAAiC;UAAjCJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAAvE,OAAA,CAAAX,WAAA,CAAiC;UAsH7BlC,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAU,UAAA,SAAA0G,GAAA,CAAAxG,YAAA,CAAAkC,IAAA,CAAuB;UAIP9C,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAU,UAAA,YAAA0G,GAAA,CAAA7B,WAAA,GAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
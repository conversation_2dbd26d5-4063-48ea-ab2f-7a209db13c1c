{"ast": null, "code": "import { Equip } from 'src/app/equipement/equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Historique } from 'src/app/equipement/Historique';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../Shared/layout/layout.component\";\nfunction EquipementsComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r30 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r30);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r30.firstName, \" \", user_r30.lastName, \" - \", user_r30.email, \" \");\n  }\n}\nfunction EquipementsComponent_mat_option_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r31 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r31);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", model_r31.nomModel, \" \");\n  }\n}\nfunction EquipementsComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le mod\\u00E8le est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de s\\u00E9rie est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" La date est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_option_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r32 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r32);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r32.nomFournisseur, \" \");\n  }\n}\nfunction EquipementsComponent_div_93_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Au moins un fournisseur est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"img\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r9.imagePreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction EquipementsComponent_mat_option_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const model_r33 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", model_r33);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", model_r33.nomModel, \" \");\n  }\n}\nfunction EquipementsComponent_div_117_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le mod\\u00E8le est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Le num\\u00E9ro de s\\u00E9rie est requis (min 4 caract\\u00E8res) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" La date est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_option_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 99);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r34 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r34);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r34.nomFournisseur, \" \");\n  }\n}\nfunction EquipementsComponent_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 98);\n    i0.ɵɵtext(1, \" Au moins un fournisseur est requis\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵelement(1, \"img\", 101);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r17 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r17.imagePreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction EquipementsComponent_div_149_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"h5\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r18.selectedEquipement.model == null ? null : ctx_r18.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r18.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementsComponent_mat_option_157_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r35 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r35);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r35.firstName, \" \", user_r35.lastName, \" - \", user_r35.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_158_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \" L'utilisateur est requis\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_175_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 102)(1, \"h5\", 103);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 104);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r22.selectedEquipement.model == null ? null : ctx_r22.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r22.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementsComponent_mat_option_184_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 97);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r36 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r36);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r36.firstName, \" \", user_r36.lastName, \" - \", user_r36.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_185_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 105);\n    i0.ɵɵtext(1, \" L'utilisateur est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_198_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r27 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r27.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r27.notification.message, \" \");\n  }\n}\nfunction EquipementsComponent_tr_225_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r37 = i0.ɵɵnextContext().$implicit;\n    const ctx_r38 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r38.NameUtilisateur[equip_r37.idEqui]), \" \");\n  }\n}\nfunction EquipementsComponent_tr_225_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 124);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_225_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r45);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r43 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r43.openAffectationModal(equip_r37));\n    });\n    i0.ɵɵtext(1, \" Affecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_225_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r48 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 125);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_225_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r48);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r46 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r46.openEditedModal(equip_r37));\n    });\n    i0.ɵɵtext(1, \" \\uD83D\\uDD04 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_225_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r51 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 126);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_225_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r51);\n      const equip_r37 = i0.ɵɵnextContext().$implicit;\n      const ctx_r49 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r49.desaffecterEquipement(equip_r37));\n    });\n    i0.ɵɵtext(1, \" D\\u00E9saffecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_225_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r53 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 107);\n    i0.ɵɵelement(2, \"img\", 108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 107)(4, \"div\", 109)(5, \"h6\", 110);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 111);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 107)(10, \"span\", 112);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 107)(13, \"span\", 112);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 107)(17, \"span\", 113);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementsComponent_tr_225_div_19_Template, 3, 3, \"div\", 114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 107)(21, \"span\", 115);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 107)(24, \"span\", 112);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 116)(27, \"div\", 117);\n    i0.ɵɵtemplate(28, EquipementsComponent_tr_225_button_28_Template, 2, 0, \"button\", 118);\n    i0.ɵɵtemplate(29, EquipementsComponent_tr_225_button_29_Template, 2, 0, \"button\", 119);\n    i0.ɵɵtemplate(30, EquipementsComponent_tr_225_button_30_Template, 2, 0, \"button\", 120);\n    i0.ɵɵelementStart(31, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_225_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const equip_r37 = restoredCtx.$implicit;\n      const ctx_r52 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r52.openModal1(equip_r37));\n    });\n    i0.ɵɵtext(32, \" \\u270F\\uFE0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_225_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r53);\n      const equip_r37 = restoredCtx.$implicit;\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.confirmDelete(equip_r37.idEqui));\n    });\n    i0.ɵɵtext(34, \" \\uD83D\\uDDD1\\uFE0F \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equip_r37 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r37.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r37.model == null ? null : equip_r37.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r37.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 16, equip_r37.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r37.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r37.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r37.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut.toLowerCase() === \"affecte\" || equip_r37.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", equip_r37.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r37.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((equip_r37.fournisseur == null ? null : equip_r37.fournisseur.nomFournisseur) || \"Aucun fournisseur\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"DISPONIBLE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"AFFECTE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r37.statut === \"AFFECTE\");\n  }\n}\nfunction EquipementsComponent_nav_226_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r59 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 129)(1, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_226_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r59);\n      const i_r57 = restoredCtx.index;\n      const ctx_r58 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r58.loadEquipements(i_r57));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r57 = ctx.index;\n    const ctx_r55 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r57 === ctx_r55.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r57 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementsComponent_nav_226_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r61 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 127)(1, \"ul\", 128)(2, \"li\", 129)(3, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_226_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r60 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r60.loadEquipements(ctx_r60.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementsComponent_nav_226_li_5_Template, 3, 3, \"li\", 131);\n    i0.ɵɵelementStart(6, \"li\", 129)(7, \"a\", 130);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_226_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r61);\n      const ctx_r62 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r62.loadEquipements(ctx_r62.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r29 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r29.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r29.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r29.currentPage === ctx_r29.totalPages - 1);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.isModalOpen = false;\n    this.isEditModalOpen = false;\n    this.isAffectationModalOpen = false;\n    this.isAffectationEditModalOpen = false;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.imagePreview = null;\n    this.selectedImage = null;\n    this.signupErrors = {};\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    this.form = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    // FormGroup pour la modification\n    this.editForm = this.fb.group({\n      model: [null, Validators.required],\n      numSerie: ['', [Validators.required, Validators.minLength(4)]],\n      description: [''],\n      dateAffectation: ['', Validators.required],\n      statut: ['DISPONIBLE'],\n      image: [null],\n      fournisseurs: [null, Validators.required]\n    });\n    this.affectationForm = this.fb.group({\n      user: [null, Validators.required],\n      equipement: [null],\n      commentaire: [''],\n      dateAffectation: [new Date()],\n      verrou: ['']\n    });\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    this.form.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire de modification\n    this.editForm.get('model')?.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    this.utilisateurCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateurs = users;\n    });\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  onModelSelected(model) {\n    this.newEquipement1.model = model;\n  }\n  onModelSelectedForAdd(model) {\n    this.form.patchValue({\n      model: model\n    });\n  }\n  onModelSelectedForEdit(model) {\n    this.editForm.patchValue({\n      model: model\n    });\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onModelInputChange(value) {\n    if (!value || typeof value === 'string') {\n      this.newEquipement1.model = null;\n    }\n  }\n  onUserSelected(user) {\n    if (this.isAffectationModalOpen) {\n      this.affectationForm.patchValue({\n        user: user\n      });\n    } else if (this.isAffectationEditModalOpen) {\n      this.EditedAffectation.user = user;\n    }\n    console.log('Utilisateur sélectionné:', user);\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  openModal1(equipement) {\n    const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\n    // Trouver le fournisseur correspondant dans la liste des fournisseurs\n    const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\n    this.newEquipement1 = {\n      ...equipement,\n      model: matchedModel ?? null\n    };\n    // Initialiser le formulaire de modification avec les données de l'équipement\n    this.editForm.patchValue({\n      model: this.newEquipement1.model,\n      numSerie: this.newEquipement1.numSerie,\n      description: this.newEquipement1.description,\n      dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\n      statut: this.newEquipement1.statut,\n      image: null,\n      fournisseurs: matchedFournisseur || null\n    });\n    console.log('Données équipement:', this.newEquipement1);\n    console.log('Fournisseur original:', this.newEquipement1.fournisseur);\n    console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\n    console.log('Date:', this.newEquipement1.dateAffectation);\n    this.modelCtrl.setValue(this.newEquipement1.model);\n    // Affiche la modale\n    this.isEditModalOpen = true;\n  }\n  onEditSubmit() {\n    this.submitted = true;\n    if (this.editForm.invalid) {\n      this.editForm.markAllAsTouched();\n      return;\n    }\n    const equipementData = {\n      ...this.editForm.value,\n      idEqui: this.newEquipement1.idEqui,\n      statut: this.newEquipement1.statut,\n      fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\n    };\n\n    this.authservice.updateEquip(equipementData).subscribe({\n      next: response => {\n        console.log('Update successful:', response);\n        this.showNotification('success', 'Équipement modifié avec succès');\n        this.closeEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const historique = new Historique();\n        historique.date = new Date();\n        historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Update failed:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'équipement');\n      }\n    });\n  }\n  updateData() {\n    console.log('Payload envoyé:', this.newEquipement1);\n    this.authservice.updateEquip(this.newEquipement1).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Équipement modifié avec succès');\n      this.closeModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\n    });\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  deleteEquip(id) {\n    this.authservice.deleteEquip(id).subscribe(() => {\n      this.showNotification('success', 'Équipement supprimé avec succès');\n      this.loadEquipements(this.currentPage);\n    });\n  }\n  enregistrerHistorique(messaege, idEquipement) {\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\n      this.NomEqui = data.equipement.model?.nomModel ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` : null;\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\n      const historique = new Historique();\n      historique.date = data.dateAffectation;\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\n      this.authservice.addHistorique(historique).subscribe({\n        next: response => {\n          console.log('Historique enregistré:', response);\n        },\n        error: error => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n    });\n  }\n  desaffecterEquipement(equip) {\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\n    if (isConfirmed) {\n      // Enregistrer l'historique AVANT de supprimer l'affectation\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\n        next: () => {\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\n            next: () => {\n              this.showNotification('success', 'Équipement désaffecté avec succès');\n              this.loadEquipements(this.currentPage);\n              window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n              });\n            },\n            error: error => {\n              console.error('Erreur lors du changement de statut:', error);\n              this.showNotification('error', 'Erreur lors du changement de statut');\n            }\n          });\n        },\n        error: error => {\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\n          this.showNotification('error', 'Échec de la désaffectation');\n        }\n      });\n    }\n  }\n  confirmDelete(ModelId) {\n    console.log(ModelId);\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\n    if (isConfirmed) {\n      this.deleteEquip(ModelId);\n    }\n  }\n  onAffectationSubmit() {\n    if (this.isAffectationModalOpen) {\n      this.handleNewAffectation();\n    } else if (this.isAffectationEditModalOpen) {\n      this.handleEditAffectation();\n    }\n  }\n  handleNewAffectation() {\n    this.affectationFormSubmitted = true;\n    if (!this.affectationForm.get('user')?.value) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    if (!this.selectedEquipement) {\n      console.error('Aucun équipement sélectionné');\n      return;\n    }\n    // S'assurer que l'équipement a le statut DISPONIBLE par défaut\n    this.selectedEquipement.statut = 'DISPONIBLE';\n    this.affectationForm.patchValue({\n      equipement: this.selectedEquipement\n    });\n    this.affectationForm.patchValue({\n      verrou: 'affecter'\n    });\n    console.log('Form Value:', this.affectationForm.value);\n    this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\n      next: response => {\n        console.log('Statut mis à jour avec succès:', response);\n      },\n      error: error => {\n        console.error('Erreur lors de la mise à jour du statut:', error);\n      }\n    });\n    this.authservice.addAff(this.affectationForm.value).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation créée avec succès !');\n        this.closeAffectationModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique\n        const utilisateur = this.affectationForm.get('user')?.value;\n        const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\n        const numSerie = this.selectedEquipement?.numSerie || 'N/A';\n        const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\n      },\n      error: error => {\n        this.showNotification('error', 'Échec de la création de l\\'affectation');\n      }\n    });\n  }\n  handleEditAffectation() {\n    // Validate the edit form - only user is required\n    if (!this.EditedAffectation.user) {\n      console.log('Form validation failed: User is required');\n      return;\n    }\n    const affectationData = {\n      ...this.EditedAffectation,\n      dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\n    };\n    console.log('Updating affectation:', affectationData);\n    this.authservice.updateAff(affectationData).subscribe({\n      next: response => {\n        this.showNotification('success', 'Affectation modifiée avec succès !');\n        this.closeAffectationEditModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        // Enregistrer dans l'historique manuellement\n        const historique = new Historique();\n        historique.date = new Date();\n        const equipementNom = this.selectedEquipement.model?.nomModel ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})` : 'Équipement inconnu';\n        const utilisateurNom = this.EditedAffectation.user ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim() : 'Utilisateur inconnu';\n        const dateFormatted = new Date().toLocaleDateString('fr-FR');\n        historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique de réaffectation enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\n          }\n        });\n      },\n      error: error => {\n        console.error('Error updating affectation:', error);\n        this.showNotification('error', 'Échec de la modification de l\\'affectation');\n      }\n    });\n  }\n  onRegister() {\n    this.submitted = true;\n    console.log(this.form.value.model);\n    if (this.form.invalid) {\n      this.form.markAllAsTouched(); // 🔥 Triggers all error messages\n      return;\n    }\n    const historique = new Historique();\n    const equipementData = {\n      ...this.form.value,\n      statut: 'DISPONIBLE',\n      fournisseur: this.form.value.fournisseurs || null\n    };\n    console.log(equipementData);\n    this.authservice.addEquipement(equipementData).subscribe({\n      next: response => {\n        historique.date = new Date();\n        historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n        this.authservice.addHistorique(historique).subscribe({\n          next: response => {\n            console.log('Historique enregistré:', response);\n          },\n          error: error => {\n            console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n          }\n        });\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Équipement ajouté avec succès');\n        this.closeModal();\n        this.loadEquipements(this.currentPage); // Refresh the equipment list\n        //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\n      },\n\n      error: error => {\n        console.error('Registration failed:', error);\n        alert('Échec de l’enregistrement');\n      }\n    });\n  }\n  onImageSelected(event) {\n    const file = event.target.files?.[0];\n    if (file) {\n      this.form.patchValue({\n        image: file\n      });\n      this.form.get('image')?.updateValueAndValidity();\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imagePreview = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n    this.resetForm();\n  }\n  closeEditModal() {\n    this.isEditModalOpen = false;\n    this.editForm.reset();\n    this.submitted = false;\n  }\n  closeOnOutsideClickEdit(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeEditModal();\n    }\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  static {\n    this.ɵfac = function EquipementsComponent_Factory(t) {\n      return new (t || EquipementsComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementsComponent,\n      selectors: [[\"app-equipements\"]],\n      decls: 236,\n      vars: 58,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-label\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [\"value\", \"DISPONIBLE\"], [\"value\", \"AFFECTE\"], [\"value\", \"MAINTENANCE\"], [\"value\", \"HORS_SERVICE\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"type\", \"text\", \"matInput\", \"\", \"formControlName\", \"model\", \"placeholder\", \"Rechercher un mod\\u00E8le...\", 3, \"matAutocomplete\"], [\"autoModelEdit\", \"matAutocomplete\"], [\"style\", \"color:red\", 4, \"ngIf\"], [\"for\", \"numSerieEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"numSerieEdit\", \"type\", \"text\", \"formControlName\", \"numSerie\", \"placeholder\", \"Entrer le num\\u00E9ro de s\\u00E9rie\", 1, \"form-inputp\"], [\"for\", \"descriptionEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"descriptionEdit\", \"type\", \"text\", \"formControlName\", \"description\", \"placeholder\", \"Entrer la description (optionnel)\", 1, \"form-inputp\"], [\"for\", \"dateAffectationEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"date\", \"id\", \"dateAffectationEdit\", \"formControlName\", \"dateAffectation\", 1, \"form-control\"], [\"for\", \"fournisseurEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"fournisseurEdit\", \"name\", \"fournisseurEdit\", \"formControlName\", \"fournisseurs\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\"], [\"disabled\", \"\", \"hidden\", \"\", 3, \"ngValue\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [\"for\", \"imageEdit\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"file\", \"id\", \"imageEdit\", \"accept\", \"image/*\", 1, \"form-inputp\", 3, \"change\"], [\"style\", \"margin-top: 10px;\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"btn\", \"btn-primary\"], [\"autoModel\", \"matAutocomplete\"], [\"for\", \"numSerie\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"numSerie\", \"type\", \"text\", \"formControlName\", \"numSerie\", \"placeholder\", \"Entrer le num\\u00E9ro de s\\u00E9rie\", 1, \"form-inputp\"], [\"for\", \"description\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"description\", \"type\", \"text\", \"formControlName\", \"description\", \"placeholder\", \"Entrer la description (optionnel)\", 1, \"form-inputp\"], [\"for\", \"dateAffectation\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"date\", \"id\", \"dateAffectation\", \"formControlName\", \"dateAffectation\", 1, \"form-control\"], [\"for\", \"fournisseur\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"type\", \"name\", \"fournisseur\", \"formControlName\", \"fournisseurs\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\"], [\"for\", \"image\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"file\", \"id\", \"image\", \"accept\", \"image/*\", 1, \"form-inputp\", 3, \"change\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"20px\"], [\"style\", \"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"auto\", \"matAutocomplete\"], [\"style\", \"color:red; font-size: 12px;\", 4, \"ngIf\"], [\"for\", \"commentaire\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"formControlName\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\"], [\"type\", \"date\", \"formControlName\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\"], [\"type\", \"submit\", 1, \"btn-submit\"], [3, \"ngSubmit\"], [\"editAffectationForm\", \"ngForm\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", \"required\", \"\", 3, \"formControl\", \"matAutocomplete\"], [\"name\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"date\", \"name\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", 3, \"ngModel\", \"ngModelChange\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [3, \"value\"], [2, \"color\", \"red\"], [3, \"ngValue\"], [2, \"margin-top\", \"10px\"], [\"alt\", \"Aper\\u00E7u\", 2, \"width\", \"100px\", \"height\", \"auto\", \"border\", \"1px solid #ccc\", \"border-radius\", \"5px\", 3, \"src\"], [2, \"margin-bottom\", \"20px\", \"padding\", \"15px\", \"background-color\", \"#f8f9fa\", \"border-radius\", \"8px\"], [2, \"margin\", \"0\", \"color\", \"#333\"], [2, \"margin\", \"5px 0 0 0\", \"color\", \"#666\", \"font-size\", \"14px\"], [2, \"color\", \"red\", \"font-size\", \"12px\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"fw-normal\", \"text-truncate\", 2, \"max-width\", \"150px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-1\", \"text-end\"], [1, \"d-flex\", \"justify-content-end\", \"gap-1\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-warning\", \"title\", \"R\\u00E9affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-secondary\", \"title\", \"D\\u00E9saffecter\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-muted\", \"small\", \"mt-1\"], [\"title\", \"Affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"R\\u00E9affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-warning\", 3, \"click\"], [\"title\", \"D\\u00E9saffecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function EquipementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel Panne \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementsComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 17)(42, \"label\", 23);\n          i0.ɵɵtext(43, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"select\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_select_ngModelChange_44_listener($event) {\n            return ctx.selectedStatut = $event;\n          })(\"change\", function EquipementsComponent_Template_select_change_44_listener() {\n            return ctx.loadEquipements(0);\n          });\n          i0.ɵɵelementStart(45, \"option\", 25);\n          i0.ɵɵtext(46, \"Tous les statuts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"option\", 26);\n          i0.ɵɵtext(48, \"Disponible\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 27);\n          i0.ɵɵtext(50, \"Affect\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 28);\n          i0.ɵɵtext(52, \"En maintenance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"option\", 29);\n          i0.ɵɵtext(54, \"Hors service\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(55, \"div\", 30)(56, \"div\", 31)(57, \"input\", 32);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_57_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementsComponent_Template_input_input_57_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"span\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_59_listener($event) {\n            return ctx.closeOnOutsideClickEdit($event);\n          });\n          i0.ɵɵelementStart(60, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_60_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(61, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_61_listener() {\n            return ctx.closeEditModal();\n          });\n          i0.ɵɵtext(62, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"h3\", 37);\n          i0.ɵɵtext(64, \"Modifier l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"form\", 38);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_65_listener() {\n            return ctx.onEditSubmit();\n          });\n          i0.ɵɵelement(66, \"br\")(67, \"br\");\n          i0.ɵɵelementStart(68, \"mat-form-field\", 18)(69, \"mat-label\");\n          i0.ɵɵtext(70, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(71, \"input\", 39);\n          i0.ɵɵelementStart(72, \"mat-autocomplete\", 20, 40);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_72_listener($event) {\n            return ctx.onModelSelectedForEdit($event.option.value);\n          });\n          i0.ɵɵtemplate(74, EquipementsComponent_mat_option_74_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(75, EquipementsComponent_div_75_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(76, \"label\", 42);\n          i0.ɵɵtext(77, \"Num\\u00E9ro de s\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(78, \"input\", 43);\n          i0.ɵɵtemplate(79, EquipementsComponent_div_79_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(80, \"label\", 44);\n          i0.ɵɵtext(81, \"Description (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"input\", 45);\n          i0.ɵɵelementStart(83, \"label\", 46);\n          i0.ɵɵtext(84, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(85, \"input\", 47);\n          i0.ɵɵtemplate(86, EquipementsComponent_div_86_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(87, \"label\", 48);\n          i0.ɵɵtext(88, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"select\", 49)(90, \"option\", 50);\n          i0.ɵɵtext(91, \"S\\u00E9lectionner fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(92, EquipementsComponent_option_92_Template, 2, 2, \"option\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(93, EquipementsComponent_div_93_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(94, \"label\", 52);\n          i0.ɵɵtext(95, \"Logo d'\\u00E9quipement (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"input\", 53);\n          i0.ɵɵlistener(\"change\", function EquipementsComponent_Template_input_change_96_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(97, EquipementsComponent_div_97_Template, 2, 1, \"div\", 54);\n          i0.ɵɵelement(98, \"br\");\n          i0.ɵɵelementStart(99, \"button\", 55);\n          i0.ɵɵtext(100, \" Modifier \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(101, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_101_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(102, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_102_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(103, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_103_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(104, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"h3\", 37);\n          i0.ɵɵtext(106, \"Ajouter un nouvel Equipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"form\", 38);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_107_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(108, \"br\")(109, \"br\");\n          i0.ɵɵelementStart(110, \"mat-form-field\", 18)(111, \"mat-label\");\n          i0.ɵɵtext(112, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(113, \"input\", 39);\n          i0.ɵɵelementStart(114, \"mat-autocomplete\", 20, 56);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_114_listener($event) {\n            return ctx.onModelSelectedForAdd($event.option.value);\n          });\n          i0.ɵɵtemplate(116, EquipementsComponent_mat_option_116_Template, 2, 2, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(117, EquipementsComponent_div_117_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(118, \"label\", 57);\n          i0.ɵɵtext(119, \"Num\\u00E9ro de s\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(120, \"input\", 58);\n          i0.ɵɵtemplate(121, EquipementsComponent_div_121_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(122, \"label\", 59);\n          i0.ɵɵtext(123, \"Description (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(124, \"input\", 60);\n          i0.ɵɵelementStart(125, \"label\", 61);\n          i0.ɵɵtext(126, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(127, \"input\", 62);\n          i0.ɵɵtemplate(128, EquipementsComponent_div_128_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(129, \"label\", 63);\n          i0.ɵɵtext(130, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(131, \"select\", 64)(132, \"option\", 50);\n          i0.ɵɵtext(133, \"S\\u00E9lectionner fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(134, EquipementsComponent_option_134_Template, 2, 2, \"option\", 51);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(135, EquipementsComponent_div_135_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(136, \"label\", 65);\n          i0.ɵɵtext(137, \"Logo d'\\u00E9quipement (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"input\", 66);\n          i0.ɵɵlistener(\"change\", function EquipementsComponent_Template_input_change_138_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(139, EquipementsComponent_div_139_Template, 2, 1, \"div\", 54);\n          i0.ɵɵelement(140, \"br\");\n          i0.ɵɵelementStart(141, \"button\", 55);\n          i0.ɵɵtext(142, \" Enregistrer \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(143, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_143_listener($event) {\n            return ctx.closeOnOutsideClickAffectation($event);\n          });\n          i0.ɵɵelementStart(144, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_144_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(145, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_145_listener() {\n            return ctx.closeAffectationModal();\n          });\n          i0.ɵɵtext(146, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"h3\", 67);\n          i0.ɵɵtext(148, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(149, EquipementsComponent_div_149_Template, 5, 2, \"div\", 68);\n          i0.ɵɵelementStart(150, \"form\", 69);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_150_listener() {\n            return ctx.onAffectationSubmit();\n          });\n          i0.ɵɵelementStart(151, \"mat-form-field\", 18)(152, \"mat-label\");\n          i0.ɵɵtext(153, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(154, \"input\", 19);\n          i0.ɵɵelementStart(155, \"mat-autocomplete\", 20, 70);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_155_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(157, EquipementsComponent_mat_option_157_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(158, EquipementsComponent_div_158_Template, 2, 0, \"div\", 71);\n          i0.ɵɵelementStart(159, \"label\", 72);\n          i0.ɵɵtext(160, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(161, \"textarea\", 73);\n          i0.ɵɵtext(162, \"      \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"label\", 61);\n          i0.ɵɵtext(164, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(165, \"input\", 74)(166, \"br\");\n          i0.ɵɵelementStart(167, \"button\", 75);\n          i0.ɵɵtext(168, \" Affecter l'\\u00E9quipement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(169, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_169_listener($event) {\n            return ctx.closeOnOutsideClickAffectationEdit($event);\n          });\n          i0.ɵɵelementStart(170, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_170_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(171, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_171_listener() {\n            return ctx.closeAffectationEditModal();\n          });\n          i0.ɵɵtext(172, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(173, \"h3\", 67);\n          i0.ɵɵtext(174, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(175, EquipementsComponent_div_175_Template, 5, 2, \"div\", 68);\n          i0.ɵɵelementStart(176, \"form\", 76, 77);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_176_listener() {\n            return ctx.selectedEquipement && ctx.updateReaffication(ctx.selectedEquipement);\n          });\n          i0.ɵɵelementStart(178, \"mat-form-field\", 18)(179, \"mat-label\");\n          i0.ɵɵtext(180, \"Utilisateur Actuel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(181, \"input\", 78);\n          i0.ɵɵelementStart(182, \"mat-autocomplete\", 20, 70);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_182_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(184, EquipementsComponent_mat_option_184_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(185, EquipementsComponent_div_185_Template, 2, 0, \"div\", 71);\n          i0.ɵɵelementStart(186, \"label\", 72);\n          i0.ɵɵtext(187, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(188, \"textarea\", 79);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_textarea_ngModelChange_188_listener($event) {\n            return ctx.EditedAffectation.commentaire = $event;\n          });\n          i0.ɵɵtext(189, \"  \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(190, \"label\", 61);\n          i0.ɵɵtext(191, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(192, \"input\", 80);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_192_listener($event) {\n            return ctx.EditedAffectation.dateAffectation = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(193, \"br\");\n          i0.ɵɵelementStart(194, \"button\", 75);\n          i0.ɵɵtext(195, \" Modifier Affectation \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(196, \"body\", 81)(197, \"div\", 82);\n          i0.ɵɵtemplate(198, EquipementsComponent_div_198_Template, 2, 2, \"div\", 83);\n          i0.ɵɵelementStart(199, \"div\", 7)(200, \"div\", 84)(201, \"div\", 85)(202, \"div\", 86)(203, \"div\", 14)(204, \"div\", 87)(205, \"table\", 88)(206, \"thead\")(207, \"tr\")(208, \"th\", 89);\n          i0.ɵɵtext(209, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(210, \"th\", 89);\n          i0.ɵɵtext(211, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(212, \"th\", 89);\n          i0.ɵɵtext(213, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(214, \"th\", 89);\n          i0.ɵɵtext(215, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(216, \"th\", 89);\n          i0.ɵɵtext(217, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(218, \"th\", 89);\n          i0.ɵɵtext(219, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(220, \"th\", 89);\n          i0.ɵɵtext(221, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(222, \"th\", 90);\n          i0.ɵɵtext(223, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(224, \"tbody\");\n          i0.ɵɵtemplate(225, EquipementsComponent_tr_225_Template, 35, 19, \"tr\", 91);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(226, EquipementsComponent_nav_226_Template, 9, 6, \"nav\", 92);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(227, \"div\", 84)(228, \"div\", 93)(229, \"p\", 94);\n          i0.ɵɵtext(230, \"Design and Developed by \");\n          i0.ɵɵelementStart(231, \"a\", 95);\n          i0.ɵɵtext(232, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(233, \" Distributed by \");\n          i0.ɵɵelementStart(234, \"a\", 96);\n          i0.ɵɵtext(235, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          const _r2 = i0.ɵɵreference(73);\n          const _r10 = i0.ɵɵreference(115);\n          const _r19 = i0.ɵɵreference(156);\n          let tmp_11_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          let tmp_16_0;\n          let tmp_23_0;\n          let tmp_24_0;\n          let tmp_25_0;\n          let tmp_28_0;\n          let tmp_37_0;\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedStatut);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(50, _c1, ctx.isEditModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r2);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modelet);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.editForm.get(\"model\")) == null ? null : tmp_11_0.invalid) && (((tmp_11_0 = ctx.editForm.get(\"model\")) == null ? null : tmp_11_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.editForm.get(\"numSerie\")) == null ? null : tmp_12_0.invalid) && (((tmp_12_0 = ctx.editForm.get(\"numSerie\")) == null ? null : tmp_12_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.editForm.get(\"dateAffectation\")) == null ? null : tmp_13_0.invalid) && (((tmp_13_0 = ctx.editForm.get(\"dateAffectation\")) == null ? null : tmp_13_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx.editForm.get(\"fournisseurs\")) == null ? null : tmp_16_0.invalid) && (((tmp_16_0 = ctx.editForm.get(\"fournisseurs\")) == null ? null : tmp_16_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.imagePreview);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(52, _c1, ctx.isModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"matAutocomplete\", _r10);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.modelet);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx.form.get(\"model\")) == null ? null : tmp_23_0.invalid) && (((tmp_23_0 = ctx.form.get(\"model\")) == null ? null : tmp_23_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_24_0 = ctx.form.get(\"numSerie\")) == null ? null : tmp_24_0.invalid) && (((tmp_24_0 = ctx.form.get(\"numSerie\")) == null ? null : tmp_24_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_25_0 = ctx.form.get(\"dateAffectation\")) == null ? null : tmp_25_0.invalid) && (((tmp_25_0 = ctx.form.get(\"dateAffectation\")) == null ? null : tmp_25_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_28_0 = ctx.form.get(\"fournisseurs\")) == null ? null : tmp_28_0.invalid) && (((tmp_28_0 = ctx.form.get(\"fournisseurs\")) == null ? null : tmp_28_0.touched) || ctx.submitted));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.imagePreview);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(54, _c1, ctx.isAffectationModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.affectationForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r19);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_37_0 = ctx.affectationForm.get(\"user\")) == null ? null : tmp_37_0.value) && ctx.affectationFormSubmitted);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(56, _c1, ctx.isAffectationEditModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r19);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.EditedAffectation.user && ctx.editAffectationFormSubmitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.commentaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.dateAffectation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, i3.FormControlDirective, i3.FormGroupDirective, i3.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "Historique", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r30", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "model_r31", "ɵɵtextInterpolate1", "nomModel", "fournisseur_r32", "nomFournisseur", "ɵɵelement", "ctx_r9", "imagePreview", "ɵɵsanitizeUrl", "model_r33", "fournisseur_r34", "ctx_r17", "ɵɵtextInterpolate", "ctx_r18", "selectedEquipement", "model", "numSerie", "user_r35", "ctx_r22", "user_r36", "ctx_r27", "notification", "type", "message", "ɵɵpipeBind1", "ctx_r38", "NameUtilisateur", "equip_r37", "idEqui", "ɵɵlistener", "EquipementsComponent_tr_225_button_28_Template_button_click_0_listener", "ɵɵrestoreView", "_r45", "ɵɵnextContext", "$implicit", "ctx_r43", "ɵɵresetView", "openAffectationModal", "EquipementsComponent_tr_225_button_29_Template_button_click_0_listener", "_r48", "ctx_r46", "openEditedModal", "EquipementsComponent_tr_225_button_30_Template_button_click_0_listener", "_r51", "ctx_r49", "desaffecterEquipement", "ɵɵtemplate", "EquipementsComponent_tr_225_div_19_Template", "EquipementsComponent_tr_225_button_28_Template", "EquipementsComponent_tr_225_button_29_Template", "EquipementsComponent_tr_225_button_30_Template", "EquipementsComponent_tr_225_Template_button_click_31_listener", "restoredCtx", "_r53", "ctx_r52", "openModal1", "EquipementsComponent_tr_225_Template_button_click_33_listener", "ctx_r54", "confirmDelete", "image", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "description", "<PERSON><PERSON><PERSON><PERSON>", "EquipementsComponent_nav_226_li_5_Template_a_click_1_listener", "_r59", "i_r57", "index", "ctx_r58", "loadEquipements", "ɵɵclassProp", "ctx_r55", "currentPage", "EquipementsComponent_nav_226_Template_a_click_3_listener", "_r61", "ctx_r60", "EquipementsComponent_nav_226_li_5_Template", "EquipementsComponent_nav_226_Template_a_click_7_listener", "ctx_r62", "ctx_r29", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "EquipementsComponent", "authservice", "http", "fb", "utilisateurService", "isModalOpen", "isEditModalOpen", "isAffectationModalOpen", "isAffectationEditModalOpen", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "selectedImage", "signupErrors", "ngOnInit", "GetAllModels", "getFournisseur", "form", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "editForm", "affectationForm", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "get", "searchUsers", "users", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "displayUtilisateur", "displayModel", "onModelSelected", "onModelSelectedForAdd", "patchValue", "onModelSelectedForEdit", "getallFournisseur", "data", "onModelInputChange", "onUserSelected", "log", "onUserSearchSelected", "closeOnOutsideClick", "event", "target", "classList", "contains", "closeModal", "matchedModel", "find", "m", "idModel", "matchedFournisseur", "f", "idFournisseur", "formatDateForInput", "onEditSubmit", "invalid", "mark<PERSON>llAsTouched", "equipementData", "updateEquip", "response", "showNotification", "closeEditModal", "historique", "date", "addHistorique", "updateData", "page", "keyword", "username", "userVal", "searchEquipements1", "getAllEquipements", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "deleteEquip", "enregistrerHistorique", "messaege", "idEquipement", "getAffectationById", "dateFormatted", "toLocaleDateString", "equip", "isConfirmed", "window", "confirm", "deleteAff", "addStatutDisponible", "scrollTo", "top", "behavior", "ModelId", "onAffectationSubmit", "handleNewAffectation", "handleEditAffectation", "addStatutAffecte", "addAff", "closeAffectationModal", "utilisateur", "equipementNom", "utilisateurNom", "affectationData", "updateAff", "closeAffectationEditModal", "onRegister", "addEquipement", "alert", "onImageSelected", "file", "files", "updateValueAndValidity", "reader", "FileReader", "onload", "result", "readAsDataURL", "onFileSelected", "formData", "FormData", "append", "post", "imageUrl", "fullUrl", "resetErrors", "getAllModel", "openModal", "resetForm", "reset", "closeOnOutsideClickEdit", "setTimeout", "hideNotification", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementsComponent_Template", "rf", "ctx", "EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementsComponent_mat_option_40_Template", "EquipementsComponent_Template_select_ngModelChange_44_listener", "EquipementsComponent_Template_select_change_44_listener", "EquipementsComponent_Template_input_ngModelChange_57_listener", "EquipementsComponent_Template_input_input_57_listener", "EquipementsComponent_Template_div_click_59_listener", "EquipementsComponent_Template_div_click_60_listener", "stopPropagation", "EquipementsComponent_Template_span_click_61_listener", "EquipementsComponent_Template_form_ngSubmit_65_listener", "EquipementsComponent_Template_mat_autocomplete_optionSelected_72_listener", "EquipementsComponent_mat_option_74_Template", "EquipementsComponent_div_75_Template", "EquipementsComponent_div_79_Template", "EquipementsComponent_div_86_Template", "EquipementsComponent_option_92_Template", "EquipementsComponent_div_93_Template", "EquipementsComponent_Template_input_change_96_listener", "EquipementsComponent_div_97_Template", "EquipementsComponent_Template_div_click_101_listener", "EquipementsComponent_Template_div_click_102_listener", "EquipementsComponent_Template_span_click_103_listener", "EquipementsComponent_Template_form_ngSubmit_107_listener", "EquipementsComponent_Template_mat_autocomplete_optionSelected_114_listener", "EquipementsComponent_mat_option_116_Template", "EquipementsComponent_div_117_Template", "EquipementsComponent_div_121_Template", "EquipementsComponent_div_128_Template", "EquipementsComponent_option_134_Template", "EquipementsComponent_div_135_Template", "EquipementsComponent_Template_input_change_138_listener", "EquipementsComponent_div_139_Template", "EquipementsComponent_Template_div_click_143_listener", "closeOnOutsideClickAffectation", "EquipementsComponent_Template_div_click_144_listener", "EquipementsComponent_Template_span_click_145_listener", "EquipementsComponent_div_149_Template", "EquipementsComponent_Template_form_ngSubmit_150_listener", "EquipementsComponent_Template_mat_autocomplete_optionSelected_155_listener", "EquipementsComponent_mat_option_157_Template", "EquipementsComponent_div_158_Template", "EquipementsComponent_Template_div_click_169_listener", "closeOnOutsideClickAffectationEdit", "EquipementsComponent_Template_div_click_170_listener", "EquipementsComponent_Template_span_click_171_listener", "EquipementsComponent_div_175_Template", "EquipementsComponent_Template_form_ngSubmit_176_listener", "updateReaffication", "EquipementsComponent_Template_mat_autocomplete_optionSelected_182_listener", "EquipementsComponent_mat_option_184_Template", "EquipementsComponent_div_185_Template", "EquipementsComponent_Template_textarea_ngModelChange_188_listener", "EquipementsComponent_Template_input_ngModelChange_192_listener", "EquipementsComponent_div_198_Template", "EquipementsComponent_tr_225_Template", "EquipementsComponent_nav_226_Template", "_r0", "ɵɵpureFunction1", "_c1", "_r2", "tmp_11_0", "touched", "tmp_12_0", "tmp_13_0", "tmp_16_0", "_r10", "tmp_23_0", "tmp_24_0", "tmp_25_0", "tmp_28_0", "_r19", "tmp_37_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.html"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\nisModalOpen = false;\nisEditModalOpen = false;\nisAffectationModalOpen = false;\nisAffectationEditModalOpen = false;\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[]; \n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) { }\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\nthis.form = this.fb.group({\n  model: [null, Validators.required],\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\n  description: [''], \n  dateAffectation: ['', Validators.required],\n  statut: ['DISPONIBLE'], \n  image: [null], \n  fournisseurs: [null, Validators.required]\n});\n\n// FormGroup pour la modification\nthis.editForm = this.fb.group({\n  model: [null, Validators.required],\n  numSerie: ['', [Validators.required, Validators.minLength(4)]],\n  description: [''], // Description optionnelle - pas de validation\n  dateAffectation: ['', Validators.required],\n  statut: ['DISPONIBLE'], // Statut par défaut, pas de validation requise\n  image: [null], // Image optionnelle - pas de validation\n  fournisseurs: [null, Validators.required]\n});\n\n\n\n\nthis.affectationForm = this.fb.group({\n  user: [null, Validators.required],\n  equipement: [null],\n  commentaire: [''], // No validation - optional\n  dateAffectation: [new Date()], // No validation - optional\n  verrou: ['']\n});\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\nthis.form.get('model')?.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire de modification\nthis.editForm.get('model')?.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n\nthis.utilisateurCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchUsers(value.trim());\n        } else {\n\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateurs = users;\n  });\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\nonModelSelected(model: any): void {\n  this.newEquipement1.model = model;\n}\n\nonModelSelectedForAdd(model: any): void {\n  this.form.patchValue({ model: model });\n}\n\nonModelSelectedForEdit(model: any): void {\n  this.editForm.patchValue({ model: model });\n}\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n  onModelInputChange(value: string) {\n\n  if (!value || typeof value === 'string') {\n    this.newEquipement1.model = null;\n  }\n}\n\n\n\n\n\n\n\n\n  onUserSelected(user: Utilisateur) {\n\n    if (this.isAffectationModalOpen) {\n\n      this.affectationForm.patchValue({\n        user: user\n      });\n    } else if (this.isAffectationEditModalOpen) {\n\n      this.EditedAffectation.user = user;\n    }\n    console.log('Utilisateur sélectionné:', user);\n  }\n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n  closeOnOutsideClick(event: MouseEvent) {\n    if ((event.target as HTMLElement).classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n\nopenModal1(equipement: Equip) {\n  const matchedModel = this.models.find(m => m.idModel === equipement.model?.idModel);\n\n  // Trouver le fournisseur correspondant dans la liste des fournisseurs\n  const matchedFournisseur = this.fournisseurs.find(f => f.idFournisseur === equipement.fournisseur?.idFournisseur);\n\n  this.newEquipement1 = {\n    ...equipement,\n    model: matchedModel ?? null\n  };\n\n  // Initialiser le formulaire de modification avec les données de l'équipement\n  this.editForm.patchValue({\n    model: this.newEquipement1.model,\n    numSerie: this.newEquipement1.numSerie,\n    description: this.newEquipement1.description,\n    dateAffectation: this.formatDateForInput(this.newEquipement1.dateAffectation),\n    statut: this.newEquipement1.statut,\n    image: null,\n    fournisseurs: matchedFournisseur || null\n  });\n\n  console.log('Données équipement:', this.newEquipement1);\n  console.log('Fournisseur original:', this.newEquipement1.fournisseur);\n  console.log('Fournisseur trouvé dans la liste:', matchedFournisseur);\n  console.log('Date:', this.newEquipement1.dateAffectation);\n\n  this.modelCtrl.setValue(this.newEquipement1.model);\n\n  // Affiche la modale\n  this.isEditModalOpen = true;\n}\n\nonEditSubmit(): void {\n  this.submitted = true;\n\n  if (this.editForm.invalid) {\n    this.editForm.markAllAsTouched(); \n    return;\n  }\n\n  const equipementData = {\n    ...this.editForm.value,\n    idEqui: this.newEquipement1.idEqui, // Garder l'ID original\n    statut: this.newEquipement1.statut, // Préserver le statut original\n    fournisseur: this.editForm.value.fournisseurs || null // S'assurer que fournisseur n'est jamais undefined\n  };\n\n  this.authservice.updateEquip(equipementData).subscribe({\n    next: (response) => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Équipement modifié avec succès');\n      this.closeEditModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n      const historique = new Historique();\n      historique.date = new Date();\n      historique.commentaire = `Modification de l'équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\n      this.authservice.addHistorique(historique).subscribe({\n        next: (response) => {\n          console.log('Historique enregistré:', response);\n        },\n        error: (error) => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n    },\n    error: (error) => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\n    }\n  });\n}\n\nupdateData() {\n  console.log('Payload envoyé:', this.newEquipement1);\n  this.authservice.updateEquip(this.newEquipement1).subscribe(\n    (response) => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Équipement modifié avec succès');\n      this.closeModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n\n    },\n    (error) => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'équipement');\n    }\n  );\n}\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n  equiements.forEach(eq => {\n   \n    this.idsEqui[eq.idEqui]=eq.idEqui;\n     })\n     console.log(this.idsEqui); \n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n   deleteEquip(id: number) {\n    this.authservice.deleteEquip(id).subscribe(() => {\n      this.showNotification('success', 'Équipement supprimé avec succès');\n      this.loadEquipements(this.currentPage); \n\n\n    });\n  }\n\n\n\n\n  private enregistrerHistorique(messaege: string, idEquipement: number) {\n    this.authservice.getAffectationById(idEquipement).subscribe(data => {\n    this.NomEqui = data.equipement.model?.nomModel \n  ? `${data.equipement.model.nomModel} (N° Série:${data.equipement.numSerie})` \n  : null;\n\n      this.NomUser = data.user.firstName + \" \" + data.user.lastName;\n\n      const historique = new Historique();\n      historique.date = data.dateAffectation;\n      const dateFormatted = data.dateAffectation ? new Date(data.dateAffectation).toLocaleDateString('fr-FR') : new Date().toLocaleDateString('fr-FR');\n      historique.commentaire = `${this.NomEqui} ${messaege} ${this.NomUser} le ${dateFormatted}`;\n\n      this.authservice.addHistorique(historique).subscribe({\n        next: (response) => {\n          console.log('Historique enregistré:', response);\n        },\n        error: (error) => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n    });\n  }\n\n  desaffecterEquipement(equip: Equip) {\n    const isConfirmed = window.confirm('Êtes-vous sûr de vouloir désaffecter cet équipement ?');\n    if (isConfirmed) {\n      // Enregistrer l'historique AVANT de supprimer l'affectation\n      this.enregistrerHistorique(`A ete desaffecte de `, equip.idEqui);\n\n      this.authservice.deleteAff(this.tableAffectation[equip.idEqui].id).subscribe({\n        next: () => {\n          this.authservice.addStatutDisponible(equip.idEqui).subscribe({\n            next: () => {\n              this.showNotification('success', 'Équipement désaffecté avec succès');\n              this.loadEquipements(this.currentPage);\n              window.scrollTo({ top: 0, behavior: 'smooth' });\n            },\n            error: (error) => {\n              console.error('Erreur lors du changement de statut:', error);\n              this.showNotification('error', 'Erreur lors du changement de statut');\n            }\n          });\n        },\n        error: (error) => {\n          console.error('Erreur lors de la suppression de l\\'affectation:', error);\n          this.showNotification('error', 'Échec de la désaffectation');\n        }\n      });\n    }\n  }\n\n\n   confirmDelete(ModelId: number): void {\n    console.log(ModelId);\n    const isConfirmed = window.confirm('Are you sure you want to delete this item?');\n    if (isConfirmed) {\n      this.deleteEquip(ModelId);\n    }\n  }\nonAffectationSubmit() {\n  \n  if (this.isAffectationModalOpen) {\n\n    this.handleNewAffectation();\n  } else if (this.isAffectationEditModalOpen) {\n   \n    this.handleEditAffectation();\n  }\n}\n\nprivate handleNewAffectation() {\n  this.affectationFormSubmitted = true;\n\n  if (!this.affectationForm.get('user')?.value) {\n    console.log('Form validation failed: User is required');\n    return;\n  }\n\n  if (!this.selectedEquipement) {\n    console.error('Aucun équipement sélectionné');\n    return;\n  }\n\n  // S'assurer que l'équipement a le statut DISPONIBLE par défaut\n  this.selectedEquipement.statut = 'DISPONIBLE';\n  this.affectationForm.patchValue({ equipement: this.selectedEquipement });\n  this.affectationForm.patchValue({ verrou:'affecter' });\n\n  console.log('Form Value:', this.affectationForm.value);\n  this.authservice.addStatutAffecte(this.selectedEquipement.idEqui).subscribe({\n    next: (response) => {\n      console.log('Statut mis à jour avec succès:', response);\n    },\n    error: (error) => {\n      console.error('Erreur lors de la mise à jour du statut:', error);\n    }\n  });\n  this.authservice.addAff(this.affectationForm.value).subscribe({\n    next: (response: any) => {\n      this.showNotification('success', 'Affectation créée avec succès !');\n      this.closeAffectationModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      // Enregistrer dans l'historique\n      const utilisateur = this.affectationForm.get('user')?.value;\n      const equipementNom = this.selectedEquipement?.model?.nomModel || 'Équipement inconnu';\n      const numSerie = this.selectedEquipement?.numSerie || 'N/A';\n      const utilisateurNom = utilisateur ? `${utilisateur.firstName || ''} ${utilisateur.lastName || ''}`.trim() : 'Utilisateur inconnu';\n      this.enregistrerHistorique(`est affecté à `, this.selectedEquipement.idEqui);\n    },\n    error: (error) => {\n      this.showNotification('error', 'Échec de la création de l\\'affectation');\n    }\n  });\n}\n\nprivate handleEditAffectation() {\n  // Validate the edit form - only user is required\n  if (!this.EditedAffectation.user) {\n    console.log('Form validation failed: User is required');\n    return;\n  }\n\n  \n  const affectationData = {\n    ...this.EditedAffectation,\n    dateAffectation: this.EditedAffectation.dateAffectation ? new Date(this.EditedAffectation.dateAffectation) : new Date()\n  };\n\n  console.log('Updating affectation:', affectationData);\n\n  this.authservice.updateAff(affectationData).subscribe({\n    next: (response: any) => {\n      this.showNotification('success', 'Affectation modifiée avec succès !');\n      this.closeAffectationEditModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n\n      // Enregistrer dans l'historique manuellement\n      const historique = new Historique();\n      historique.date = new Date();\n\n      const equipementNom = this.selectedEquipement.model?.nomModel\n        ? `${this.selectedEquipement.model.nomModel} (N° Série: ${this.selectedEquipement.numSerie})`\n        : 'Équipement inconnu';\n\n      const utilisateurNom = this.EditedAffectation.user\n        ? `${this.EditedAffectation.user.firstName || ''} ${this.EditedAffectation.user.lastName || ''}`.trim()\n        : 'Utilisateur inconnu';\n\n      const dateFormatted = new Date().toLocaleDateString('fr-FR');\n      historique.commentaire = `${equipementNom} a été réaffecté à ${utilisateurNom} le ${dateFormatted}`;\n\n      this.authservice.addHistorique(historique).subscribe({\n        next: (response) => {\n          console.log('Historique de réaffectation enregistré:', response);\n        },\n        error: (error) => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique de réaffectation:', error);\n        }\n      });\n    },\n    error: (error) => {\n      console.error('Error updating affectation:', error);\n      this.showNotification('error', 'Échec de la modification de l\\'affectation');\n    }\n  });\n}\n\n\n\n\n    \n  onRegister(): void {\n  this.submitted = true;\n\nconsole.log(this.form.value.model);\nif (this.form.invalid) {\n    this.form.markAllAsTouched(); // 🔥 Triggers all error messages\n    return;\n  }\nconst historique = new Historique();\n\n  const equipementData = {\n    ...this.form.value,\n    statut: 'DISPONIBLE', \n    fournisseur: this.form.value.fournisseurs || null \n  };\nconsole.log(equipementData);\n  this.authservice.addEquipement(equipementData).subscribe({\n    next: (response) => {\n      historique.date = new Date();\n      historique.commentaire = `Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`;\nthis.authservice.addHistorique(historique).subscribe({\n        next: (response) => {\n          console.log('Historique enregistré:', response);\n        },\n        error: (error) => {\n          console.error('Erreur lors de l\\'enregistrement de l\\'historique:', error);\n        }\n      });\n      console.log('User registered successfully', response);\n      this.showNotification('success', 'Équipement ajouté avec succès');\n      this.closeModal();\n      this.loadEquipements(this.currentPage); // Refresh the equipment list\n      //this.enregistrerHistorique(`Ajout d'un nouvel équipement: ${equipementData.model?.nomModel} (N° Série: ${equipementData.numSerie})`);\n    },\n    error: (error) => {\n      console.error('Registration failed:', error);\n      alert('Échec de l’enregistrement');\n    }\n  });\n}\n\n\n  imagePreview: string | ArrayBuffer | null = null;\nselectedImage: File | null = null;\n\nonImageSelected(event: Event): void {\n  const file = (event.target as HTMLInputElement).files?.[0];\n  if (file) {\n    this.form.patchValue({ image: file });\n    this.form.get('image')?.updateValueAndValidity();\n\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.imagePreview = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n}\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n  openModal() {\n    this.isModalOpen = true;\n  }\n\n  closeModal() {\n    this.isModalOpen = false;\n    this.resetForm();\n  }\n\n  closeEditModal() {\n    this.isEditModalOpen = false;\n    this.editForm.reset();\n    this.submitted = false;\n  }\n\n  closeOnOutsideClickEdit(event: MouseEvent) {\n    if ((event.target as HTMLElement).classList.contains('modal')) {\n      this.closeEditModal();\n    }\n  }\n\n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n}\n\n  \n\n\n\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Équipements</h2>\n    <p>Gérez les différents types d'équipements informatiques\n\n</p>\n  </div>\n<button class=\"add-user-btn\" >\n  <span class=\"icon\">+</span>Nouvel Panne \n\n</button>\n</div>\n\n<!-- Formulaire de recherche simple -->\n<div class=\"card mt-3 mb-4\">\n  <div class=\"card-body\">\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\n\n    <div class=\"row g-3\">\n      <!-- Recherche par utilisateur -->\n      <div class=\"col-md-6\">\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n          <mat-label>Utilisateur</mat-label>\n          <input\n            type=\"text\"\n            matInput\n            [formControl]=\"utilisateurSearchCtrl\"\n            [matAutocomplete]=\"autoUserSearch\"\n            placeholder=\"Rechercher un utilisateur...\">\n\n          <mat-autocomplete\n            #autoUserSearch=\"matAutocomplete\"\n            [displayWith]=\"displayUtilisateur\"\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n            </mat-option>\n          </mat-autocomplete>\n        </mat-form-field>\n      </div>\n\n      <!-- Recherche par statut -->\n<div class=\"col-md-6\">\n  <label class=\"form-label\">Statut</label>\n  <select class=\"form-select\" [(ngModel)]=\"selectedStatut\" (change)=\"loadEquipements(0)\">\n    <option value=\"\">Tous les statuts</option>\n    <option value=\"DISPONIBLE\">Disponible</option>\n    <option value=\"AFFECTE\">Affecté</option>\n    <option value=\"MAINTENANCE\">En maintenance</option>\n    <option value=\"HORS_SERVICE\">Hors service</option>\n  </select>\n</div>\n</div>\n  </div>\n</div>\n\n<div class=\"search-wrapper\">\n  <div class=\"custom-search\">\n    <input\n      type=\"text\"\n      placeholder=\"Rechercher un equipement...\"\n      [(ngModel)]=\"searchTerm\"\n      (input)=\"onSearchChange()\"\n      class=\"form-control\"\n    />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n\n</div>\n<!-- Modal -->\n\n<!-- Modal de modification -->\n<div class=\"modal\" [ngClass]=\"{'show': isEditModalOpen}\" (click)=\"closeOnOutsideClickEdit($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeEditModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\">Modifier l'équipement</h3>\n    <form [formGroup]=\"editForm\" (ngSubmit)=\"onEditSubmit()\" novalidate>\n      <br><br>\n\n      <!-- 🧩 Model -->\n      <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n        <mat-label>Modèle</mat-label>\n        <input type=\"text\"\n               matInput\n               formControlName=\"model\"\n               [matAutocomplete]=\"autoModelEdit\"\n               placeholder=\"Rechercher un modèle...\">\n        <mat-autocomplete #autoModelEdit=\"matAutocomplete\"\n                          [displayWith]=\"displayModel\"\n                          (optionSelected)=\"onModelSelectedForEdit($event.option.value)\">\n          <mat-option *ngFor=\"let model of modelet\" [value]=\"model\">\n            {{ model.nomModel }}\n          </mat-option>\n        </mat-autocomplete>\n      </mat-form-field>\n\n      <div *ngIf=\"editForm.get('model')?.invalid && (editForm.get('model')?.touched || submitted)\" style=\"color:red\">\n        Le modèle est requis\n      </div>\n\n      <!-- 🧩 Numéro de série -->\n      <label for=\"numSerieEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Numéro de série</label>\n      <input\n        class=\"form-inputp\"\n        id=\"numSerieEdit\"\n        type=\"text\"\n        formControlName=\"numSerie\"\n        placeholder=\"Entrer le numéro de série\"\n      />\n      <div *ngIf=\"editForm.get('numSerie')?.invalid && (editForm.get('numSerie')?.touched || submitted)\" style=\"color:red\">\n        Le numéro de série est requis (min 4 caractères)\n      </div>\n\n      <!-- 🧩 Description -->\n      <label for=\"descriptionEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Description (optionnel)</label>\n      <input\n        class=\"form-inputp\"\n        id=\"descriptionEdit\"\n        type=\"text\"\n        formControlName=\"description\"\n        placeholder=\"Entrer la description (optionnel)\"\n      />\n\n      <!-- 🧩 Date -->\n      <label for=\"dateAffectationEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'acquisition</label>\n      <input\n        type=\"date\"\n        id=\"dateAffectationEdit\"\n        class=\"form-control\"\n        formControlName=\"dateAffectation\"\n      />\n      <div *ngIf=\"editForm.get('dateAffectation')?.invalid && (editForm.get('dateAffectation')?.touched || submitted)\" style=\"color:red\">\n        La date est requise\n      </div>\n\n      <label for=\"fournisseurEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Fournisseur</label>\n      <select\n        class=\"form-inputp\"\n        id=\"fournisseurEdit\"\n        name=\"fournisseurEdit\"\n        formControlName=\"fournisseurs\"\n        style=\"width: 100%;\"\n        required\n    >\n        <option [ngValue]=\"null\" disabled hidden>Sélectionner fournisseur</option>\n        <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\n          {{ fournisseur.nomFournisseur }}\n        </option>\n      </select>\n      <div *ngIf=\"editForm.get('fournisseurs')?.invalid && (editForm.get('fournisseurs')?.touched || submitted)\" style=\"color:red\">\n        Au moins un fournisseur est requis\n      </div>\n\n      <!-- 🧩 Image -->\n      <label for=\"imageEdit\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Logo d'équipement (optionnel)</label>\n      <input\n        type=\"file\"\n        id=\"imageEdit\"\n        (change)=\"onFileSelected($event)\"\n        accept=\"image/*\"\n        class=\"form-inputp\"\n      />\n      <div *ngIf=\"imagePreview\" style=\"margin-top: 10px;\">\n        <img [src]=\"imagePreview\" alt=\"Aperçu\" style=\"width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;\" />\n      </div>\n\n      <br />\n      <button type=\"submit\" class=\"btn btn-primary\">\n        Modifier\n      </button>\n    </form>\n  </div>\n</div>\n\n\n\n\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\" >Ajouter un nouvel Equipement</h3>\n<form [formGroup]=\"form\" (ngSubmit)=\"onRegister()\" novalidate>\n  <br><br>\n\n  <!-- 🧩 Model -->\n<mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n  <mat-label>Modèle</mat-label>\n  <input type=\"text\"\n         matInput\n         formControlName=\"model\"\n         [matAutocomplete]=\"autoModel\"\n         placeholder=\"Rechercher un modèle...\">\n  <mat-autocomplete #autoModel=\"matAutocomplete\"\n                    [displayWith]=\"displayModel\"\n                    (optionSelected)=\"onModelSelectedForAdd($event.option.value)\">\n    <mat-option *ngFor=\"let model of modelet\" [value]=\"model\">\n      {{ model.nomModel }}\n    </mat-option>\n  </mat-autocomplete>\n</mat-form-field>\n\n  <div *ngIf=\"form.get('model')?.invalid && (form.get('model')?.touched || submitted)\" style=\"color:red\">\n    Le modèle est requis\n  </div>\n\n  <!-- 🧩 Numéro de série -->\n  <label for=\"numSerie\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Numéro de série</label>\n  <input\n    class=\"form-inputp\"\n    id=\"numSerie\"\n    type=\"text\"\n    formControlName=\"numSerie\"\n    placeholder=\"Entrer le numéro de série\"\n  />\n  <div *ngIf=\"form.get('numSerie')?.invalid && (form.get('numSerie')?.touched || submitted)\" style=\"color:red\">\n    Le numéro de série est requis (min 4 caractères)\n  </div>\n\n  <!-- 🧩 Description -->\n  <label for=\"description\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Description (optionnel)</label>\n  <input\n    class=\"form-inputp\"\n    id=\"description\"\n    type=\"text\"\n    formControlName=\"description\"\n    placeholder=\"Entrer la description (optionnel)\"\n  />\n\n  <!-- 🧩 Date -->\n  <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'acquisition</label>\n  <input\n    type=\"date\"\n    id=\"dateAffectation\"\n    class=\"form-control\"\n    formControlName=\"dateAffectation\"\n  />\n  <div *ngIf=\"form.get('dateAffectation')?.invalid && (form.get('dateAffectation')?.touched || submitted)\" style=\"color:red\">\n    La date est requise\n  </div>\n   <label for=\"fournisseur\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Fournisseur</label>\n<select\n  class=\"form-inputp\"\n  id=\"type\"\n  name=\"fournisseur\"\n  formControlName=\"fournisseurs\"\n  style=\"width: 100%;\"\n  required\n\n>\n <option [ngValue]=\"null\" disabled hidden>Sélectionner fournisseur</option>\n  <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\n    {{ fournisseur.nomFournisseur }}\n  </option>\n</select>\n<div *ngIf=\"form.get('fournisseurs')?.invalid && (form.get('fournisseurs')?.touched || submitted)\" style=\"color:red\">\n  Au moins un fournisseur est requis\n</div>\n\n\n  <!-- 🧩 Image -->\n  <label for=\"image\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Logo d'équipement (optionnel)</label>\n  <input \n    type=\"file\" \n    id=\"image\" \n    (change)=\"onFileSelected($event)\" \n    accept=\"image/*\"\n    class=\"form-inputp\"\n  />\n  <div *ngIf=\"imagePreview\" style=\"margin-top: 10px;\">\n    <img [src]=\"imagePreview\" alt=\"Aperçu\" style=\"width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;\" />\n  </div>\n\n  <br />\n  <button type=\"submit\" class=\"btn btn-primary\">\n    Enregistrer\n  </button>\n</form>\n\n\n  </div>\n</div>\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<!-- Modal d'affectation -->\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationModalOpen}\" (click)=\"closeOnOutsideClickAffectation($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeAffectationModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\n\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\n    </div>\n\n    <form [formGroup]=\"affectationForm\" (ngSubmit)=\"onAffectationSubmit()\">\n\n<mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n  <mat-label>Utilisateur</mat-label>\n  <input\n    type=\"text\"\n    matInput\n    [formControl]=\"utilisateurCtrl\"\n    [matAutocomplete]=\"auto\"\n    placeholder=\"Rechercher un utilisateur...\">\n    \n  <mat-autocomplete\n    #auto=\"matAutocomplete\"\n    [displayWith]=\"displayUtilisateur\"\n    (optionSelected)=\"onUserSelected($event.option.value)\">\n    <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\n      {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n    </mat-option>\n  </mat-autocomplete>\n</mat-form-field>\n\n\n<div *ngIf=\"!affectationForm.get('user')?.value && affectationFormSubmitted\"\n     style=\"color:red; font-size: 12px;\">\n  L'utilisateur est requis\n</div>\n\n\n      <!-- Commentaire -->\n      <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\n      <textarea\n        formControlName=\"commentaire\"\n        class=\"form-inputp\"\n        rows=\"3\"\n        placeholder=\"Commentaire sur l'affectation (optionnel)...\"\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\n      </textarea>\n\n      <!-- Date d'affectation -->\n      <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\n      <input\n        type=\"date\"\n        formControlName=\"dateAffectation\"\n        class=\"form-inputp\"\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\n\n      <br />\n      <button type=\"submit\" class=\"btn-submit\">\n         Affecter l'équipement\n      </button>\n    </form>\n  </div>\n</div>\n\n\n\n\n\n\n<!-- Modal d'affectation -->\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationEditModalOpen}\" (click)=\"closeOnOutsideClickAffectationEdit($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeAffectationEditModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\n\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\n    </div>\n\n<form (ngSubmit)=\"selectedEquipement && updateReaffication(selectedEquipement)\" #editAffectationForm=\"ngForm\">\n\n  <!-- Utilisateur -->\n  <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n    <mat-label>Utilisateur Actuel</mat-label>\n <input\n  type=\"text\"\n  matInput\n  [formControl]=\"utilisateurCtrl\"\n  [matAutocomplete]=\"auto\"\n  placeholder=\"Rechercher un utilisateur...\"\n  required>\n\n    <mat-autocomplete \n      #auto=\"matAutocomplete\" \n      [displayWith]=\"displayUtilisateur\"\n      (optionSelected)=\"onUserSelected($event.option.value)\">\n      <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\n        {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n      </mat-option>\n    </mat-autocomplete>\n  </mat-form-field>\n\n  <div *ngIf=\"!EditedAffectation.user && editAffectationFormSubmitted\" style=\"color:red; font-size: 12px;\">\n    L'utilisateur est requis\n  </div>\n\n  <!-- Commentaire -->\n  <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\n  <textarea\n    name=\"commentaire\"\n    [(ngModel)]=\"EditedAffectation.commentaire\"\n    class=\"form-inputp\"\n    rows=\"3\"\n    placeholder=\"Commentaire sur l'affectation (optionnel)...\"\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\n  </textarea>\n\n  <!-- Date d'affectation -->\n  <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\n  <input\n    type=\"date\"\n    name=\"dateAffectation\"\n    [(ngModel)]=\"EditedAffectation.dateAffectation\"\n    class=\"form-inputp\"\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\n\n  <!-- Submit -->\n  <br />\n  <button type=\"submit\" class=\"btn-submit\">\n  Modifier Affectation\n  </button>\n</form>\n\n  </div>\n</div>\n\n\n\n\n\n\n\n<style>\n    .card-custom {\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    }\n\n    .btn-outline-lightblue {\n      border: 1px solid #cfe2ff;\n      color: #0d6efd;\n      background-color: #e7f1ff;\n    }\n\n    .tag {\n      background-color: #e7f1ff;\n      color: #0d6efd;\n      padding: 3px 10px;\n      font-size: 0.8rem;\n      border-radius: 15px;\n      position: absolute;\n      right: 20px;\n      top: 20px;\n    }\n\n.icon-box {\n  font-size: 48px; /* optional - for icon size */\n  width: 100px;     /* increase width */\n  height: 100px;    /* set height */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #0d6efd;\n  margin-right: 10px;\n border-radius: 0% !important;\n}\n\n    .btn-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n.card-custom {\n  border-radius: 12px;\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\n  background-color: #fff;\n  color: #212529; /* Darker text */\n  font-size: 0.95rem; /* Slightly larger base font */\n}\n\n.card-custom strong {\n  font-weight: 600; /* Heavier for labels */\n  color: #1a1a1a;\n}\n\n.card-custom h5 {\n  font-weight: 600;\n  color: #000;\n}\n\n.card-custom small,\n.text-muted {\n  color: #495057 !important; /* Less faded gray */\n}\n\n.icon-box {\n  font-size: 32px;\n  color: #0d6efd;\n  margin-right: 10px;\n}\n\n.tag {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n  padding: 3px 10px;\n  font-size: 0.8rem;\n  border-radius: 15px;\n  position: absolute;\n  right: 20px;\n  top: 20px;\n}\n\n\n\n  </style>\n\n<body class=\"bg-light\">\n  <div class=\"container my-2\">\n\n    <!-- Simple Notification Bar -->\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\n      {{ notification.message }}\n    </div>\n\n    <!-- Tableau des équipements -->\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <div class=\"table-responsive mt-1\">\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Description</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Fournisseur</th>\n                      <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr *ngFor=\"let equip of equiements\">\n                      <!-- Image -->\n                      <td class=\"px-1\">\n                        <img [src]=\"equip.image\"\n                             alt=\"Équipement\"\n                             class=\"rounded-circle img-fluid\"\n                             width=\"40\" height=\"40\" />\n                      </td>\n\n                      <!-- Modèle -->\n                      <td class=\"px-1\">\n                        <div class=\"ms-3\">\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\n                          <span class=\"fw-normal text-muted\">Modèle</span>\n                        </div>\n                      </td>\n\n                      <!-- Numéro de série -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\n                      </td>\n\n                      <!-- Date d'acquisition -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\n                      </td>\n\n                      <!-- Statut -->\n                      <td class=\"px-1\">\n                        <span class=\"badge rounded-pill\"\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\n                              [style.color]=\"'white'\">\n                          {{ equip.statut }}\n                        </span>\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\n                             class=\"text-muted small mt-1\">\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\n                        </div>\n                      </td>\n\n                      <!-- Description -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal text-truncate\" style=\"max-width: 150px; display: inline-block;\"\n                              [title]=\"equip.description\">\n                          {{ equip.description }}\n                        </span>\n                      </td>\n\n                      <!-- Fournisseur -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>\n                      </td>\n\n                      <!-- Actions -->\n                      <td class=\"px-1 text-end\">\n                        <div class=\"d-flex justify-content-end gap-1\">\n                          <!-- Bouton d'affectation conditionnel -->\n                          <button\n                            *ngIf=\"equip.statut === 'DISPONIBLE'\"\n                            class=\"btn btn-sm btn-outline-primary\"\n                            (click)=\"openAffectationModal(equip)\"\n                            title=\"Affecter\">\n                            Affecter\n                          </button>\n                          <button\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\n                            class=\"btn btn-sm btn-outline-warning\"\n                            (click)=\"openEditedModal(equip)\"\n                            title=\"Réaffecter\">\n                            🔄\n                          </button>\n                          <button\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\n                            class=\"btn btn-sm btn-outline-secondary\"\n                            (click)=\"desaffecterEquipement(equip)\"\n                            title=\"Désaffecter\">\n                            Désaffecter\n                          </button>\n                          <button\n                            class=\"btn btn-sm btn-outline-primary\"\n                            (click)=\"openModal1(equip)\"\n                            title=\"Modifier\">\n                            ✏️\n                          </button>\n                          <button\n                            class=\"btn btn-sm btn-outline-danger\"\n                            (click)=\"confirmDelete(equip.idEqui)\"\n                            title=\"Supprimer\">\n                            🗑️\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n    <!-- Pagination Bootstrap -->\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\n  <ul class=\"pagination\">\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\n    </li>\n\n    <li class=\"page-item\"\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\n        [class.active]=\"i === currentPage\">\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\n    </li>\n\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\n    </li>\n  </ul>\n</nav>\n\n  </div>\n</body>\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            \n          <div class=\"py-6 px-6 text-center\">\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\n  <script src=\"./assets/js/app.min.js\"></script>\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\n  <script src=\"./assets/js/dashboard.js\"></script>\n  <!-- solar icons -->\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\n</body>\n\n</html>"], "mappings": "AAEA,SAASA,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAE7E,SAASC,UAAU,QAAQ,+BAA+B;;;;;;;;;;;;;;ICuD9CC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,QAAA,CAAAG,SAAA,OAAAH,QAAA,CAAAI,QAAA,SAAAJ,QAAA,CAAAK,KAAA,MACF;;;;;IAuDFV,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAO,SAAA,CAAe;IACvDX,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAD,SAAA,CAAAE,QAAA,MACF;;;;;IAIJb,EAAA,CAAAC,cAAA,cAA+G;IAC7GD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAE,MAAA,wEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAC,cAAA,cAAmI;IACjID,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAI,UAAA,YAAAU,eAAA,CAAuB;IACtEd,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAE,eAAA,CAAAC,cAAA,MACF;;;;;IAEFf,EAAA,CAAAC,cAAA,cAA6H;IAC3HD,EAAA,CAAAE,MAAA,2CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAgB,SAAA,eAAyH;IAC3HhB,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,QAAAa,MAAA,CAAAC,YAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAoB;;;;;IAgC7BnB,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF6BH,EAAA,CAAAI,UAAA,UAAAgB,SAAA,CAAe;IACvDpB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAQ,SAAA,CAAAP,QAAA,MACF;;;;;IAIFb,EAAA,CAAAC,cAAA,cAAuG;IACrGD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAA6G;IAC3GD,EAAA,CAAAE,MAAA,wEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBNH,EAAA,CAAAC,cAAA,cAA2H;IACzHD,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYNH,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAI,UAAA,YAAAiB,eAAA,CAAuB;IACtErB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAS,eAAA,CAAAN,cAAA,MACF;;;;;IAEFf,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYJH,EAAA,CAAAC,cAAA,eAAoD;IAClDD,EAAA,CAAAgB,SAAA,eAAyH;IAC3HhB,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAI,UAAA,QAAAkB,OAAA,CAAAJ,YAAA,EAAAlB,EAAA,CAAAmB,aAAA,CAAoB;;;;;IAsCzBnB,EAAA,CAAAC,cAAA,eAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,aAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAuB,iBAAA,CAAAC,OAAA,CAAAC,kBAAA,CAAAC,KAAA,kBAAAF,OAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAb,QAAA,CAAwC;IAChBb,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAY,kBAAA,yBAAAY,OAAA,CAAAC,kBAAA,CAAAE,QAAA,KAA2C;;;;;IAkBzG3B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAwB,QAAA,CAAc;IAClE5B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAqB,QAAA,CAAApB,SAAA,OAAAoB,QAAA,CAAAnB,QAAA,SAAAmB,QAAA,CAAAlB,KAAA,MACF;;;;;IAKJV,EAAA,CAAAC,cAAA,eACyC;IACvCD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwCFH,EAAA,CAAAC,cAAA,eAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,aAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAuB,iBAAA,CAAAM,OAAA,CAAAJ,kBAAA,CAAAC,KAAA,kBAAAG,OAAA,CAAAJ,kBAAA,CAAAC,KAAA,CAAAb,QAAA,CAAwC;IAChBb,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAY,kBAAA,yBAAAiB,OAAA,CAAAJ,kBAAA,CAAAE,QAAA,KAA2C;;;;;IAoBvG3B,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAA0B,QAAA,CAAc;IAClE9B,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAuB,QAAA,CAAAtB,SAAA,OAAAsB,QAAA,CAAArB,QAAA,SAAAqB,QAAA,CAAApB,KAAA,MACF;;;;;IAIJV,EAAA,CAAAC,cAAA,eAAyG;IACvGD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgIJH,EAAA,CAAAC,cAAA,eAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAA2B,OAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFjC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAAmB,OAAA,CAAAC,YAAA,CAAAE,OAAA,MACF;;;;;IAyDoBlC,EAAA,CAAAC,cAAA,eACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,0BAAAZ,EAAA,CAAAmC,WAAA,OAAAC,OAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,MAAA,QACF;;;;;;IAoBEvC,EAAA,CAAAC,cAAA,kBAImB;IADjBD,EAAA,CAAAwC,UAAA,mBAAAC,uEAAA;MAAAzC,EAAA,CAAA0C,aAAA,CAAAC,IAAA;MAAA,MAAAL,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAA9C,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAD,OAAA,CAAAE,oBAAA,CAAAV,SAAA,CAA2B;IAAA,EAAC;IAErCtC,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBAIqB;IADnBD,EAAA,CAAAwC,UAAA,mBAAAS,uEAAA;MAAAjD,EAAA,CAAA0C,aAAA,CAAAQ,IAAA;MAAA,MAAAZ,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAM,OAAA,GAAAnD,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAI,OAAA,CAAAC,eAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IAEhCtC,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,kBAIsB;IADpBD,EAAA,CAAAwC,UAAA,mBAAAa,uEAAA;MAAArD,EAAA,CAAA0C,aAAA,CAAAY,IAAA;MAAA,MAAAhB,SAAA,GAAAtC,EAAA,CAAA4C,aAAA,GAAAC,SAAA;MAAA,MAAAU,OAAA,GAAAvD,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAQ,OAAA,CAAAC,qBAAA,CAAAlB,SAAA,CAA4B;IAAA,EAAC;IAEtCtC,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7EfH,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAAgB,SAAA,eAG8B;IAChChB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,eAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,eAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAyD,UAAA,KAAAC,2CAAA,mBAGM;IACR1D,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,eAAiB;IAGbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,eAAiB;IACSD,EAAA,CAAAE,MAAA,IAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/FH,EAAA,CAAAC,cAAA,eAA0B;IAGtBD,EAAA,CAAAyD,UAAA,KAAAE,8CAAA,sBAMS;IACT3D,EAAA,CAAAyD,UAAA,KAAAG,8CAAA,sBAMS;IACT5D,EAAA,CAAAyD,UAAA,KAAAI,8CAAA,sBAMS;IACT7D,EAAA,CAAAC,cAAA,mBAGmB;IADjBD,EAAA,CAAAwC,UAAA,mBAAAsB,8DAAA;MAAA,MAAAC,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAsB,IAAA;MAAA,MAAA1B,SAAA,GAAAyB,WAAA,CAAAlB,SAAA;MAAA,MAAAoB,OAAA,GAAAjE,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAkB,OAAA,CAAAC,UAAA,CAAA5B,SAAA,CAAiB;IAAA,EAAC;IAE3BtC,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAGoB;IADlBD,EAAA,CAAAwC,UAAA,mBAAA2B,8DAAA;MAAA,MAAAJ,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAsB,IAAA;MAAA,MAAA1B,SAAA,GAAAyB,WAAA,CAAAlB,SAAA;MAAA,MAAAuB,OAAA,GAAApE,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAqB,OAAA,CAAAC,aAAA,CAAA/B,SAAA,CAAAC,MAAA,CAA2B;IAAA,EAAC;IAErCvC,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAtFNH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAkC,SAAA,CAAAgC,KAAA,EAAAtE,EAAA,CAAAmB,aAAA,CAAmB;IASYnB,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAuB,iBAAA,CAAAe,SAAA,CAAAZ,KAAA,kBAAAY,SAAA,CAAAZ,KAAA,CAAAb,QAAA,CAA2B;IAOvCb,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAuB,iBAAA,CAAAe,SAAA,CAAAX,QAAA,CAAoB;IAKpB3B,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAuE,WAAA,SAAAjC,SAAA,CAAAkC,eAAA,gBAAgD;IAMlExE,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAAyE,WAAA,qBAAAnC,SAAA,CAAAoC,MAAA,gCAAApC,SAAA,CAAAoC,MAAA,uCAA2H;IAE/H1E,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAA0B,SAAA,CAAAoC,MAAA,MACF;IACM1E,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,CAAAC,WAAA,oBAAArC,SAAA,CAAAoC,MAAA,CAAAC,WAAA,sBAA0F;IAS1F3E,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAkC,SAAA,CAAAsC,WAAA,CAA2B;IAC/B5E,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAY,kBAAA,MAAA0B,SAAA,CAAAsC,WAAA,MACF;IAKwB5E,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAuB,iBAAA,EAAAe,SAAA,CAAAuC,WAAA,kBAAAvC,SAAA,CAAAuC,WAAA,CAAA9D,cAAA,yBAA8D;IAQjFf,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,kBAAmC;IAOnC1E,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,eAAgC;IAOhC1E,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAkC,SAAA,CAAAoC,MAAA,eAAgC;;;;;;IAsCzD1E,EAAA,CAAAC,cAAA,cAEuC;IAChBD,EAAA,CAAAwC,UAAA,mBAAAsC,8DAAA;MAAA,MAAAf,WAAA,GAAA/D,EAAA,CAAA0C,aAAA,CAAAqC,IAAA;MAAA,MAAAC,KAAA,GAAAjB,WAAA,CAAAkB,KAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAAmC,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAAChF,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAAoF,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACctF,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAuB,iBAAA,CAAAyD,KAAA,KAAW;;;;;;;;;IATnEhF,EAAA,CAAAC,cAAA,eAAuE;IAG5CD,EAAA,CAAAwC,UAAA,mBAAA+C,yDAAA;MAAAvF,EAAA,CAAA0C,aAAA,CAAA8C,IAAA;MAAA,MAAAC,OAAA,GAAAzF,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAA0C,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACtF,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAyD,UAAA,IAAAiC,0CAAA,kBAIK;IAEL1F,EAAA,CAAAC,cAAA,cAAwE;IACjDD,EAAA,CAAAwC,UAAA,mBAAAmD,yDAAA;MAAA3F,EAAA,CAAA0C,aAAA,CAAA8C,IAAA;MAAA,MAAAI,OAAA,GAAA5F,EAAA,CAAA4C,aAAA;MAAA,OAAS5C,EAAA,CAAA+C,WAAA,CAAA6C,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACtF,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAoF,WAAA,aAAAS,OAAA,CAAAP,WAAA,OAAoC;IAKrCtF,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA8F,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,OAAA,CAAAI,UAAA,EAA+B;IAK9BjG,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAoF,WAAA,aAAAS,OAAA,CAAAP,WAAA,KAAAO,OAAA,CAAAI,UAAA,KAAiD;;;;;;;;ADjrB3E,OAAM,MAAOC,oBAAoB;EA2EjCF,YACUG,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA5E5B,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,sBAAsB,GAAG,KAAK;IAC9B,KAAAC,0BAA0B,GAAG,KAAK;IAClC,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAAlF,YAAY,GAAG;MACbmF,IAAI,EAAE,KAAK;MACXlF,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAoD,WAAW,GAAG,CAAC;IACf,KAAA8B,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBjF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACX+C,MAAM,EAAC,EAAE;MACTJ,KAAK,EAAC,EAAE;MACR5C,KAAK,EAAC,IAAI;MACV8C,eAAe,EAAC,IAAIiD,IAAI,CAAJ,CAAI;MACxB7C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IACD,KAAA6C,cAAc,GAAO;MACrBnF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACX+C,MAAM,EAAC,EAAE;MACTJ,KAAK,EAAC,EAAE;MACR5C,KAAK,EAAC,IAAI;MACV8C,eAAe,EAAC,IAAIiD,IAAI,CAAJ,CAAI;MACxB7C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IAID,KAAA8C,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdrD,eAAe,EAAC,IAAIiD,IAAI,EAAE;MAC1BK,IAAI,EAAC,IAAIrI,WAAW,EAAE;MACtBsI,UAAU,EAAC,IAAIzI,KAAK,EAAE;MACtB0I,MAAM,EAAC;KAER;IACD,KAAA3F,eAAe,GAAU,EAAE;IAC3B,KAAA4F,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAI9I,WAAW,EAAE;IACnC,KAAA+I,qBAAqB,GAAG,IAAI/I,WAAW,EAAE;IACzC,KAAAgJ,SAAS,GAAG,IAAIhJ,WAAW,EAAE;IAsV/B,KAAAiJ,cAAc,GAAW,EAAE,CAAC,CAAC;IA+U3B,KAAAtH,YAAY,GAAgC,IAAI;IAClD,KAAAuH,aAAa,GAAgB,IAAI;IAgDjC,KAAAC,YAAY,GAAQ,EAAE;EAhtBlB;EACFC,QAAQA,CAAA;IACJ,IAAI,CAACrD,WAAW,GAAG,CAAC;IAEtB,IAAI,CAACsD,YAAY,EAAE;IACnB,IAAI,CAACzD,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAACuD,cAAc,EAAE;IAGzB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACzC,EAAE,CAAC0C,KAAK,CAAC;MACxBrH,KAAK,EAAE,CAAC,IAAI,EAAElC,UAAU,CAACwJ,QAAQ,CAAC;MAClCrH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACwJ,QAAQ,EAAExJ,UAAU,CAACyJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DrE,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBJ,eAAe,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACwJ,QAAQ,CAAC;MAC1CtE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBJ,KAAK,EAAE,CAAC,IAAI,CAAC;MACb8D,YAAY,EAAE,CAAC,IAAI,EAAE5I,UAAU,CAACwJ,QAAQ;KACzC,CAAC;IAEF;IACA,IAAI,CAACE,QAAQ,GAAG,IAAI,CAAC7C,EAAE,CAAC0C,KAAK,CAAC;MAC5BrH,KAAK,EAAE,CAAC,IAAI,EAAElC,UAAU,CAACwJ,QAAQ,CAAC;MAClCrH,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnC,UAAU,CAACwJ,QAAQ,EAAExJ,UAAU,CAACyJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DrE,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBJ,eAAe,EAAE,CAAC,EAAE,EAAEhF,UAAU,CAACwJ,QAAQ,CAAC;MAC1CtE,MAAM,EAAE,CAAC,YAAY,CAAC;MACtBJ,KAAK,EAAE,CAAC,IAAI,CAAC;MACb8D,YAAY,EAAE,CAAC,IAAI,EAAE5I,UAAU,CAACwJ,QAAQ;KACzC,CAAC;IAKF,IAAI,CAACG,eAAe,GAAG,IAAI,CAAC9C,EAAE,CAAC0C,KAAK,CAAC;MACnCjB,IAAI,EAAE,CAAC,IAAI,EAAEtI,UAAU,CAACwJ,QAAQ,CAAC;MACjCjB,UAAU,EAAE,CAAC,IAAI,CAAC;MAClBF,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBrD,eAAe,EAAE,CAAC,IAAIiD,IAAI,EAAE,CAAC;MAC7BO,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;IAIF;IACA,IAAI,CAACO,SAAS,CAACa,YAAY,CACxBC,IAAI,CACH3J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyJ,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACrD,WAAW,CAACsD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO3J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8J,SAAS,CAAC/C,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACmC,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACjCC,IAAI,CACH3J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyJ,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACrD,WAAW,CAACsD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO3J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8J,SAAS,CAAC/C,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACuC,QAAQ,CAACS,GAAG,CAAC,OAAO,CAAC,EAAEP,YAAY,CACrCC,IAAI,CACH3J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyJ,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACrD,WAAW,CAACsD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO3J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8J,SAAS,CAAC/C,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAGJ,IAAI,CAAC0B,eAAe,CAACe,YAAY,CAC9BC,IAAI,CACH3J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyJ,KAAK,IAAG;MAEhB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACrD,WAAW,CAACyD,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UAEL,OAAO3J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8J,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAC/C,oBAAoB,GAAG+C,KAAK;IACnC,CAAC,CAAC;IAEJ;IACA,IAAI,CAACvB,qBAAqB,CAACc,YAAY,CACpCC,IAAI,CACH3J,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEwJ,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACjB,qBAAqB,CAACwB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAAC5E,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFtF,SAAS,CAACyJ,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACrD,WAAW,CAAC6D,iBAAiB,CAAC,IAAI,CAAC3C,UAAU,EAAC,IAAI,CAACiB,qBAAqB,CAACgB,KAAK,EAAC,CAAC,EAAC,IAAI,CAAClC,QAAQ,CAAC,CAACsC,SAAS,CAAC;YAC7GO,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;cAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;cAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;YACzC,CAAC;YACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAACnF,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACgB,WAAW,CAACyD,WAAW,CAACN,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAO3J,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8J,SAAS,CAACG,KAAK,IAAG;MACjB,IAAI,CAAC9C,0BAA0B,GAAG8C,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAW,kBAAkBA,CAAC1C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAACtH,SAAS,IAAIsH,IAAI,CAACrH,QAAQ,MAAMqH,IAAI,CAACpH,KAAK,EAAE,GAAG,EAAE;EACzE;EAGA+J,YAAYA,CAAC/I,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACb,QAAQ,EAAE,GAAG,EAAE;EACzC;EAEA6J,eAAeA,CAAChJ,KAAU;IACxB,IAAI,CAACgG,cAAc,CAAChG,KAAK,GAAGA,KAAK;EACnC;EAEAiJ,qBAAqBA,CAACjJ,KAAU;IAC9B,IAAI,CAACoH,IAAI,CAAC8B,UAAU,CAAC;MAAElJ,KAAK,EAAEA;IAAK,CAAE,CAAC;EACxC;EAEAmJ,sBAAsBA,CAACnJ,KAAU;IAC/B,IAAI,CAACwH,QAAQ,CAAC0B,UAAU,CAAC;MAAElJ,KAAK,EAAEA;IAAK,CAAE,CAAC;EAC5C;EAICmH,cAAcA,CAAA;IAGb,IAAI,CAAC1C,WAAW,CAAC2E,iBAAiB,EAAE,CAACpB,SAAS,CAACqB,IAAI,IAAG;MACtD,IAAI,CAAC3C,YAAY,GAAG2C,IAAI;IAE1B,CAAC,CAAC;EAGA;EAEAC,kBAAkBA,CAAC1B,KAAa;IAEhC,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MACvC,IAAI,CAAC5B,cAAc,CAAChG,KAAK,GAAG,IAAI;;EAEpC;EASEuJ,cAAcA,CAACnD,IAAiB;IAE9B,IAAI,IAAI,CAACrB,sBAAsB,EAAE;MAE/B,IAAI,CAAC0C,eAAe,CAACyB,UAAU,CAAC;QAC9B9C,IAAI,EAAEA;OACP,CAAC;KACH,MAAM,IAAI,IAAI,CAACpB,0BAA0B,EAAE;MAE1C,IAAI,CAACiB,iBAAiB,CAACG,IAAI,GAAGA,IAAI;;IAEpCyC,OAAO,CAACW,GAAG,CAAC,0BAA0B,EAAEpD,IAAI,CAAC;EAC/C;EAEAqD,oBAAoBA,CAACrD,IAAiB;IACpC,IAAI,CAAC3C,eAAe,CAAC,CAAC,CAAC;EAEzB;EAEAiG,mBAAmBA,CAACC,KAAiB;IACnC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACC,UAAU,EAAE;;EAErB;EAEFvH,UAAUA,CAAC6D,UAAiB;IAC1B,MAAM2D,YAAY,GAAG,IAAI,CAAC/E,MAAM,CAACgF,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAK9D,UAAU,CAACrG,KAAK,EAAEmK,OAAO,CAAC;IAEnF;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAC1D,YAAY,CAACuD,IAAI,CAACI,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKjE,UAAU,CAAClD,WAAW,EAAEmH,aAAa,CAAC;IAEjH,IAAI,CAACtE,cAAc,GAAG;MACpB,GAAGK,UAAU;MACbrG,KAAK,EAAEgK,YAAY,IAAI;KACxB;IAED;IACA,IAAI,CAACxC,QAAQ,CAAC0B,UAAU,CAAC;MACvBlJ,KAAK,EAAE,IAAI,CAACgG,cAAc,CAAChG,KAAK;MAChCC,QAAQ,EAAE,IAAI,CAAC+F,cAAc,CAAC/F,QAAQ;MACtCiD,WAAW,EAAE,IAAI,CAAC8C,cAAc,CAAC9C,WAAW;MAC5CJ,eAAe,EAAE,IAAI,CAACyH,kBAAkB,CAAC,IAAI,CAACvE,cAAc,CAAClD,eAAe,CAAC;MAC7EE,MAAM,EAAE,IAAI,CAACgD,cAAc,CAAChD,MAAM;MAClCJ,KAAK,EAAE,IAAI;MACX8D,YAAY,EAAE0D,kBAAkB,IAAI;KACrC,CAAC;IAEFvB,OAAO,CAACW,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACxD,cAAc,CAAC;IACvD6C,OAAO,CAACW,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACxD,cAAc,CAAC7C,WAAW,CAAC;IACrE0F,OAAO,CAACW,GAAG,CAAC,mCAAmC,EAAEY,kBAAkB,CAAC;IACpEvB,OAAO,CAACW,GAAG,CAAC,OAAO,EAAE,IAAI,CAACxD,cAAc,CAAClD,eAAe,CAAC;IAEzD,IAAI,CAAC+D,SAAS,CAACuB,QAAQ,CAAC,IAAI,CAACpC,cAAc,CAAChG,KAAK,CAAC;IAElD;IACA,IAAI,CAAC8E,eAAe,GAAG,IAAI;EAC7B;EAEA0F,YAAYA,CAAA;IACV,IAAI,CAAC/D,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACe,QAAQ,CAACiD,OAAO,EAAE;MACzB,IAAI,CAACjD,QAAQ,CAACkD,gBAAgB,EAAE;MAChC;;IAGF,MAAMC,cAAc,GAAG;MACrB,GAAG,IAAI,CAACnD,QAAQ,CAACI,KAAK;MACtB/G,MAAM,EAAE,IAAI,CAACmF,cAAc,CAACnF,MAAM;MAClCmC,MAAM,EAAE,IAAI,CAACgD,cAAc,CAAChD,MAAM;MAClCG,WAAW,EAAE,IAAI,CAACqE,QAAQ,CAACI,KAAK,CAAClB,YAAY,IAAI,IAAI,CAAC;KACvD;;IAED,IAAI,CAACjC,WAAW,CAACmG,WAAW,CAACD,cAAc,CAAC,CAAC3C,SAAS,CAAC;MACrDO,IAAI,EAAGsC,QAAQ,IAAI;QACjBhC,OAAO,CAACW,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;QAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;QAClE,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACtH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAMoH,UAAU,GAAG,IAAI3M,UAAU,EAAE;QACnC2M,UAAU,CAACC,IAAI,GAAG,IAAIlF,IAAI,EAAE;QAC5BiF,UAAU,CAAC7E,WAAW,GAAG,iCAAiCwE,cAAc,CAAC3K,KAAK,EAAEb,QAAQ,eAAewL,cAAc,CAAC1K,QAAQ,GAAG;QACjI,IAAI,CAACwE,WAAW,CAACyG,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UACnDO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;UACjD,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;QACtC,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;MAC7E;KACD,CAAC;EACJ;EAEAK,UAAUA,CAAA;IACRtC,OAAO,CAACW,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACxD,cAAc,CAAC;IACnD,IAAI,CAACvB,WAAW,CAACmG,WAAW,CAAC,IAAI,CAAC5E,cAAc,CAAC,CAACgC,SAAS,CACxD6C,QAAQ,IAAI;MACXhC,OAAO,CAACW,GAAG,CAAC,oBAAoB,EAAEqB,QAAQ,CAAC;MAC3C,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,gCAAgC,CAAC;MAClE,IAAI,CAACf,UAAU,EAAE;MACjB,IAAI,CAACtG,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;MACxC;IAEF,CAAC,EACA+E,KAAK,IAAI;MACRE,OAAO,CAACF,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,2CAA2C,CAAC;IAC7E,CAAC,CACF;EACH;EAGArH,eAAeA,CAAC2H,IAAY;IAC1B,IAAI,CAACxH,WAAW,GAAGwH,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAAC1F,UAAU,CAACkC,IAAI,EAAE;IACtC,MAAM7E,MAAM,GAAG,IAAI,CAAC8D,cAAc,CAACe,IAAI,EAAE;IAEzC,IAAIyD,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAAC3E,qBAAqB,CAACgB,KAAK;IAEhD,IAAI,OAAO2D,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAAC1D,IAAI,EAAE;KAC1B,MAAM,IAAI0D,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACzD,IAAI,EAAE;;IAGpC;IACA,IAAIyD,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAC7G,WAAW,CAAC6D,iBAAiB,CAAC+C,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAAC1F,QAAQ,CAAC,CAACsC,SAAS,CAAC;QACzFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;QACzC,CAAC;QACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAIrI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAAC+G,kBAAkB,CAAC,EAAE,EAAExI,MAAM,EAAEoI,IAAI,EAAE,IAAI,CAAC1F,QAAQ,CAAC,CAACsC,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;QACzC,CAAC;QACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAIrI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAAC6D,iBAAiB,CAAC+C,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC1F,QAAQ,CAAC,CAACsC,SAAS,CAAC;QAC7EO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;QACzC,CAAC;QACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIyC,OAAO,KAAK,EAAE,IAAIrI,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACyB,WAAW,CAAC+G,kBAAkB,CAACH,OAAO,EAAErI,MAAM,EAAEoI,IAAI,EAAE,IAAI,CAAC1F,QAAQ,CAAC,CAACsC,SAAS,CAAC;QAClFO,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;QACzC,CAAC;QACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAACnE,WAAW,CAACgH,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAAC1F,QAAQ,CAAC,CAACsC,SAAS,CAAC;MAChEO,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACtD,UAAU,GAAGsD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;QAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACxD,UAAU,CAAC;MACzC,CAAC;MACDyD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACxD,UAAiB;IACzC2D,OAAO,CAACW,GAAG,CAACtE,UAAU,CAAC;IACvBA,UAAU,CAACwG,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAACpF,OAAO,CAACoF,EAAE,CAAC9K,MAAM,CAAC,GAAC8K,EAAE,CAAC9K,MAAM;IAChC,CAAC,CAAC;IACFgI,OAAO,CAACW,GAAG,CAAC,IAAI,CAACjD,OAAO,CAAC;IAC1B,IAAI,CAAC9B,WAAW,CAACmH,oBAAoB,CAAC,IAAI,CAACrF,OAAO,CAAC,CAACyB,SAAS,CAACqB,IAAI,IAAG;MAEnEA,IAAI,CAACqC,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAACrF,gBAAgB,CAACqF,WAAW,CAACxF,UAAU,CAACxF,MAAM,CAAC,GAAGgL,WAAW;QAClE,IAAI,CAAClL,eAAe,CAACkL,WAAW,CAACxF,UAAU,CAACxF,MAAM,CAAC,GAAGgL,WAAW,CAACzF,IAAI,CAACtH,SAAS,GAAG,GAAG,GAAG+M,WAAW,CAACzF,IAAI,CAACrH,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGE+M,cAAcA,CAAA;IAEZ,IAAI,CAACrI,eAAe,CAAC,CAAC,CAAC;EACzB;EAGCsI,WAAWA,CAAC7F,EAAU;IACrB,IAAI,CAACzB,WAAW,CAACsH,WAAW,CAAC7F,EAAE,CAAC,CAAC8B,SAAS,CAAC,MAAK;MAC9C,IAAI,CAAC8C,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;MACnE,IAAI,CAACrH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IAGxC,CAAC,CAAC;EACJ;EAKQoI,qBAAqBA,CAACC,QAAgB,EAAEC,YAAoB;IAClE,IAAI,CAACzH,WAAW,CAAC0H,kBAAkB,CAACD,YAAY,CAAC,CAAClE,SAAS,CAACqB,IAAI,IAAG;MACnE,IAAI,CAAC9D,OAAO,GAAG8D,IAAI,CAAChD,UAAU,CAACrG,KAAK,EAAEb,QAAQ,GAC9C,GAAGkK,IAAI,CAAChD,UAAU,CAACrG,KAAK,CAACb,QAAQ,cAAckK,IAAI,CAAChD,UAAU,CAACpG,QAAQ,GAAG,GAC1E,IAAI;MAEF,IAAI,CAACuF,OAAO,GAAG6D,IAAI,CAACjD,IAAI,CAACtH,SAAS,GAAG,GAAG,GAAGuK,IAAI,CAACjD,IAAI,CAACrH,QAAQ;MAE7D,MAAMiM,UAAU,GAAG,IAAI3M,UAAU,EAAE;MACnC2M,UAAU,CAACC,IAAI,GAAG5B,IAAI,CAACvG,eAAe;MACtC,MAAMsJ,aAAa,GAAG/C,IAAI,CAACvG,eAAe,GAAG,IAAIiD,IAAI,CAACsD,IAAI,CAACvG,eAAe,CAAC,CAACuJ,kBAAkB,CAAC,OAAO,CAAC,GAAG,IAAItG,IAAI,EAAE,CAACsG,kBAAkB,CAAC,OAAO,CAAC;MAChJrB,UAAU,CAAC7E,WAAW,GAAG,GAAG,IAAI,CAACZ,OAAO,IAAI0G,QAAQ,IAAI,IAAI,CAACzG,OAAO,OAAO4G,aAAa,EAAE;MAE1F,IAAI,CAAC3H,WAAW,CAACyG,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;QACnDO,IAAI,EAAGsC,QAAQ,IAAI;UACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;QACjD,CAAC;QACDlC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;QAC5E;OACD,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA7G,qBAAqBA,CAACwK,KAAY;IAChC,MAAMC,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,uDAAuD,CAAC;IAC3F,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAACP,qBAAqB,CAAC,sBAAsB,EAAEM,KAAK,CAACzL,MAAM,CAAC;MAEhE,IAAI,CAAC4D,WAAW,CAACiI,SAAS,CAAC,IAAI,CAAClG,gBAAgB,CAAC8F,KAAK,CAACzL,MAAM,CAAC,CAACqF,EAAE,CAAC,CAAC8B,SAAS,CAAC;QAC3EO,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC9D,WAAW,CAACkI,mBAAmB,CAACL,KAAK,CAACzL,MAAM,CAAC,CAACmH,SAAS,CAAC;YAC3DO,IAAI,EAAEA,CAAA,KAAK;cACT,IAAI,CAACuC,gBAAgB,CAAC,SAAS,EAAE,mCAAmC,CAAC;cACrE,IAAI,CAACrH,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;cACtC4I,MAAM,CAACI,QAAQ,CAAC;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAQ,CAAE,CAAC;YACjD,CAAC;YACDnE,KAAK,EAAGA,KAAK,IAAI;cACfE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;cAC5D,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,qCAAqC,CAAC;YACvE;WACD,CAAC;QACJ,CAAC;QACDnC,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,kDAAkD,EAAEA,KAAK,CAAC;UACxE,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,4BAA4B,CAAC;QAC9D;OACD,CAAC;;EAEN;EAGCnI,aAAaA,CAACoK,OAAe;IAC5BlE,OAAO,CAACW,GAAG,CAACuD,OAAO,CAAC;IACpB,MAAMR,WAAW,GAAGC,MAAM,CAACC,OAAO,CAAC,4CAA4C,CAAC;IAChF,IAAIF,WAAW,EAAE;MACf,IAAI,CAACR,WAAW,CAACgB,OAAO,CAAC;;EAE7B;EACFC,mBAAmBA,CAAA;IAEjB,IAAI,IAAI,CAACjI,sBAAsB,EAAE;MAE/B,IAAI,CAACkI,oBAAoB,EAAE;KAC5B,MAAM,IAAI,IAAI,CAACjI,0BAA0B,EAAE;MAE1C,IAAI,CAACkI,qBAAqB,EAAE;;EAEhC;EAEQD,oBAAoBA,CAAA;IAC1B,IAAI,CAACrH,wBAAwB,GAAG,IAAI;IAEpC,IAAI,CAAC,IAAI,CAAC6B,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK,EAAE;MAC5CiB,OAAO,CAACW,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAGF,IAAI,CAAC,IAAI,CAACzJ,kBAAkB,EAAE;MAC5B8I,OAAO,CAACF,KAAK,CAAC,8BAA8B,CAAC;MAC7C;;IAGF;IACA,IAAI,CAAC5I,kBAAkB,CAACiD,MAAM,GAAG,YAAY;IAC7C,IAAI,CAACyE,eAAe,CAACyB,UAAU,CAAC;MAAE7C,UAAU,EAAE,IAAI,CAACtG;IAAkB,CAAE,CAAC;IACxE,IAAI,CAAC0H,eAAe,CAACyB,UAAU,CAAC;MAAE5C,MAAM,EAAC;IAAU,CAAE,CAAC;IAEtDuC,OAAO,CAACW,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC/B,eAAe,CAACG,KAAK,CAAC;IACtD,IAAI,CAACnD,WAAW,CAAC0I,gBAAgB,CAAC,IAAI,CAACpN,kBAAkB,CAACc,MAAM,CAAC,CAACmH,SAAS,CAAC;MAC1EO,IAAI,EAAGsC,QAAQ,IAAI;QACjBhC,OAAO,CAACW,GAAG,CAAC,gCAAgC,EAAEqB,QAAQ,CAAC;MACzD,CAAC;MACDlC,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MAClE;KACD,CAAC;IACF,IAAI,CAAClE,WAAW,CAAC2I,MAAM,CAAC,IAAI,CAAC3F,eAAe,CAACG,KAAK,CAAC,CAACI,SAAS,CAAC;MAC5DO,IAAI,EAAGsC,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,iCAAiC,CAAC;QACnE,IAAI,CAACuC,qBAAqB,EAAE;QAC5B,IAAI,CAAC5J,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;QACA,MAAM0J,WAAW,GAAG,IAAI,CAAC7F,eAAe,CAACQ,GAAG,CAAC,MAAM,CAAC,EAAEL,KAAK;QAC3D,MAAM2F,aAAa,GAAG,IAAI,CAACxN,kBAAkB,EAAEC,KAAK,EAAEb,QAAQ,IAAI,oBAAoB;QACtF,MAAMc,QAAQ,GAAG,IAAI,CAACF,kBAAkB,EAAEE,QAAQ,IAAI,KAAK;QAC3D,MAAMuN,cAAc,GAAGF,WAAW,GAAG,GAAGA,WAAW,CAACxO,SAAS,IAAI,EAAE,IAAIwO,WAAW,CAACvO,QAAQ,IAAI,EAAE,EAAE,CAAC8I,IAAI,EAAE,GAAG,qBAAqB;QAClI,IAAI,CAACmE,qBAAqB,CAAC,gBAAgB,EAAE,IAAI,CAACjM,kBAAkB,CAACc,MAAM,CAAC;MAC9E,CAAC;MACD8H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,wCAAwC,CAAC;MAC1E;KACD,CAAC;EACJ;EAEQoC,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC,IAAI,CAACjH,iBAAiB,CAACG,IAAI,EAAE;MAChCyC,OAAO,CAACW,GAAG,CAAC,0CAA0C,CAAC;MACvD;;IAIF,MAAMiE,eAAe,GAAG;MACtB,GAAG,IAAI,CAACxH,iBAAiB;MACzBnD,eAAe,EAAE,IAAI,CAACmD,iBAAiB,CAACnD,eAAe,GAAG,IAAIiD,IAAI,CAAC,IAAI,CAACE,iBAAiB,CAACnD,eAAe,CAAC,GAAG,IAAIiD,IAAI;KACtH;IAED8C,OAAO,CAACW,GAAG,CAAC,uBAAuB,EAAEiE,eAAe,CAAC;IAErD,IAAI,CAAChJ,WAAW,CAACiJ,SAAS,CAACD,eAAe,CAAC,CAACzF,SAAS,CAAC;MACpDO,IAAI,EAAGsC,QAAa,IAAI;QACtB,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,oCAAoC,CAAC;QACtE,IAAI,CAAC6C,yBAAyB,EAAE;QAChC,IAAI,CAAClK,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QAExC;QACA,MAAMoH,UAAU,GAAG,IAAI3M,UAAU,EAAE;QACnC2M,UAAU,CAACC,IAAI,GAAG,IAAIlF,IAAI,EAAE;QAE5B,MAAMwH,aAAa,GAAG,IAAI,CAACxN,kBAAkB,CAACC,KAAK,EAAEb,QAAQ,GACzD,GAAG,IAAI,CAACY,kBAAkB,CAACC,KAAK,CAACb,QAAQ,eAAe,IAAI,CAACY,kBAAkB,CAACE,QAAQ,GAAG,GAC3F,oBAAoB;QAExB,MAAMuN,cAAc,GAAG,IAAI,CAACvH,iBAAiB,CAACG,IAAI,GAC9C,GAAG,IAAI,CAACH,iBAAiB,CAACG,IAAI,CAACtH,SAAS,IAAI,EAAE,IAAI,IAAI,CAACmH,iBAAiB,CAACG,IAAI,CAACrH,QAAQ,IAAI,EAAE,EAAE,CAAC8I,IAAI,EAAE,GACrG,qBAAqB;QAEzB,MAAMuE,aAAa,GAAG,IAAIrG,IAAI,EAAE,CAACsG,kBAAkB,CAAC,OAAO,CAAC;QAC5DrB,UAAU,CAAC7E,WAAW,GAAG,GAAGoH,aAAa,sBAAsBC,cAAc,OAAOpB,aAAa,EAAE;QAEnG,IAAI,CAAC3H,WAAW,CAACyG,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UACnDO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,yCAAyC,EAAEqB,QAAQ,CAAC;UAClE,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,qEAAqE,EAAEA,KAAK,CAAC;UAC7F;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAACmC,gBAAgB,CAAC,OAAO,EAAE,4CAA4C,CAAC;MAC9E;KACD,CAAC;EACJ;EAME8C,UAAUA,CAAA;IACV,IAAI,CAACnH,SAAS,GAAG,IAAI;IAEvBoC,OAAO,CAACW,GAAG,CAAC,IAAI,CAACpC,IAAI,CAACQ,KAAK,CAAC5H,KAAK,CAAC;IAClC,IAAI,IAAI,CAACoH,IAAI,CAACqD,OAAO,EAAE;MACnB,IAAI,CAACrD,IAAI,CAACsD,gBAAgB,EAAE,CAAC,CAAC;MAC9B;;IAEJ,MAAMM,UAAU,GAAG,IAAI3M,UAAU,EAAE;IAEjC,MAAMsM,cAAc,GAAG;MACrB,GAAG,IAAI,CAACvD,IAAI,CAACQ,KAAK;MAClB5E,MAAM,EAAE,YAAY;MACpBG,WAAW,EAAE,IAAI,CAACiE,IAAI,CAACQ,KAAK,CAAClB,YAAY,IAAI;KAC9C;IACHmC,OAAO,CAACW,GAAG,CAACmB,cAAc,CAAC;IACzB,IAAI,CAAClG,WAAW,CAACoJ,aAAa,CAAClD,cAAc,CAAC,CAAC3C,SAAS,CAAC;MACvDO,IAAI,EAAGsC,QAAQ,IAAI;QACjBG,UAAU,CAACC,IAAI,GAAG,IAAIlF,IAAI,EAAE;QAC5BiF,UAAU,CAAC7E,WAAW,GAAG,iCAAiCwE,cAAc,CAAC3K,KAAK,EAAEb,QAAQ,eAAewL,cAAc,CAAC1K,QAAQ,GAAG;QACvI,IAAI,CAACwE,WAAW,CAACyG,aAAa,CAACF,UAAU,CAAC,CAAChD,SAAS,CAAC;UAC7CO,IAAI,EAAGsC,QAAQ,IAAI;YACjBhC,OAAO,CAACW,GAAG,CAAC,wBAAwB,EAAEqB,QAAQ,CAAC;UACjD,CAAC;UACDlC,KAAK,EAAGA,KAAK,IAAI;YACfE,OAAO,CAACF,KAAK,CAAC,oDAAoD,EAAEA,KAAK,CAAC;UAC5E;SACD,CAAC;QACFE,OAAO,CAACW,GAAG,CAAC,8BAA8B,EAAEqB,QAAQ,CAAC;QACrD,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,+BAA+B,CAAC;QACjE,IAAI,CAACf,UAAU,EAAE;QACjB,IAAI,CAACtG,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC;QACxC;MACF,CAAC;;MACD+E,KAAK,EAAGA,KAAK,IAAI;QACfE,OAAO,CAACF,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5CmF,KAAK,CAAC,2BAA2B,CAAC;MACpC;KACD,CAAC;EACJ;EAMAC,eAAeA,CAACpE,KAAY;IAC1B,MAAMqE,IAAI,GAAIrE,KAAK,CAACC,MAA2B,CAACqE,KAAK,GAAG,CAAC,CAAC;IAC1D,IAAID,IAAI,EAAE;MACR,IAAI,CAAC5G,IAAI,CAAC8B,UAAU,CAAC;QAAEtG,KAAK,EAAEoL;MAAI,CAAE,CAAC;MACrC,IAAI,CAAC5G,IAAI,CAACa,GAAG,CAAC,OAAO,CAAC,EAAEiG,sBAAsB,EAAE;MAEhD,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAAC7O,YAAY,GAAG2O,MAAM,CAACG,MAAM;MACnC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACP,IAAI,CAAC;;EAE9B;EAIAQ,cAAcA,CAAC7E,KAAU;IACvB,MAAMqE,IAAI,GAAGrE,KAAK,CAACC,MAAM,CAACqE,KAAK,CAAC,CAAC,CAAC;IAElC,IAAID,IAAI,EAAE;MACR,MAAMS,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEX,IAAI,CAAC;MAE7B,IAAI,CAACtJ,IAAI,CAACkK,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACzG,SAAS,CACpE6C,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACgE,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBjE,QAAQ,CAACgE,QAAQ,EAAE;UAC3DhG,OAAO,CAACW,GAAG,CAAC,mBAAmB,EAAEsF,OAAO,CAAC;UAGzC,IAAI,CAAC1H,IAAI,CAAC8B,UAAU,CAAC;YACnBtG,KAAK,EAAEkM;WACR,CAAC;SACH,MAAM;UACLjG,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEoG,WAAWA,CAAA;IACT,IAAI,CAAC/H,YAAY,GAAG,EAAE;EACxB;EAEIE,YAAYA,CAAA;IAEZ,IAAI,CAACzC,WAAW,CAACuK,WAAW,EAAE,CAAChH,SAAS,CAACqB,IAAI,IAAG;MAChD,IAAI,CAACpE,MAAM,GAAGoE,IAAI;IAEpB,CAAC,CAAC;EACF;EAKF4F,SAASA,CAAA;IACP,IAAI,CAACpK,WAAW,GAAG,IAAI;EACzB;EAEAkF,UAAUA,CAAA;IACR,IAAI,CAAClF,WAAW,GAAG,KAAK;IACxB,IAAI,CAACqK,SAAS,EAAE;EAClB;EAEAnE,cAAcA,CAAA;IACZ,IAAI,CAACjG,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC0C,QAAQ,CAAC2H,KAAK,EAAE;IACrB,IAAI,CAAC1I,SAAS,GAAG,KAAK;EACxB;EAEA2I,uBAAuBA,CAACzF,KAAiB;IACvC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACiB,cAAc,EAAE;;EAEzB;EAEA;EACAD,gBAAgBA,CAACvK,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACF,YAAY,GAAG;MAClBmF,IAAI,EAAE,IAAI;MACVlF,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACA6O,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAChP,YAAY,CAACmF,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACA8E,kBAAkBA,CAACU,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMsE,OAAO,GAAG,IAAIxJ,IAAI,CAACkF,IAAI,CAAC;MAC9B,IAAIuE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAOhH,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAiH,QAAQA,CAACxE,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAC7G,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAAC2H,IAAI,CAAC;;EAE9B;EAEAyE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjM,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAkM,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClM,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAmM,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxM,WAAW,GAAGuM,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAChM,UAAU,GAAG,CAAC,EAAE2L,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;;;uBAv5BWxL,oBAAoB,EAAAlG,EAAA,CAAAoS,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtS,EAAA,CAAAoS,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAxS,EAAA,CAAAoS,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1S,EAAA,CAAAoS,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApB1M,oBAAoB;MAAA2M,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBjCnT,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAgB,SAAA,cAAsB;UAEtBhB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAgB,SAAA,iBAAyB;UAIrBhB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAgB,SAAA,iBAES;UAEThB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAA8B;UACTD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAgB,SAAA,iBAK6C;UAE7ChB,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAAwC,UAAA,4BAAA6Q,0EAAAC,MAAA;YAAA,OAAkBF,GAAA,CAAAjI,oBAAA,CAAAmI,MAAA,CAAAC,MAAA,CAAAjK,KAAA,CAAyC;UAAA,EAAC;UAC5DtJ,EAAA,CAAAyD,UAAA,KAAA+P,2CAAA,yBAEa;UACfxT,EAAA,CAAAG,YAAA,EAAmB;UAK7BH,EAAA,CAAAC,cAAA,eAAsB;UACMD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,kBAAuF;UAA3DD,EAAA,CAAAwC,UAAA,2BAAAiR,+DAAAH,MAAA;YAAA,OAAAF,GAAA,CAAA5K,cAAA,GAAA8K,MAAA;UAAA,EAA4B,oBAAAI,wDAAA;YAAA,OAAWN,GAAA,CAAAjO,eAAA,CAAgB,CAAC,CAAC;UAAA,EAA7B;UACtDnF,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnDH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOtDH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAAwC,UAAA,2BAAAmR,8DAAAL,MAAA;YAAA,OAAAF,GAAA,CAAA/L,UAAA,GAAAiM,MAAA;UAAA,EAAwB,mBAAAM,sDAAA;YAAA,OACfR,GAAA,CAAA5F,cAAA,EAAgB;UAAA,EADD;UAH1BxN,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAgB,SAAA,gBAAiC;UACnChB,EAAA,CAAAG,YAAA,EAAM;UAORH,EAAA,CAAAC,cAAA,eAAmG;UAA1CD,EAAA,CAAAwC,UAAA,mBAAAqR,oDAAAP,MAAA;YAAA,OAASF,GAAA,CAAAtC,uBAAA,CAAAwC,MAAA,CAA+B;UAAA,EAAC;UAChGtT,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAAsR,oDAAAR,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3D/T,EAAA,CAAAC,cAAA,gBAA+C;UAA3BD,EAAA,CAAAwC,UAAA,mBAAAwR,qDAAA;YAAA,OAASZ,GAAA,CAAA3G,cAAA,EAAgB;UAAA,EAAC;UAACzM,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7DH,EAAA,CAAAC,cAAA,cAAmD;UAAAD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7EH,EAAA,CAAAC,cAAA,gBAAoE;UAAvCD,EAAA,CAAAwC,UAAA,sBAAAyR,wDAAA;YAAA,OAAYb,GAAA,CAAAlH,YAAA,EAAc;UAAA,EAAC;UACtDlM,EAAA,CAAAgB,SAAA,UAAI;UAGJhB,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAgB,SAAA,iBAI6C;UAC7ChB,EAAA,CAAAC,cAAA,gCAEiF;UAA/DD,EAAA,CAAAwC,UAAA,4BAAA0R,0EAAAZ,MAAA;YAAA,OAAkBF,GAAA,CAAAvI,sBAAA,CAAAyI,MAAA,CAAAC,MAAA,CAAAjK,KAAA,CAA2C;UAAA,EAAC;UAC9EtJ,EAAA,CAAAyD,UAAA,KAAA0Q,2CAAA,yBAEa;UACfnU,EAAA,CAAAG,YAAA,EAAmB;UAGrBH,EAAA,CAAAyD,UAAA,KAAA2Q,oCAAA,kBAEM;UAGNpU,EAAA,CAAAC,cAAA,iBAAqF;UAAAD,EAAA,CAAAE,MAAA,iCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC5GH,EAAA,CAAAgB,SAAA,iBAME;UACFhB,EAAA,CAAAyD,UAAA,KAAA4Q,oCAAA,kBAEM;UAGNrU,EAAA,CAAAC,cAAA,iBAAwF;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvHH,EAAA,CAAAgB,SAAA,iBAME;UAGFhB,EAAA,CAAAC,cAAA,iBAA4F;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtHH,EAAA,CAAAgB,SAAA,iBAKE;UACFhB,EAAA,CAAAyD,UAAA,KAAA6Q,oCAAA,kBAEM;UAENtU,EAAA,CAAAC,cAAA,iBAAwF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3GH,EAAA,CAAAC,cAAA,kBAOD;UAC4CD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAAyD,UAAA,KAAA8Q,uCAAA,qBAES;UACXvU,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAyD,UAAA,KAAA+Q,oCAAA,kBAEM;UAGNxU,EAAA,CAAAC,cAAA,iBAAkF;UAAAD,EAAA,CAAAE,MAAA,0CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvHH,EAAA,CAAAC,cAAA,iBAME;UAHAD,EAAA,CAAAwC,UAAA,oBAAAiS,uDAAAnB,MAAA;YAAA,OAAUF,GAAA,CAAAlD,cAAA,CAAAoD,MAAA,CAAsB;UAAA,EAAC;UAHnCtT,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAyD,UAAA,KAAAiR,oCAAA,kBAEM;UAEN1U,EAAA,CAAAgB,SAAA,UAAM;UACNhB,EAAA,CAAAC,cAAA,kBAA8C;UAC5CD,EAAA,CAAAE,MAAA,mBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAQfH,EAAA,CAAAC,cAAA,gBAA2F;UAAtCD,EAAA,CAAAwC,UAAA,mBAAAmS,qDAAArB,MAAA;YAAA,OAASF,GAAA,CAAAhI,mBAAA,CAAAkI,MAAA,CAA2B;UAAA,EAAC;UACxFtT,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAAoS,qDAAAtB,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3D/T,EAAA,CAAAC,cAAA,iBAA2C;UAAvBD,EAAA,CAAAwC,UAAA,mBAAAqS,sDAAA;YAAA,OAASzB,GAAA,CAAA3H,UAAA,EAAY;UAAA,EAAC;UAACzL,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,eAAoD;UAAAD,EAAA,CAAAE,MAAA,qCAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACzFH,EAAA,CAAAC,cAAA,iBAA8D;UAArCD,EAAA,CAAAwC,UAAA,sBAAAsS,yDAAA;YAAA,OAAY1B,GAAA,CAAA9D,UAAA,EAAY;UAAA,EAAC;UAChDtP,EAAA,CAAAgB,SAAA,WAAI;UAGNhB,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,oBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAgB,SAAA,kBAI6C;UAC7ChB,EAAA,CAAAC,cAAA,iCAEgF;UAA9DD,EAAA,CAAAwC,UAAA,4BAAAuS,2EAAAzB,MAAA;YAAA,OAAkBF,GAAA,CAAAzI,qBAAA,CAAA2I,MAAA,CAAAC,MAAA,CAAAjK,KAAA,CAA0C;UAAA,EAAC;UAC7EtJ,EAAA,CAAAyD,UAAA,MAAAuR,4CAAA,yBAEa;UACfhV,EAAA,CAAAG,YAAA,EAAmB;UAGnBH,EAAA,CAAAyD,UAAA,MAAAwR,qCAAA,kBAEM;UAGNjV,EAAA,CAAAC,cAAA,kBAAiF;UAAAD,EAAA,CAAAE,MAAA,kCAAe;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxGH,EAAA,CAAAgB,SAAA,kBAME;UACFhB,EAAA,CAAAyD,UAAA,MAAAyR,qCAAA,kBAEM;UAGNlV,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAgB,SAAA,kBAME;UAGFhB,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAClHH,EAAA,CAAAgB,SAAA,kBAKE;UACFhB,EAAA,CAAAyD,UAAA,MAAA0R,qCAAA,kBAEM;UACLnV,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1GH,EAAA,CAAAC,cAAA,mBAQC;UACyCD,EAAA,CAAAE,MAAA,sCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAAyD,UAAA,MAAA2R,wCAAA,qBAES;UACXpV,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAyD,UAAA,MAAA4R,qCAAA,kBAEM;UAIJrV,EAAA,CAAAC,cAAA,kBAA8E;UAAAD,EAAA,CAAAE,MAAA,2CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,kBAME;UAHAD,EAAA,CAAAwC,UAAA,oBAAA8S,wDAAAhC,MAAA;YAAA,OAAUF,GAAA,CAAAlD,cAAA,CAAAoD,MAAA,CAAsB;UAAA,EAAC;UAHnCtT,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAyD,UAAA,MAAA8R,qCAAA,kBAEM;UAENvV,EAAA,CAAAgB,SAAA,WAAM;UACNhB,EAAA,CAAAC,cAAA,mBAA8C;UAC5CD,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UA2BXH,EAAA,CAAAC,cAAA,gBAAiH;UAAjDD,EAAA,CAAAwC,UAAA,mBAAAgT,qDAAAlC,MAAA;YAAA,OAASF,GAAA,CAAAqC,8BAAA,CAAAnC,MAAA,CAAsC;UAAA,EAAC;UAC9GtT,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAAkT,qDAAApC,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3D/T,EAAA,CAAAC,cAAA,iBAAsD;UAAlCD,EAAA,CAAAwC,UAAA,mBAAAmT,sDAAA;YAAA,OAASvC,GAAA,CAAArE,qBAAA,EAAuB;UAAA,EAAC;UAAC/O,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,eAAkD;UAAAD,EAAA,CAAAE,MAAA,mCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAyD,UAAA,MAAAmS,qCAAA,kBAGM;UAEN5V,EAAA,CAAAC,cAAA,iBAAuE;UAAnCD,EAAA,CAAAwC,UAAA,sBAAAqT,yDAAA;YAAA,OAAYzC,GAAA,CAAA1E,mBAAA,EAAqB;UAAA,EAAC;UAE1E1O,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAgB,SAAA,kBAK6C;UAE7ChB,EAAA,CAAAC,cAAA,iCAGyD;UAAvDD,EAAA,CAAAwC,UAAA,4BAAAsT,2EAAAxC,MAAA;YAAA,OAAkBF,GAAA,CAAAnI,cAAA,CAAAqI,MAAA,CAAAC,MAAA,CAAAjK,KAAA,CAAmC;UAAA,EAAC;UACtDtJ,EAAA,CAAAyD,UAAA,MAAAsS,4CAAA,yBAEa;UACf/V,EAAA,CAAAG,YAAA,EAAmB;UAIrBH,EAAA,CAAAyD,UAAA,MAAAuS,qCAAA,kBAGM;UAIAhW,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,qBAKyH;UACzHD,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAAgB,SAAA,kBAIuG;UAGvGhB,EAAA,CAAAC,cAAA,mBAAyC;UACtCD,EAAA,CAAAE,MAAA,qCACH;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAWfH,EAAA,CAAAC,cAAA,gBAAyH;UAArDD,EAAA,CAAAwC,UAAA,mBAAAyT,qDAAA3C,MAAA;YAAA,OAASF,GAAA,CAAA8C,kCAAA,CAAA5C,MAAA,CAA0C;UAAA,EAAC;UACtHtT,EAAA,CAAAC,cAAA,gBAA8D;UAAnCD,EAAA,CAAAwC,UAAA,mBAAA2T,qDAAA7C,MAAA;YAAA,OAASA,MAAA,CAAAS,eAAA,EAAwB;UAAA,EAAC;UAC3D/T,EAAA,CAAAC,cAAA,iBAA0D;UAAtCD,EAAA,CAAAwC,UAAA,mBAAA4T,sDAAA;YAAA,OAAShD,GAAA,CAAA/D,yBAAA,EAA2B;UAAA,EAAC;UAACrP,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAC,cAAA,eAAkD;UAAAD,EAAA,CAAAE,MAAA,mCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAyD,UAAA,MAAA4S,qCAAA,kBAGM;UAEVrW,EAAA,CAAAC,cAAA,qBAA8G;UAAxGD,EAAA,CAAAwC,UAAA,sBAAA8T,yDAAA;YAAA,OAAAlD,GAAA,CAAA3R,kBAAA,IAAkC2R,GAAA,CAAAmD,kBAAA,CAAAnD,GAAA,CAAA3R,kBAAA,CAAsC;UAAA,EAAC;UAG7EzB,EAAA,CAAAC,cAAA,2BAA0D;UAC7CD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAgB,SAAA,kBAMU;UAEPhB,EAAA,CAAAC,cAAA,iCAGyD;UAAvDD,EAAA,CAAAwC,UAAA,4BAAAgU,2EAAAlD,MAAA;YAAA,OAAkBF,GAAA,CAAAnI,cAAA,CAAAqI,MAAA,CAAAC,MAAA,CAAAjK,KAAA,CAAmC;UAAA,EAAC;UACtDtJ,EAAA,CAAAyD,UAAA,MAAAgT,4CAAA,yBAEa;UACfzW,EAAA,CAAAG,YAAA,EAAmB;UAGrBH,EAAA,CAAAyD,UAAA,MAAAiT,qCAAA,kBAEM;UAGN1W,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,qBAMyH;UAJvHD,EAAA,CAAAwC,UAAA,2BAAAmU,kEAAArD,MAAA;YAAA,OAAAF,GAAA,CAAAzL,iBAAA,CAAAE,WAAA,GAAAyL,MAAA;UAAA,EAA2C;UAK7CtT,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAAC,cAAA,kBAKuG;UAFrGD,EAAA,CAAAwC,UAAA,2BAAAoU,+DAAAtD,MAAA;YAAA,OAAAF,GAAA,CAAAzL,iBAAA,CAAAnD,eAAA,GAAA8O,MAAA;UAAA,EAA+C;UAHjDtT,EAAA,CAAAG,YAAA,EAKuG;UAGvGH,EAAA,CAAAgB,SAAA,WAAM;UACNhB,EAAA,CAAAC,cAAA,mBAAyC;UACzCD,EAAA,CAAAE,MAAA,+BACA;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAkGXH,EAAA,CAAAC,cAAA,iBAAuB;UAInBD,EAAA,CAAAyD,UAAA,MAAAoT,qCAAA,kBAEM;UAGN7W,EAAA,CAAAC,cAAA,eAA6B;UAS6BD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAiD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAyD,UAAA,MAAAqT,oCAAA,mBA4FK;UACP9W,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAyD,UAAA,MAAAsT,qCAAA,kBAgBM;UAEJ/W,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,gBAAiB;UAGMD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;;;;;;;;;;UA3pBzJH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA9K,qBAAA,CAAqC,oBAAA0O,GAAA;UAMrChX,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA5I,kBAAA,CAAkC;UAELxK,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAArM,0BAAA,CAA6B;UAUxC/G,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAA5K,cAAA,CAA4B;UAiBpDxI,EAAA,CAAAM,SAAA,IAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAA/L,UAAA,CAAwB;UAYXrH,EAAA,CAAAM,SAAA,GAAqC;UAArCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAiX,eAAA,KAAAC,GAAA,EAAA9D,GAAA,CAAA5M,eAAA,EAAqC;UAI9CxG,EAAA,CAAAM,SAAA,GAAsB;UAAtBN,EAAA,CAAAI,UAAA,cAAAgT,GAAA,CAAAlK,QAAA,CAAsB;UASjBlJ,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,oBAAA+W,GAAA,CAAiC;UAGtBnX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA3I,YAAA,CAA4B;UAEdzK,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAApM,OAAA,CAAU;UAMtChH,EAAA,CAAAM,SAAA,GAAqF;UAArFN,EAAA,CAAAI,UAAA,WAAAgX,QAAA,GAAAhE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,4BAAAyN,QAAA,CAAAjL,OAAA,QAAAiL,QAAA,GAAAhE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,4BAAAyN,QAAA,CAAAC,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAAqF;UAarFnI,EAAA,CAAAM,SAAA,GAA2F;UAA3FN,EAAA,CAAAI,UAAA,WAAAkX,QAAA,GAAAlE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,+BAAA2N,QAAA,CAAAnL,OAAA,QAAAmL,QAAA,GAAAlE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,+BAAA2N,QAAA,CAAAD,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAA2F;UAsB3FnI,EAAA,CAAAM,SAAA,GAAyG;UAAzGN,EAAA,CAAAI,UAAA,WAAAmX,QAAA,GAAAnE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,sCAAA4N,QAAA,CAAApL,OAAA,QAAAoL,QAAA,GAAAnE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,sCAAA4N,QAAA,CAAAF,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAAyG;UAarGnI,EAAA,CAAAM,SAAA,GAAgB;UAAhBN,EAAA,CAAAI,UAAA,iBAAgB;UACQJ,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAhL,YAAA,CAAe;UAI3CpI,EAAA,CAAAM,SAAA,GAAmG;UAAnGN,EAAA,CAAAI,UAAA,WAAAoX,QAAA,GAAApE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,mCAAA6N,QAAA,CAAArL,OAAA,QAAAqL,QAAA,GAAApE,GAAA,CAAAlK,QAAA,CAAAS,GAAA,mCAAA6N,QAAA,CAAAH,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAAmG;UAanGnI,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAAlS,YAAA,CAAkB;UAeXlB,EAAA,CAAAM,SAAA,GAAiC;UAAjCN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAiX,eAAA,KAAAC,GAAA,EAAA9D,GAAA,CAAA7M,WAAA,EAAiC;UAI9CvG,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,cAAAgT,GAAA,CAAAtK,IAAA,CAAkB;UASf9I,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,oBAAAqX,IAAA,CAA6B;UAGlBzX,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA3I,YAAA,CAA4B;UAEdzK,EAAA,CAAAM,SAAA,GAAU;UAAVN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAApM,OAAA,CAAU;UAMpChH,EAAA,CAAAM,SAAA,GAA6E;UAA7EN,EAAA,CAAAI,UAAA,WAAAsX,QAAA,GAAAtE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,4BAAA+N,QAAA,CAAAvL,OAAA,QAAAuL,QAAA,GAAAtE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,4BAAA+N,QAAA,CAAAL,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAA6E;UAa7EnI,EAAA,CAAAM,SAAA,GAAmF;UAAnFN,EAAA,CAAAI,UAAA,WAAAuX,QAAA,GAAAvE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,+BAAAgO,QAAA,CAAAxL,OAAA,QAAAwL,QAAA,GAAAvE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,+BAAAgO,QAAA,CAAAN,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAAmF;UAsBnFnI,EAAA,CAAAM,SAAA,GAAiG;UAAjGN,EAAA,CAAAI,UAAA,WAAAwX,QAAA,GAAAxE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,sCAAAiO,QAAA,CAAAzL,OAAA,QAAAyL,QAAA,GAAAxE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,sCAAAiO,QAAA,CAAAP,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAAiG;UAahGnI,EAAA,CAAAM,SAAA,GAAgB;UAAhBN,EAAA,CAAAI,UAAA,iBAAgB;UACSJ,EAAA,CAAAM,SAAA,GAAe;UAAfN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAhL,YAAA,CAAe;UAI3CpI,EAAA,CAAAM,SAAA,GAA2F;UAA3FN,EAAA,CAAAI,UAAA,WAAAyX,QAAA,GAAAzE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,mCAAAkO,QAAA,CAAA1L,OAAA,QAAA0L,QAAA,GAAAzE,GAAA,CAAAtK,IAAA,CAAAa,GAAA,mCAAAkO,QAAA,CAAAR,OAAA,KAAAjE,GAAA,CAAAjL,SAAA,EAA2F;UAczFnI,EAAA,CAAAM,SAAA,GAAkB;UAAlBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAAlS,YAAA,CAAkB;UAkCPlB,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAiX,eAAA,KAAAC,GAAA,EAAA9D,GAAA,CAAA3M,sBAAA,EAA4C;UAKrDzG,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAA3R,kBAAA,CAAwB;UAKxBzB,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,cAAAgT,GAAA,CAAAjK,eAAA,CAA6B;UAOnCnJ,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA/K,eAAA,CAA+B,oBAAAyP,IAAA;UAM/B9X,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA5I,kBAAA,CAAkC;UAELxK,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAtM,oBAAA,CAAuB;UAOlD9G,EAAA,CAAAM,SAAA,GAAqE;UAArEN,EAAA,CAAAI,UAAA,YAAA2X,QAAA,GAAA3E,GAAA,CAAAjK,eAAA,CAAAQ,GAAA,2BAAAoO,QAAA,CAAAzO,KAAA,KAAA8J,GAAA,CAAA9L,wBAAA,CAAqE;UAsCxDtH,EAAA,CAAAM,SAAA,IAAgD;UAAhDN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAiX,eAAA,KAAAC,GAAA,EAAA9D,GAAA,CAAA1M,0BAAA,EAAgD;UAKzD1G,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAA3R,kBAAA,CAAwB;UAahCzB,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA/K,eAAA,CAA+B,oBAAAyP,IAAA;UAO3B9X,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAgT,GAAA,CAAA5I,kBAAA,CAAkC;UAELxK,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAtM,oBAAA,CAAuB;UAMlD9G,EAAA,CAAAM,SAAA,GAA6D;UAA7DN,EAAA,CAAAI,UAAA,UAAAgT,GAAA,CAAAzL,iBAAA,CAAAG,IAAA,IAAAsL,GAAA,CAAA7L,4BAAA,CAA6D;UAQjEvH,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAzL,iBAAA,CAAAE,WAAA,CAA2C;UAY3C7H,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAzL,iBAAA,CAAAnD,eAAA,CAA+C;UA8GzCxE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAApR,YAAA,CAAAmF,IAAA,CAAuB;UAyBSnH,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAAgT,GAAA,CAAAxM,UAAA,CAAa;UAwGN5G,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAgT,GAAA,CAAAnN,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
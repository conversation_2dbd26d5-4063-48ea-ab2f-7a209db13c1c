spring.application.name=microserviceUser
spring.datasource.url=**********************************************************************************************************************************************************************

spring.datasource.username=root

spring.datasource.password=

### JPA / HIBERNATE ###
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=dlglxrxhhzasabgc
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

spring.jpa.show-sql=true

spring.jpa.hibernate.ddl-auto=update
server.port=8088
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect


eureka.client.register-with-eureka=true
eureka.client.service-url.defaultZone=http://localhost:8761/eureka

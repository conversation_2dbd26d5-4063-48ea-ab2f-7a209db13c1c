{"ast": null, "code": "import { Equip } from 'src/app/equipement/equip';\nimport { FormControl } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../Shared/layout/layout.component\";\nfunction EquipementsComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r14);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r14.firstName, \" \", user_r14.lastName, \" - \", user_r14.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"h5\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedEquipement.model == null ? null : ctx_r2.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r2.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementsComponent_mat_option_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r15);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r15.firstName, \" \", user_r15.lastName, \" - \", user_r15.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1, \" L'utilisateur est requis\\n\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"h5\", 70);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 71);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r6.selectedEquipement.model == null ? null : ctx_r6.selectedEquipement.model.nomModel);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"N\\u00B0 S\\u00E9rie: \", ctx_r6.selectedEquipement.numSerie, \"\");\n  }\n}\nfunction EquipementsComponent_mat_option_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 68);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r16);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r16.firstName, \" \", user_r16.lastName, \" - \", user_r16.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_101_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72);\n    i0.ɵɵtext(1, \" L'utilisateur est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_div_114_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r11.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r11.notification.message, \" \");\n  }\n}\nfunction EquipementsComponent_tr_141_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r18.NameUtilisateur[equip_r17.idEqui]), \" \");\n  }\n}\nfunction EquipementsComponent_tr_141_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_141_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const equip_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.openAffectationModal(equip_r17));\n    });\n    i0.ɵɵtext(1, \" Affecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_141_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_141_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const equip_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.openEditedModal(equip_r17));\n    });\n    i0.ɵɵtext(1, \" \\uD83D\\uDD04 \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_141_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 93);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_141_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const equip_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.desaffecterEquipement(equip_r17));\n    });\n    i0.ɵɵtext(1, \" D\\u00E9saffecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EquipementsComponent_tr_141_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 74);\n    i0.ɵɵelement(2, \"img\", 75);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 74)(4, \"div\", 76)(5, \"h6\", 77);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 78);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 74)(10, \"span\", 79);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 74)(13, \"span\", 79);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 74)(17, \"span\", 80);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementsComponent_tr_141_div_19_Template, 3, 3, \"div\", 81);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 74)(21, \"span\", 82);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 74)(24, \"span\", 79);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 83)(27, \"div\", 84);\n    i0.ɵɵtemplate(28, EquipementsComponent_tr_141_button_28_Template, 2, 0, \"button\", 85);\n    i0.ɵɵtemplate(29, EquipementsComponent_tr_141_button_29_Template, 2, 0, \"button\", 86);\n    i0.ɵɵtemplate(30, EquipementsComponent_tr_141_button_30_Template, 2, 0, \"button\", 87);\n    i0.ɵɵelementStart(31, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_141_Template_button_click_31_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r33);\n      const equip_r17 = restoredCtx.$implicit;\n      const ctx_r32 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r32.openModal1(equip_r17));\n    });\n    i0.ɵɵtext(32, \" \\u270F\\uFE0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_141_Template_button_click_33_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r33);\n      const equip_r17 = restoredCtx.$implicit;\n      const ctx_r34 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r34.confirmDelete(equip_r17.idEqui));\n    });\n    i0.ɵɵtext(34, \" \\uD83D\\uDDD1\\uFE0F \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const equip_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r17.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r17.model == null ? null : equip_r17.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r17.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 16, equip_r17.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r17.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r17.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r17.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r17.statut.toLowerCase() === \"affecte\" || equip_r17.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", equip_r17.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r17.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((equip_r17.fournisseur == null ? null : equip_r17.fournisseur.nomFournisseur) || \"Aucun fournisseur\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", equip_r17.statut === \"DISPONIBLE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r17.statut === \"AFFECTE\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r17.statut === \"AFFECTE\");\n  }\n}\nfunction EquipementsComponent_nav_142_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 96)(1, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_142_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const i_r37 = restoredCtx.index;\n      const ctx_r38 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r38.loadEquipements(i_r37));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r37 = ctx.index;\n    const ctx_r35 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r37 === ctx_r35.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r37 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementsComponent_nav_142_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 94)(1, \"ul\", 95)(2, \"li\", 96)(3, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_142_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.loadEquipements(ctx_r40.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementsComponent_nav_142_li_5_Template, 3, 3, \"li\", 98);\n    i0.ɵɵelementStart(6, \"li\", 96)(7, \"a\", 97);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_142_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r41);\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.loadEquipements(ctx_r42.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r13.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r13.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r13.currentPage === ctx_r13.totalPages - 1);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.signupErrors = {};\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    equiements.forEach(eq => {\n      this.idsEqui[eq.idEqui] = eq.idEqui;\n    });\n    console.log(this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  static {\n    this.ɵfac = function EquipementsComponent_Factory(t) {\n      return new (t || EquipementsComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementsComponent,\n      selectors: [[\"app-equipements\"]],\n      decls: 152,\n      vars: 30,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"form-label\"], [1, \"form-select\", 3, \"ngModel\", \"ngModelChange\", \"change\"], [\"value\", \"\"], [\"value\", \"DISPONIBLE\"], [\"value\", \"AFFECTE\"], [\"value\", \"MAINTENANCE\"], [\"value\", \"HORS_SERVICE\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"20px\"], [\"style\", \"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\", 4, \"ngIf\"], [3, \"formGroup\", \"ngSubmit\"], [\"auto\", \"matAutocomplete\"], [\"style\", \"color:red; font-size: 12px;\", 4, \"ngIf\"], [\"for\", \"commentaire\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"formControlName\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\"], [\"for\", \"dateAffectation\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"date\", \"formControlName\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\"], [\"type\", \"submit\", 1, \"btn-submit\"], [3, \"ngSubmit\"], [\"editAffectationForm\", \"ngForm\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", \"required\", \"\", 3, \"formControl\", \"matAutocomplete\"], [\"name\", \"commentaire\", \"rows\", \"3\", \"placeholder\", \"Commentaire sur l'affectation (optionnel)...\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", \"resize\", \"vertical\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"date\", \"name\", \"dateAffectation\", 1, \"form-inputp\", 2, \"width\", \"100%\", \"padding\", \"12px\", \"margin-bottom\", \"15px\", \"border\", \"1px solid #ddd\", \"border-radius\", \"5px\", 3, \"ngModel\", \"ngModelChange\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [3, \"value\"], [2, \"margin-bottom\", \"20px\", \"padding\", \"15px\", \"background-color\", \"#f8f9fa\", \"border-radius\", \"8px\"], [2, \"margin\", \"0\", \"color\", \"#333\"], [2, \"margin\", \"5px 0 0 0\", \"color\", \"#666\", \"font-size\", \"14px\"], [2, \"color\", \"red\", \"font-size\", \"12px\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"fw-normal\", \"text-truncate\", 2, \"max-width\", \"150px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-1\", \"text-end\"], [1, \"d-flex\", \"justify-content-end\", \"gap-1\"], [\"class\", \"btn btn-sm btn-outline-primary\", \"title\", \"Affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-warning\", \"title\", \"R\\u00E9affecter\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-sm btn-outline-secondary\", \"title\", \"D\\u00E9saffecter\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-sm\", \"btn-outline-danger\", 3, \"click\"], [1, \"text-muted\", \"small\", \"mt-1\"], [\"title\", \"Affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-primary\", 3, \"click\"], [\"title\", \"R\\u00E9affecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-warning\", 3, \"click\"], [\"title\", \"D\\u00E9saffecter\", 1, \"btn\", \"btn-sm\", \"btn-outline-secondary\", 3, \"click\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function EquipementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel Panne \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementsComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(41, \"div\", 17)(42, \"label\", 23);\n          i0.ɵɵtext(43, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"select\", 24);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_select_ngModelChange_44_listener($event) {\n            return ctx.selectedStatut = $event;\n          })(\"change\", function EquipementsComponent_Template_select_change_44_listener() {\n            return ctx.loadEquipements(0);\n          });\n          i0.ɵɵelementStart(45, \"option\", 25);\n          i0.ɵɵtext(46, \"Tous les statuts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"option\", 26);\n          i0.ɵɵtext(48, \"Disponible\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"option\", 27);\n          i0.ɵɵtext(50, \"Affect\\u00E9\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"option\", 28);\n          i0.ɵɵtext(52, \"En maintenance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"option\", 29);\n          i0.ɵɵtext(54, \"Hors service\");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(55, \"div\", 30)(56, \"div\", 31)(57, \"input\", 32);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_57_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementsComponent_Template_input_input_57_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(58, \"span\", 33);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(59, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_59_listener($event) {\n            return ctx.closeOnOutsideClickAffectation($event);\n          });\n          i0.ɵɵelementStart(60, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_60_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(61, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_61_listener() {\n            return ctx.closeAffectationModal();\n          });\n          i0.ɵɵtext(62, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"h3\", 37);\n          i0.ɵɵtext(64, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(65, EquipementsComponent_div_65_Template, 5, 2, \"div\", 38);\n          i0.ɵɵelementStart(66, \"form\", 39);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_66_listener() {\n            return ctx.onAffectationSubmit();\n          });\n          i0.ɵɵelementStart(67, \"mat-form-field\", 18)(68, \"mat-label\");\n          i0.ɵɵtext(69, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"input\", 19);\n          i0.ɵɵelementStart(71, \"mat-autocomplete\", 20, 40);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_71_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(73, EquipementsComponent_mat_option_73_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(74, EquipementsComponent_div_74_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(75, \"label\", 42);\n          i0.ɵɵtext(76, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"textarea\", 43);\n          i0.ɵɵtext(78, \"      \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"label\", 44);\n          i0.ɵɵtext(80, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(81, \"input\", 45)(82, \"br\");\n          i0.ɵɵelementStart(83, \"button\", 46);\n          i0.ɵɵtext(84, \" Affecter l'\\u00E9quipement \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(85, \"div\", 34);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_85_listener($event) {\n            return ctx.closeOnOutsideClickAffectationEdit($event);\n          });\n          i0.ɵɵelementStart(86, \"div\", 35);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_div_click_86_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(87, \"span\", 36);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_span_click_87_listener() {\n            return ctx.closeAffectationEditModal();\n          });\n          i0.ɵɵtext(88, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"h3\", 37);\n          i0.ɵɵtext(90, \"Affecter l'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(91, EquipementsComponent_div_91_Template, 5, 2, \"div\", 38);\n          i0.ɵɵelementStart(92, \"form\", 47, 48);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_92_listener() {\n            return ctx.selectedEquipement && ctx.updateReaffication(ctx.selectedEquipement);\n          });\n          i0.ɵɵelementStart(94, \"mat-form-field\", 18)(95, \"mat-label\");\n          i0.ɵɵtext(96, \"Utilisateur Actuel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(97, \"input\", 49);\n          i0.ɵɵelementStart(98, \"mat-autocomplete\", 20, 40);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_98_listener($event) {\n            return ctx.onUserSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(100, EquipementsComponent_mat_option_100_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(101, EquipementsComponent_div_101_Template, 2, 0, \"div\", 41);\n          i0.ɵɵelementStart(102, \"label\", 42);\n          i0.ɵɵtext(103, \"Commentaire (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"textarea\", 50);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_textarea_ngModelChange_104_listener($event) {\n            return ctx.EditedAffectation.commentaire = $event;\n          });\n          i0.ɵɵtext(105, \"  \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(106, \"label\", 44);\n          i0.ɵɵtext(107, \"Date d'affectation (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"input\", 51);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_108_listener($event) {\n            return ctx.EditedAffectation.dateAffectation = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(109, \"br\");\n          i0.ɵɵelementStart(110, \"button\", 46);\n          i0.ɵɵtext(111, \" Modifier Affectation \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(112, \"body\", 52)(113, \"div\", 53);\n          i0.ɵɵtemplate(114, EquipementsComponent_div_114_Template, 2, 2, \"div\", 54);\n          i0.ɵɵelementStart(115, \"div\", 7)(116, \"div\", 55)(117, \"div\", 56)(118, \"div\", 57)(119, \"div\", 14)(120, \"div\", 58)(121, \"table\", 59)(122, \"thead\")(123, \"tr\")(124, \"th\", 60);\n          i0.ɵɵtext(125, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(126, \"th\", 60);\n          i0.ɵɵtext(127, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(128, \"th\", 60);\n          i0.ɵɵtext(129, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"th\", 60);\n          i0.ɵɵtext(131, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"th\", 60);\n          i0.ɵɵtext(133, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"th\", 60);\n          i0.ɵɵtext(135, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"th\", 60);\n          i0.ɵɵtext(137, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"th\", 61);\n          i0.ɵɵtext(139, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(140, \"tbody\");\n          i0.ɵɵtemplate(141, EquipementsComponent_tr_141_Template, 35, 19, \"tr\", 62);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(142, EquipementsComponent_nav_142_Template, 9, 6, \"nav\", 63);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(143, \"div\", 55)(144, \"div\", 64)(145, \"p\", 65);\n          i0.ɵɵtext(146, \"Design and Developed by \");\n          i0.ɵɵelementStart(147, \"a\", 66);\n          i0.ɵɵtext(148, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(149, \" Distributed by \");\n          i0.ɵɵelementStart(150, \"a\", 67);\n          i0.ɵɵtext(151, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          const _r3 = i0.ɵɵreference(72);\n          let tmp_13_0;\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedStatut);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(26, _c1, ctx.isAffectationModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.affectationForm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !((tmp_13_0 = ctx.affectationForm.get(\"user\")) == null ? null : tmp_13_0.value) && ctx.affectationFormSubmitted);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(28, _c1, ctx.isAffectationEditModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedEquipement);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurCtrl)(\"matAutocomplete\", _r3);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.EditedAffectation.user && ctx.editAffectationFormSubmitted);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.commentaire);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.EditedAffectation.dateAffectation);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.NgModel, i3.NgForm, i3.FormControlDirective, i3.FormGroupDirective, i3.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r14", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "ɵɵtextInterpolate", "ctx_r2", "selectedEquipement", "model", "nomModel", "ɵɵtextInterpolate1", "numSerie", "user_r15", "ctx_r6", "user_r16", "ctx_r11", "notification", "type", "message", "ɵɵpipeBind1", "ctx_r18", "NameUtilisateur", "equip_r17", "idEqui", "ɵɵlistener", "EquipementsComponent_tr_141_button_28_Template_button_click_0_listener", "ɵɵrestoreView", "_r25", "ɵɵnextContext", "$implicit", "ctx_r23", "ɵɵresetView", "openAffectationModal", "EquipementsComponent_tr_141_button_29_Template_button_click_0_listener", "_r28", "ctx_r26", "openEditedModal", "EquipementsComponent_tr_141_button_30_Template_button_click_0_listener", "_r31", "ctx_r29", "desaffecterEquipement", "ɵɵelement", "ɵɵtemplate", "EquipementsComponent_tr_141_div_19_Template", "EquipementsComponent_tr_141_button_28_Template", "EquipementsComponent_tr_141_button_29_Template", "EquipementsComponent_tr_141_button_30_Template", "EquipementsComponent_tr_141_Template_button_click_31_listener", "restoredCtx", "_r33", "ctx_r32", "openModal1", "EquipementsComponent_tr_141_Template_button_click_33_listener", "ctx_r34", "confirmDelete", "image", "ɵɵsanitizeUrl", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "description", "<PERSON><PERSON><PERSON><PERSON>", "nomFournisseur", "EquipementsComponent_nav_142_li_5_Template_a_click_1_listener", "_r39", "i_r37", "index", "ctx_r38", "loadEquipements", "ɵɵclassProp", "ctx_r35", "currentPage", "EquipementsComponent_nav_142_Template_a_click_3_listener", "_r41", "ctx_r40", "EquipementsComponent_nav_142_li_5_Template", "EquipementsComponent_nav_142_Template_a_click_7_listener", "ctx_r42", "ctx_r13", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "EquipementsComponent", "authservice", "http", "fb", "utilisateurService", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "signupErrors", "ngOnInit", "GetAllModels", "getFournisseur", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "searchUsers", "users", "displayUtilisateur", "displayModel", "getallFournisseur", "data", "onUserSearchSelected", "page", "keyword", "username", "userVal", "searchEquipements1", "getAllEquipements", "log", "for<PERSON>ach", "eq", "getAffectationsByIds", "affectation", "onSearchChange", "onFileSelected", "event", "file", "target", "files", "formData", "FormData", "append", "post", "response", "imageUrl", "fullUrl", "form", "patchValue", "resetErrors", "getAllModel", "showNotification", "setTimeout", "hideNotification", "formatDateForInput", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementsComponent_Template", "rf", "ctx", "EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementsComponent_mat_option_40_Template", "EquipementsComponent_Template_select_ngModelChange_44_listener", "EquipementsComponent_Template_select_change_44_listener", "EquipementsComponent_Template_input_ngModelChange_57_listener", "EquipementsComponent_Template_input_input_57_listener", "EquipementsComponent_Template_div_click_59_listener", "closeOnOutsideClickAffectation", "EquipementsComponent_Template_div_click_60_listener", "stopPropagation", "EquipementsComponent_Template_span_click_61_listener", "closeAffectationModal", "EquipementsComponent_div_65_Template", "EquipementsComponent_Template_form_ngSubmit_66_listener", "onAffectationSubmit", "EquipementsComponent_Template_mat_autocomplete_optionSelected_71_listener", "onUserSelected", "EquipementsComponent_mat_option_73_Template", "EquipementsComponent_div_74_Template", "EquipementsComponent_Template_div_click_85_listener", "closeOnOutsideClickAffectationEdit", "EquipementsComponent_Template_div_click_86_listener", "EquipementsComponent_Template_span_click_87_listener", "closeAffectationEditModal", "EquipementsComponent_div_91_Template", "EquipementsComponent_Template_form_ngSubmit_92_listener", "updateReaffication", "EquipementsComponent_Template_mat_autocomplete_optionSelected_98_listener", "EquipementsComponent_mat_option_100_Template", "EquipementsComponent_div_101_Template", "EquipementsComponent_Template_textarea_ngModelChange_104_listener", "EquipementsComponent_Template_input_ngModelChange_108_listener", "EquipementsComponent_div_114_Template", "EquipementsComponent_tr_141_Template", "EquipementsComponent_nav_142_Template", "_r0", "ɵɵpureFunction1", "_c1", "isAffectationModalOpen", "affectationForm", "_r3", "tmp_13_0", "get", "isAffectationEditModalOpen"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.html"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\n\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[]; \n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) { }\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\n\n\n\n\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\n\n\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\n\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n\n\n\n\n\n\n\n\n \n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n\n\n\n\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getAllEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n  equiements.forEach(eq => {\n   \n    this.idsEqui[eq.idEqui]=eq.idEqui;\n     })\n     console.log(this.idsEqui); \n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n  \n\n\n\n\n\n   \n\n\n\n\n\n\n\n    \n\n\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n \n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n}\n\n  \n\n\n\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Équipements</h2>\n    <p>Gérez les différents types d'équipements informatiques\n\n</p>\n  </div>\n<button class=\"add-user-btn\" >\n  <span class=\"icon\">+</span>Nouvel Panne \n\n</button>\n</div>\n\n<!-- Formulaire de recherche simple -->\n<div class=\"card mt-3 mb-4\">\n  <div class=\"card-body\">\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\n\n    <div class=\"row g-3\">\n      <!-- Recherche par utilisateur -->\n      <div class=\"col-md-6\">\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n          <mat-label>Utilisateur</mat-label>\n          <input\n            type=\"text\"\n            matInput\n            [formControl]=\"utilisateurSearchCtrl\"\n            [matAutocomplete]=\"autoUserSearch\"\n            placeholder=\"Rechercher un utilisateur...\">\n\n          <mat-autocomplete\n            #autoUserSearch=\"matAutocomplete\"\n            [displayWith]=\"displayUtilisateur\"\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n            </mat-option>\n          </mat-autocomplete>\n        </mat-form-field>\n      </div>\n\n      <!-- Recherche par statut -->\n<div class=\"col-md-6\">\n  <label class=\"form-label\">Statut</label>\n  <select class=\"form-select\" [(ngModel)]=\"selectedStatut\" (change)=\"loadEquipements(0)\">\n    <option value=\"\">Tous les statuts</option>\n    <option value=\"DISPONIBLE\">Disponible</option>\n    <option value=\"AFFECTE\">Affecté</option>\n    <option value=\"MAINTENANCE\">En maintenance</option>\n    <option value=\"HORS_SERVICE\">Hors service</option>\n  </select>\n</div>\n</div>\n  </div>\n</div>\n\n<div class=\"search-wrapper\">\n  <div class=\"custom-search\">\n    <input\n      type=\"text\"\n      placeholder=\"Rechercher un equipement...\"\n      [(ngModel)]=\"searchTerm\"\n      (input)=\"onSearchChange()\"\n      class=\"form-control\"\n    />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n\n</div>\n<!-- Modal -->\n\n<!-- Modal de modification -->\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<!-- Modal d'affectation -->\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationModalOpen}\" (click)=\"closeOnOutsideClickAffectation($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeAffectationModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\n\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\n    </div>\n\n    <form [formGroup]=\"affectationForm\" (ngSubmit)=\"onAffectationSubmit()\">\n\n<mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n  <mat-label>Utilisateur</mat-label>\n  <input\n    type=\"text\"\n    matInput\n    [formControl]=\"utilisateurCtrl\"\n    [matAutocomplete]=\"auto\"\n    placeholder=\"Rechercher un utilisateur...\">\n    \n  <mat-autocomplete\n    #auto=\"matAutocomplete\"\n    [displayWith]=\"displayUtilisateur\"\n    (optionSelected)=\"onUserSelected($event.option.value)\">\n    <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\n      {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n    </mat-option>\n  </mat-autocomplete>\n</mat-form-field>\n\n\n<div *ngIf=\"!affectationForm.get('user')?.value && affectationFormSubmitted\"\n     style=\"color:red; font-size: 12px;\">\n  L'utilisateur est requis\n</div>\n\n\n      <!-- Commentaire -->\n      <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\n      <textarea\n        formControlName=\"commentaire\"\n        class=\"form-inputp\"\n        rows=\"3\"\n        placeholder=\"Commentaire sur l'affectation (optionnel)...\"\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\n      </textarea>\n\n      <!-- Date d'affectation -->\n      <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\n      <input\n        type=\"date\"\n        formControlName=\"dateAffectation\"\n        class=\"form-inputp\"\n        style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\n\n      <br />\n      <button type=\"submit\" class=\"btn-submit\">\n         Affecter l'équipement\n      </button>\n    </form>\n  </div>\n</div>\n\n\n\n\n\n\n<!-- Modal d'affectation -->\n<div class=\"modal\" [ngClass]=\"{'show': isAffectationEditModalOpen}\" (click)=\"closeOnOutsideClickAffectationEdit($event)\">\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\n    <span class=\"close\" (click)=\"closeAffectationEditModal()\">&times;</span>\n    <h3 style=\"font-size: 20px; margin-bottom: 20px;\">Affecter l'équipement</h3>\n\n    <div *ngIf=\"selectedEquipement\" style=\"margin-bottom: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;\">\n      <h5 style=\"margin: 0; color: #333;\">{{ selectedEquipement.model?.nomModel }}</h5>\n      <p style=\"margin: 5px 0 0 0; color: #666; font-size: 14px;\">N° Série: {{ selectedEquipement.numSerie }}</p>\n    </div>\n\n<form (ngSubmit)=\"selectedEquipement && updateReaffication(selectedEquipement)\" #editAffectationForm=\"ngForm\">\n\n  <!-- Utilisateur -->\n  <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n    <mat-label>Utilisateur Actuel</mat-label>\n <input\n  type=\"text\"\n  matInput\n  [formControl]=\"utilisateurCtrl\"\n  [matAutocomplete]=\"auto\"\n  placeholder=\"Rechercher un utilisateur...\"\n  required>\n\n    <mat-autocomplete \n      #auto=\"matAutocomplete\" \n      [displayWith]=\"displayUtilisateur\"\n      (optionSelected)=\"onUserSelected($event.option.value)\">\n      <mat-option *ngFor=\"let user of filteredUtilisateurs\" [value]=\"user\">\n        {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n      </mat-option>\n    </mat-autocomplete>\n  </mat-form-field>\n\n  <div *ngIf=\"!EditedAffectation.user && editAffectationFormSubmitted\" style=\"color:red; font-size: 12px;\">\n    L'utilisateur est requis\n  </div>\n\n  <!-- Commentaire -->\n  <label for=\"commentaire\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Commentaire (optionnel)</label>\n  <textarea\n    name=\"commentaire\"\n    [(ngModel)]=\"EditedAffectation.commentaire\"\n    class=\"form-inputp\"\n    rows=\"3\"\n    placeholder=\"Commentaire sur l'affectation (optionnel)...\"\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; resize: vertical;\">\n  </textarea>\n\n  <!-- Date d'affectation -->\n  <label for=\"dateAffectation\" style=\"font-size: 14px; font-weight: 500; color: #000000;\">Date d'affectation (optionnel)</label>\n  <input\n    type=\"date\"\n    name=\"dateAffectation\"\n    [(ngModel)]=\"EditedAffectation.dateAffectation\"\n    class=\"form-inputp\"\n    style=\"width: 100%; padding: 12px; margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px;\">\n\n  <!-- Submit -->\n  <br />\n  <button type=\"submit\" class=\"btn-submit\">\n  Modifier Affectation\n  </button>\n</form>\n\n  </div>\n</div>\n\n\n\n\n\n\n\n<style>\n    .card-custom {\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    }\n\n    .btn-outline-lightblue {\n      border: 1px solid #cfe2ff;\n      color: #0d6efd;\n      background-color: #e7f1ff;\n    }\n\n    .tag {\n      background-color: #e7f1ff;\n      color: #0d6efd;\n      padding: 3px 10px;\n      font-size: 0.8rem;\n      border-radius: 15px;\n      position: absolute;\n      right: 20px;\n      top: 20px;\n    }\n\n.icon-box {\n  font-size: 48px; /* optional - for icon size */\n  width: 100px;     /* increase width */\n  height: 100px;    /* set height */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #0d6efd;\n  margin-right: 10px;\n border-radius: 0% !important;\n}\n\n    .btn-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n.card-custom {\n  border-radius: 12px;\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\n  background-color: #fff;\n  color: #212529; /* Darker text */\n  font-size: 0.95rem; /* Slightly larger base font */\n}\n\n.card-custom strong {\n  font-weight: 600; /* Heavier for labels */\n  color: #1a1a1a;\n}\n\n.card-custom h5 {\n  font-weight: 600;\n  color: #000;\n}\n\n.card-custom small,\n.text-muted {\n  color: #495057 !important; /* Less faded gray */\n}\n\n.icon-box {\n  font-size: 32px;\n  color: #0d6efd;\n  margin-right: 10px;\n}\n\n.tag {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n  padding: 3px 10px;\n  font-size: 0.8rem;\n  border-radius: 15px;\n  position: absolute;\n  right: 20px;\n  top: 20px;\n}\n\n\n\n  </style>\n\n<body class=\"bg-light\">\n  <div class=\"container my-2\">\n\n    <!-- Simple Notification Bar -->\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\n      {{ notification.message }}\n    </div>\n\n    <!-- Tableau des équipements -->\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <div class=\"table-responsive mt-1\">\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Description</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Fournisseur</th>\n                      <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr *ngFor=\"let equip of equiements\">\n                      <!-- Image -->\n                      <td class=\"px-1\">\n                        <img [src]=\"equip.image\"\n                             alt=\"Équipement\"\n                             class=\"rounded-circle img-fluid\"\n                             width=\"40\" height=\"40\" />\n                      </td>\n\n                      <!-- Modèle -->\n                      <td class=\"px-1\">\n                        <div class=\"ms-3\">\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\n                          <span class=\"fw-normal text-muted\">Modèle</span>\n                        </div>\n                      </td>\n\n                      <!-- Numéro de série -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\n                      </td>\n\n                      <!-- Date d'acquisition -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\n                      </td>\n\n                      <!-- Statut -->\n                      <td class=\"px-1\">\n                        <span class=\"badge rounded-pill\"\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\n                              [style.color]=\"'white'\">\n                          {{ equip.statut }}\n                        </span>\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\n                             class=\"text-muted small mt-1\">\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\n                        </div>\n                      </td>\n\n                      <!-- Description -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal text-truncate\" style=\"max-width: 150px; display: inline-block;\"\n                              [title]=\"equip.description\">\n                          {{ equip.description }}\n                        </span>\n                      </td>\n\n                      <!-- Fournisseur -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>\n                      </td>\n\n                      <!-- Actions -->\n                      <td class=\"px-1 text-end\">\n                        <div class=\"d-flex justify-content-end gap-1\">\n                          <!-- Bouton d'affectation conditionnel -->\n                          <button\n                            *ngIf=\"equip.statut === 'DISPONIBLE'\"\n                            class=\"btn btn-sm btn-outline-primary\"\n                            (click)=\"openAffectationModal(equip)\"\n                            title=\"Affecter\">\n                            Affecter\n                          </button>\n                          <button\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\n                            class=\"btn btn-sm btn-outline-warning\"\n                            (click)=\"openEditedModal(equip)\"\n                            title=\"Réaffecter\">\n                            🔄\n                          </button>\n                          <button\n                            *ngIf=\"equip.statut === 'AFFECTE'\"\n                            class=\"btn btn-sm btn-outline-secondary\"\n                            (click)=\"desaffecterEquipement(equip)\"\n                            title=\"Désaffecter\">\n                            Désaffecter\n                          </button>\n                          <button\n                            class=\"btn btn-sm btn-outline-primary\"\n                            (click)=\"openModal1(equip)\"\n                            title=\"Modifier\">\n                            ✏️\n                          </button>\n                          <button\n                            class=\"btn btn-sm btn-outline-danger\"\n                            (click)=\"confirmDelete(equip.idEqui)\"\n                            title=\"Supprimer\">\n                            🗑️\n                          </button>\n                        </div>\n                      </td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n    <!-- Pagination Bootstrap -->\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\n  <ul class=\"pagination\">\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\n    </li>\n\n    <li class=\"page-item\"\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\n        [class.active]=\"i === currentPage\">\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\n    </li>\n\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\n    </li>\n  </ul>\n</nav>\n\n  </div>\n</body>\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            \n          <div class=\"py-6 px-6 text-center\">\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\n  <script src=\"./assets/js/app.min.js\"></script>\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\n  <script src=\"./assets/js/dashboard.js\"></script>\n  <!-- solar icons -->\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\n</body>\n\n</html>"], "mappings": "AAEA,SAASA,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,QAA+B,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;;;;;;;;;;;;;;ICyDjEC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,QAAA,CAAAG,SAAA,OAAAH,QAAA,CAAAI,QAAA,SAAAJ,QAAA,CAAAK,KAAA,MACF;;;;;IAmERV,EAAA,CAAAC,cAAA,cAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,YAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAW,iBAAA,CAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,kBAAAF,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAC,QAAA,CAAwC;IAChBf,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAgB,kBAAA,yBAAAJ,MAAA,CAAAC,kBAAA,CAAAI,QAAA,KAA2C;;;;;IAkBzGjB,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAc,QAAA,CAAc;IAClElB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAW,QAAA,CAAAV,SAAA,OAAAU,QAAA,CAAAT,QAAA,SAAAS,QAAA,CAAAR,KAAA,MACF;;;;;IAKJV,EAAA,CAAAC,cAAA,cACyC;IACvCD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwCFH,EAAA,CAAAC,cAAA,cAA2H;IACrFD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjFH,EAAA,CAAAC,cAAA,YAA4D;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADvEH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAW,iBAAA,CAAAQ,MAAA,CAAAN,kBAAA,CAAAC,KAAA,kBAAAK,MAAA,CAAAN,kBAAA,CAAAC,KAAA,CAAAC,QAAA,CAAwC;IAChBf,EAAA,CAAAM,SAAA,GAA2C;IAA3CN,EAAA,CAAAgB,kBAAA,yBAAAG,MAAA,CAAAN,kBAAA,CAAAI,QAAA,KAA2C;;;;;IAoBvGjB,EAAA,CAAAC,cAAA,qBAAqE;IACnED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFyCH,EAAA,CAAAI,UAAA,UAAAgB,QAAA,CAAc;IAClEpB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAa,QAAA,CAAAZ,SAAA,OAAAY,QAAA,CAAAX,QAAA,SAAAW,QAAA,CAAAV,KAAA,MACF;;;;;IAIJV,EAAA,CAAAC,cAAA,cAAyG;IACvGD,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgIJH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAAiB,OAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFvB,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAgB,kBAAA,MAAAK,OAAA,CAAAC,YAAA,CAAAE,OAAA,MACF;;;;;IAyDoBxB,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAgB,kBAAA,0BAAAhB,EAAA,CAAAyB,WAAA,OAAAC,OAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,MAAA,QACF;;;;;;IAoBE7B,EAAA,CAAAC,cAAA,iBAImB;IADjBD,EAAA,CAAA8B,UAAA,mBAAAC,uEAAA;MAAA/B,EAAA,CAAAgC,aAAA,CAAAC,IAAA;MAAA,MAAAL,SAAA,GAAA5B,EAAA,CAAAkC,aAAA,GAAAC,SAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAD,OAAA,CAAAE,oBAAA,CAAAV,SAAA,CAA2B;IAAA,EAAC;IAErC5B,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAIqB;IADnBD,EAAA,CAAA8B,UAAA,mBAAAS,uEAAA;MAAAvC,EAAA,CAAAgC,aAAA,CAAAQ,IAAA;MAAA,MAAAZ,SAAA,GAAA5B,EAAA,CAAAkC,aAAA,GAAAC,SAAA;MAAA,MAAAM,OAAA,GAAAzC,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAI,OAAA,CAAAC,eAAA,CAAAd,SAAA,CAAsB;IAAA,EAAC;IAEhC5B,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IACTH,EAAA,CAAAC,cAAA,iBAIsB;IADpBD,EAAA,CAAA8B,UAAA,mBAAAa,uEAAA;MAAA3C,EAAA,CAAAgC,aAAA,CAAAY,IAAA;MAAA,MAAAhB,SAAA,GAAA5B,EAAA,CAAAkC,aAAA,GAAAC,SAAA;MAAA,MAAAU,OAAA,GAAA7C,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAQ,OAAA,CAAAC,qBAAA,CAAAlB,SAAA,CAA4B;IAAA,EAAC;IAEtC5B,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IA7EfH,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAA+C,SAAA,cAG8B;IAChC/C,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,aAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,aAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,cAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAgD,UAAA,KAAAC,2CAAA,kBAGM;IACRjD,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAiB;IAGbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/FH,EAAA,CAAAC,cAAA,cAA0B;IAGtBD,EAAA,CAAAgD,UAAA,KAAAE,8CAAA,qBAMS;IACTlD,EAAA,CAAAgD,UAAA,KAAAG,8CAAA,qBAMS;IACTnD,EAAA,CAAAgD,UAAA,KAAAI,8CAAA,qBAMS;IACTpD,EAAA,CAAAC,cAAA,kBAGmB;IADjBD,EAAA,CAAA8B,UAAA,mBAAAuB,8DAAA;MAAA,MAAAC,WAAA,GAAAtD,EAAA,CAAAgC,aAAA,CAAAuB,IAAA;MAAA,MAAA3B,SAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAqB,OAAA,GAAAxD,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAmB,OAAA,CAAAC,UAAA,CAAA7B,SAAA,CAAiB;IAAA,EAAC;IAE3B5B,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGoB;IADlBD,EAAA,CAAA8B,UAAA,mBAAA4B,8DAAA;MAAA,MAAAJ,WAAA,GAAAtD,EAAA,CAAAgC,aAAA,CAAAuB,IAAA;MAAA,MAAA3B,SAAA,GAAA0B,WAAA,CAAAnB,SAAA;MAAA,MAAAwB,OAAA,GAAA3D,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAsB,OAAA,CAAAC,aAAA,CAAAhC,SAAA,CAAAC,MAAA,CAA2B;IAAA,EAAC;IAErC7B,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAtFNH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAwB,SAAA,CAAAiC,KAAA,EAAA7D,EAAA,CAAA8D,aAAA,CAAmB;IASY9D,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAW,iBAAA,CAAAiB,SAAA,CAAAd,KAAA,kBAAAc,SAAA,CAAAd,KAAA,CAAAC,QAAA,CAA2B;IAOvCf,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAW,iBAAA,CAAAiB,SAAA,CAAAX,QAAA,CAAoB;IAKpBjB,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAA+D,WAAA,SAAAnC,SAAA,CAAAoC,eAAA,gBAAgD;IAMlEhE,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAAiE,WAAA,qBAAArC,SAAA,CAAAsC,MAAA,gCAAAtC,SAAA,CAAAsC,MAAA,uCAA2H;IAE/HlE,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAgB,kBAAA,MAAAY,SAAA,CAAAsC,MAAA,MACF;IACMlE,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAwB,SAAA,CAAAsC,MAAA,CAAAC,WAAA,oBAAAvC,SAAA,CAAAsC,MAAA,CAAAC,WAAA,sBAA0F;IAS1FnE,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAwB,SAAA,CAAAwC,WAAA,CAA2B;IAC/BpE,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAgB,kBAAA,MAAAY,SAAA,CAAAwC,WAAA,MACF;IAKwBpE,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAW,iBAAA,EAAAiB,SAAA,CAAAyC,WAAA,kBAAAzC,SAAA,CAAAyC,WAAA,CAAAC,cAAA,yBAA8D;IAQjFtE,EAAA,CAAAM,SAAA,GAAmC;IAAnCN,EAAA,CAAAI,UAAA,SAAAwB,SAAA,CAAAsC,MAAA,kBAAmC;IAOnClE,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAwB,SAAA,CAAAsC,MAAA,eAAgC;IAOhClE,EAAA,CAAAM,SAAA,GAAgC;IAAhCN,EAAA,CAAAI,UAAA,SAAAwB,SAAA,CAAAsC,MAAA,eAAgC;;;;;;IAsCzDlE,EAAA,CAAAC,cAAA,aAEuC;IAChBD,EAAA,CAAA8B,UAAA,mBAAAyC,8DAAA;MAAA,MAAAjB,WAAA,GAAAtD,EAAA,CAAAgC,aAAA,CAAAwC,IAAA;MAAA,MAAAC,KAAA,GAAAnB,WAAA,CAAAoB,KAAA;MAAA,MAAAC,OAAA,GAAA3E,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAsC,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAACzE,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAA6E,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACc/E,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAW,iBAAA,CAAA8D,KAAA,KAAW;;;;;;;;;IATnEzE,EAAA,CAAAC,cAAA,cAAuE;IAG5CD,EAAA,CAAA8B,UAAA,mBAAAkD,yDAAA;MAAAhF,EAAA,CAAAgC,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAAlF,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAA6C,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAAC/E,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAgD,UAAA,IAAAmC,0CAAA,iBAIK;IAELnF,EAAA,CAAAC,cAAA,aAAwE;IACjDD,EAAA,CAAA8B,UAAA,mBAAAsD,yDAAA;MAAApF,EAAA,CAAAgC,aAAA,CAAAiD,IAAA;MAAA,MAAAI,OAAA,GAAArF,EAAA,CAAAkC,aAAA;MAAA,OAASlC,EAAA,CAAAqC,WAAA,CAAAgD,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAAC/E,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAA6E,WAAA,aAAAS,OAAA,CAAAP,WAAA,OAAoC;IAKrC/E,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAuF,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,OAAA,CAAAI,UAAA,EAA+B;IAK9B1F,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAA6E,WAAA,aAAAS,OAAA,CAAAP,WAAA,KAAAO,OAAA,CAAAI,UAAA,KAAiD;;;;;;;;ADpe3E,OAAM,MAAOC,oBAAoB;EAwEjCF,YACUG,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAxE5B,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAAjF,YAAY,GAAG;MACbkF,IAAI,EAAE,KAAK;MACXjF,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAuD,WAAW,GAAG,CAAC;IACf,KAAA0B,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBhF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACXiD,MAAM,EAAC,EAAE;MACTL,KAAK,EAAC,EAAE;MACR/C,KAAK,EAAC,IAAI;MACVkD,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxB1C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IACD,KAAA0C,cAAc,GAAO;MACrBlF,MAAM,EAAC,CAAC;MACRZ,QAAQ,EAAC,EAAE;MACXiD,MAAM,EAAC,EAAE;MACTL,KAAK,EAAC,EAAE;MACR/C,KAAK,EAAC,IAAI;MACVkD,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxB1C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IAID,KAAA2C,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdlD,eAAe,EAAC,IAAI8C,IAAI,EAAE;MAC1BK,IAAI,EAAC,IAAIzH,WAAW,EAAE;MACtB0H,UAAU,EAAC,IAAI5H,KAAK,EAAE;MACtB6H,MAAM,EAAC;KAER;IACD,KAAA1F,eAAe,GAAU,EAAE;IAC3B,KAAA2F,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAIjI,WAAW,EAAE;IACnC,KAAAkI,qBAAqB,GAAG,IAAIlI,WAAW,EAAE;IACzC,KAAAmI,SAAS,GAAG,IAAInI,WAAW,EAAE;IAwI/B,KAAAoI,cAAc,GAAW,EAAE,CAAC,CAAC;IA+J7B,KAAAC,YAAY,GAAQ,EAAE;EAjSlB;EACFC,QAAQA,CAAA;IACJ,IAAI,CAAChD,WAAW,GAAG,CAAC;IAEtB,IAAI,CAACiD,YAAY,EAAE;IACnB,IAAI,CAACpD,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAACkD,cAAc,EAAE;IAUzB;IACA,IAAI,CAACL,SAAS,CAACM,YAAY,CACxBC,IAAI,CACHxI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACsI,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAAC1C,WAAW,CAAC2C,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOxI,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA2I,SAAS,CAACxC,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IAIA;IACA,IAAI,CAAC2B,qBAAqB,CAACO,YAAY,CACpCC,IAAI,CACHxI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEqI,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAACV,qBAAqB,CAACc,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAAC9D,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACF9E,SAAS,CAACsI,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAAC1C,WAAW,CAAC+C,iBAAiB,CAAC,IAAI,CAACjC,UAAU,EAAC,IAAI,CAACiB,qBAAqB,CAACS,KAAK,EAAC,CAAC,EAAC,IAAI,CAAC3B,QAAQ,CAAC,CAAC+B,SAAS,CAAC;YAC7GI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;cAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;cAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;YACzC,CAAC;YACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAACrE,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACgB,WAAW,CAACuD,WAAW,CAACf,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAOxI,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA2I,SAAS,CAACY,KAAK,IAAG;MACjB,IAAI,CAAChD,0BAA0B,GAAGgD,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAC,kBAAkBA,CAAClC,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAAC3G,SAAS,IAAI2G,IAAI,CAAC1G,QAAQ,MAAM0G,IAAI,CAACzG,KAAK,EAAE,GAAG,EAAE;EACzE;EAGA4I,YAAYA,CAACxI,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACC,QAAQ,EAAE,GAAG,EAAE;EACzC;EAMCkH,cAAcA,CAAA;IAGb,IAAI,CAACrC,WAAW,CAAC2D,iBAAiB,EAAE,CAACf,SAAS,CAACgB,IAAI,IAAG;MACtD,IAAI,CAAC/B,YAAY,GAAG+B,IAAI;IAE1B,CAAC,CAAC;EAGA;EAYAC,oBAAoBA,CAACtC,IAAiB;IACpC,IAAI,CAACvC,eAAe,CAAC,CAAC,CAAC;EAEzB;EAQFA,eAAeA,CAAC8E,IAAY;IAC1B,IAAI,CAAC3E,WAAW,GAAG2E,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAACjD,UAAU,CAAC2B,IAAI,EAAE;IACtC,MAAMnE,MAAM,GAAG,IAAI,CAAC2D,cAAc,CAACQ,IAAI,EAAE;IAEzC,IAAIuB,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAAClC,qBAAqB,CAACS,KAAK;IAEhD,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAACxB,IAAI,EAAE;KAC1B,MAAM,IAAIwB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACvB,IAAI,EAAE;;IAGpC;IACA,IAAIuB,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAAChE,WAAW,CAAC+C,iBAAiB,CAACgB,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAACjD,QAAQ,CAAC,CAAC+B,SAAS,CAAC;QACzFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;UAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;QACzC,CAAC;QACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIzF,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAACkE,kBAAkB,CAAC,EAAE,EAAE5F,MAAM,EAAEwF,IAAI,EAAE,IAAI,CAACjD,QAAQ,CAAC,CAAC+B,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;UAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;QACzC,CAAC;QACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIzF,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAAC+C,iBAAiB,CAACgB,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAACjD,QAAQ,CAAC,CAAC+B,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;UAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;QACzC,CAAC;QACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIzF,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAACkE,kBAAkB,CAACH,OAAO,EAAEzF,MAAM,EAAEwF,IAAI,EAAE,IAAI,CAACjD,QAAQ,CAAC,CAAC+B,SAAS,CAAC;QAClFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;UAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;UAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;QACzC,CAAC;QACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAACrD,WAAW,CAACmE,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAACjD,QAAQ,CAAC,CAAC+B,SAAS,CAAC;MAChEI,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAAC5C,UAAU,GAAG4C,GAAG,CAACC,OAAO;QAC7B,IAAI,CAACpD,UAAU,GAAGmD,GAAG,CAACnD,UAAU;QAChC,IAAI,CAACqD,iBAAiB,CAAC,IAAI,CAAC9C,UAAU,CAAC;MACzC,CAAC;MACD+C,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAAC9C,UAAiB;IACzCiD,OAAO,CAACc,GAAG,CAAC/D,UAAU,CAAC;IACvBA,UAAU,CAACgE,OAAO,CAACC,EAAE,IAAG;MAEtB,IAAI,CAAC5C,OAAO,CAAC4C,EAAE,CAACrI,MAAM,CAAC,GAACqI,EAAE,CAACrI,MAAM;IAChC,CAAC,CAAC;IACFqH,OAAO,CAACc,GAAG,CAAC,IAAI,CAAC1C,OAAO,CAAC;IAC1B,IAAI,CAAC1B,WAAW,CAACuE,oBAAoB,CAAC,IAAI,CAAC7C,OAAO,CAAC,CAACkB,SAAS,CAACgB,IAAI,IAAG;MAEnEA,IAAI,CAACS,OAAO,CAACG,WAAW,IAAG;QACzB,IAAI,CAAC7C,gBAAgB,CAAC6C,WAAW,CAAChD,UAAU,CAACvF,MAAM,CAAC,GAAGuI,WAAW;QAClE,IAAI,CAACzI,eAAe,CAACyI,WAAW,CAAChD,UAAU,CAACvF,MAAM,CAAC,GAAGuI,WAAW,CAACjD,IAAI,CAAC3G,SAAS,GAAG,GAAG,GAAG4J,WAAW,CAACjD,IAAI,CAAC1G,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGE4J,cAAcA,CAAA;IAEZ,IAAI,CAACzF,eAAe,CAAC,CAAC,CAAC;EACzB;EAuBF0F,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,IAAI,CAAC3E,IAAI,CAACiF,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACnC,SAAS,CACpEuC,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBF,QAAQ,CAACC,QAAQ,EAAE;UAC3D9B,OAAO,CAACc,GAAG,CAAC,mBAAmB,EAAEiB,OAAO,CAAC;UAGzC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC;YACnBtH,KAAK,EAAEoH;WACR,CAAC;SACH,MAAM;UACL/B,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEoC,WAAWA,CAAA;IACT,IAAI,CAACtD,YAAY,GAAG,EAAE;EACxB;EAEIE,YAAYA,CAAA;IAEZ,IAAI,CAACpC,WAAW,CAACyF,WAAW,EAAE,CAAC7C,SAAS,CAACgB,IAAI,IAAG;MAChD,IAAI,CAACxD,MAAM,GAAGwD,IAAI;IAEpB,CAAC,CAAC;EACF;EAMF;EACA8B,gBAAgBA,CAAC/J,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACF,YAAY,GAAG;MAClBkF,IAAI,EAAE,IAAI;MACVjF,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACA+J,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAClK,YAAY,CAACkF,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACAiF,kBAAkBA,CAACC,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMC,OAAO,GAAG,IAAI7E,IAAI,CAAC4E,IAAI,CAAC;MAC9B,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAO/C,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAgD,QAAQA,CAACtC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAChE,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAAC8E,IAAI,CAAC;;EAE9B;EAEAuC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAClH,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAmH,YAAYA,CAAA;IACV,IAAI,IAAI,CAACnH,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAoH,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACzH,WAAW,GAAGwH,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAACjH,UAAU,GAAG,CAAC,EAAE4G,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;;;uBAjdWzG,oBAAoB,EAAA3F,EAAA,CAAA8M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAhN,EAAA,CAAA8M,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAlN,EAAA,CAAA8M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApN,EAAA,CAAA8M,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApB3H,oBAAoB;MAAA4H,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCxBjC7N,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAA+C,SAAA,cAAsB;UAEtB/C,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAA+C,SAAA,iBAAyB;UAIrB/C,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAA+C,SAAA,iBAES;UAET/C,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAA8B;UACTD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAA+C,SAAA,iBAK6C;UAE7C/C,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAA8B,UAAA,4BAAAiM,0EAAAC,MAAA;YAAA,OAAkBF,GAAA,CAAArE,oBAAA,CAAAuE,MAAA,CAAAC,MAAA,CAAA7F,KAAA,CAAyC;UAAA,EAAC;UAC5DpI,EAAA,CAAAgD,UAAA,KAAAkL,2CAAA,yBAEa;UACflO,EAAA,CAAAG,YAAA,EAAmB;UAK7BH,EAAA,CAAAC,cAAA,eAAsB;UACMD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxCH,EAAA,CAAAC,cAAA,kBAAuF;UAA3DD,EAAA,CAAA8B,UAAA,2BAAAqM,+DAAAH,MAAA;YAAA,OAAAF,GAAA,CAAAjG,cAAA,GAAAmG,MAAA;UAAA,EAA4B,oBAAAI,wDAAA;YAAA,OAAWN,GAAA,CAAAlJ,eAAA,CAAgB,CAAC,CAAC;UAAA,EAA7B;UACtD5E,EAAA,CAAAC,cAAA,kBAAiB;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1CH,EAAA,CAAAC,cAAA,kBAA2B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC9CH,EAAA,CAAAC,cAAA,kBAAwB;UAAAD,EAAA,CAAAE,MAAA,oBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxCH,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnDH,EAAA,CAAAC,cAAA,kBAA6B;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOtDH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAA8B,UAAA,2BAAAuM,8DAAAL,MAAA;YAAA,OAAAF,GAAA,CAAApH,UAAA,GAAAsH,MAAA;UAAA,EAAwB,mBAAAM,sDAAA;YAAA,OACfR,GAAA,CAAAzD,cAAA,EAAgB;UAAA,EADD;UAH1BrK,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAA+C,SAAA,gBAAiC;UACnC/C,EAAA,CAAAG,YAAA,EAAM;UAgCRH,EAAA,CAAAC,cAAA,eAAiH;UAAjDD,EAAA,CAAA8B,UAAA,mBAAAyM,oDAAAP,MAAA;YAAA,OAASF,GAAA,CAAAU,8BAAA,CAAAR,MAAA,CAAsC;UAAA,EAAC;UAC9GhO,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAA8B,UAAA,mBAAA2M,oDAAAT,MAAA;YAAA,OAASA,MAAA,CAAAU,eAAA,EAAwB;UAAA,EAAC;UAC3D1O,EAAA,CAAAC,cAAA,gBAAsD;UAAlCD,EAAA,CAAA8B,UAAA,mBAAA6M,qDAAA;YAAA,OAASb,GAAA,CAAAc,qBAAA,EAAuB;UAAA,EAAC;UAAC5O,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpEH,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAgD,UAAA,KAAA6L,oCAAA,kBAGM;UAEN7O,EAAA,CAAAC,cAAA,gBAAuE;UAAnCD,EAAA,CAAA8B,UAAA,sBAAAgN,wDAAA;YAAA,OAAYhB,GAAA,CAAAiB,mBAAA,EAAqB;UAAA,EAAC;UAE1E/O,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAA+C,SAAA,iBAK6C;UAE7C/C,EAAA,CAAAC,cAAA,gCAGyD;UAAvDD,EAAA,CAAA8B,UAAA,4BAAAkN,0EAAAhB,MAAA;YAAA,OAAkBF,GAAA,CAAAmB,cAAA,CAAAjB,MAAA,CAAAC,MAAA,CAAA7F,KAAA,CAAmC;UAAA,EAAC;UACtDpI,EAAA,CAAAgD,UAAA,KAAAkM,2CAAA,yBAEa;UACflP,EAAA,CAAAG,YAAA,EAAmB;UAIrBH,EAAA,CAAAgD,UAAA,KAAAmM,oCAAA,kBAGM;UAIAnP,EAAA,CAAAC,cAAA,iBAAoF;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,oBAKyH;UACzHD,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,iBAAwF;UAAAD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAA+C,SAAA,iBAIuG;UAGvG/C,EAAA,CAAAC,cAAA,kBAAyC;UACtCD,EAAA,CAAAE,MAAA,oCACH;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAWfH,EAAA,CAAAC,cAAA,eAAyH;UAArDD,EAAA,CAAA8B,UAAA,mBAAAsN,oDAAApB,MAAA;YAAA,OAASF,GAAA,CAAAuB,kCAAA,CAAArB,MAAA,CAA0C;UAAA,EAAC;UACtHhO,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAA8B,UAAA,mBAAAwN,oDAAAtB,MAAA;YAAA,OAASA,MAAA,CAAAU,eAAA,EAAwB;UAAA,EAAC;UAC3D1O,EAAA,CAAAC,cAAA,gBAA0D;UAAtCD,EAAA,CAAA8B,UAAA,mBAAAyN,qDAAA;YAAA,OAASzB,GAAA,CAAA0B,yBAAA,EAA2B;UAAA,EAAC;UAACxP,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACxEH,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,kCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE5EH,EAAA,CAAAgD,UAAA,KAAAyM,oCAAA,kBAGM;UAEVzP,EAAA,CAAAC,cAAA,oBAA8G;UAAxGD,EAAA,CAAA8B,UAAA,sBAAA4N,wDAAA;YAAA,OAAA5B,GAAA,CAAAjN,kBAAA,IAAkCiN,GAAA,CAAA6B,kBAAA,CAAA7B,GAAA,CAAAjN,kBAAA,CAAsC;UAAA,EAAC;UAG7Eb,EAAA,CAAAC,cAAA,0BAA0D;UAC7CD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAA+C,SAAA,iBAMU;UAEP/C,EAAA,CAAAC,cAAA,gCAGyD;UAAvDD,EAAA,CAAA8B,UAAA,4BAAA8N,0EAAA5B,MAAA;YAAA,OAAkBF,GAAA,CAAAmB,cAAA,CAAAjB,MAAA,CAAAC,MAAA,CAAA7F,KAAA,CAAmC;UAAA,EAAC;UACtDpI,EAAA,CAAAgD,UAAA,MAAA6M,4CAAA,yBAEa;UACf7P,EAAA,CAAAG,YAAA,EAAmB;UAGrBH,EAAA,CAAAgD,UAAA,MAAA8M,qCAAA,kBAEM;UAGN9P,EAAA,CAAAC,cAAA,kBAAoF;UAAAD,EAAA,CAAAE,MAAA,gCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnHH,EAAA,CAAAC,cAAA,qBAMyH;UAJvHD,EAAA,CAAA8B,UAAA,2BAAAiO,kEAAA/B,MAAA;YAAA,OAAAF,GAAA,CAAA9G,iBAAA,CAAAE,WAAA,GAAA8G,MAAA;UAAA,EAA2C;UAK7ChO,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAGXH,EAAA,CAAAC,cAAA,kBAAwF;UAAAD,EAAA,CAAAE,MAAA,uCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9HH,EAAA,CAAAC,cAAA,kBAKuG;UAFrGD,EAAA,CAAA8B,UAAA,2BAAAkO,+DAAAhC,MAAA;YAAA,OAAAF,GAAA,CAAA9G,iBAAA,CAAAhD,eAAA,GAAAgK,MAAA;UAAA,EAA+C;UAHjDhO,EAAA,CAAAG,YAAA,EAKuG;UAGvGH,EAAA,CAAA+C,SAAA,WAAM;UACN/C,EAAA,CAAAC,cAAA,mBAAyC;UACzCD,EAAA,CAAAE,MAAA,+BACA;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAkGXH,EAAA,CAAAC,cAAA,iBAAuB;UAInBD,EAAA,CAAAgD,UAAA,MAAAiN,qCAAA,kBAEM;UAGNjQ,EAAA,CAAAC,cAAA,eAA6B;UAS6BD,EAAA,CAAAE,MAAA,cAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAiD;UAAAD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,cAAO;UACLD,EAAA,CAAAgD,UAAA,MAAAkN,oCAAA,mBA4FK;UACPlQ,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAgD,UAAA,MAAAmN,qCAAA,kBAgBM;UAEJnQ,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,gBAAiB;UAGMD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;UA9czJH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAAnG,qBAAA,CAAqC,oBAAAyI,GAAA;UAMrCpQ,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAAzE,kBAAA,CAAkC;UAELrJ,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA1H,0BAAA,CAA6B;UAUxCpG,EAAA,CAAAM,SAAA,GAA4B;UAA5BN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAAjG,cAAA,CAA4B;UAiBpD7H,EAAA,CAAAM,SAAA,IAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAApH,UAAA,CAAwB;UAqCX1G,EAAA,CAAAM,SAAA,GAA4C;UAA5CN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAAxC,GAAA,CAAAyC,sBAAA,EAA4C;UAKrDvQ,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAA0N,GAAA,CAAAjN,kBAAA,CAAwB;UAKxBb,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,cAAA0N,GAAA,CAAA0C,eAAA,CAA6B;UAOnCxQ,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAApG,eAAA,CAA+B,oBAAA+I,GAAA;UAM/BzQ,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAAzE,kBAAA,CAAkC;UAELrJ,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA3H,oBAAA,CAAuB;UAOlDnG,EAAA,CAAAM,SAAA,GAAqE;UAArEN,EAAA,CAAAI,UAAA,YAAAsQ,QAAA,GAAA5C,GAAA,CAAA0C,eAAA,CAAAG,GAAA,2BAAAD,QAAA,CAAAtI,KAAA,KAAA0F,GAAA,CAAAnH,wBAAA,CAAqE;UAsCxD3G,EAAA,CAAAM,SAAA,IAAgD;UAAhDN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAqQ,eAAA,KAAAC,GAAA,EAAAxC,GAAA,CAAA8C,0BAAA,EAAgD;UAKzD5Q,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,SAAA0N,GAAA,CAAAjN,kBAAA,CAAwB;UAahCb,EAAA,CAAAM,SAAA,GAA+B;UAA/BN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAApG,eAAA,CAA+B,oBAAA+I,GAAA;UAO3BzQ,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAA0N,GAAA,CAAAzE,kBAAA,CAAkC;UAELrJ,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA3H,oBAAA,CAAuB;UAMlDnG,EAAA,CAAAM,SAAA,GAA6D;UAA7DN,EAAA,CAAAI,UAAA,UAAA0N,GAAA,CAAA9G,iBAAA,CAAAG,IAAA,IAAA2G,GAAA,CAAAlH,4BAAA,CAA6D;UAQjE5G,EAAA,CAAAM,SAAA,GAA2C;UAA3CN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA9G,iBAAA,CAAAE,WAAA,CAA2C;UAY3ClH,EAAA,CAAAM,SAAA,GAA+C;UAA/CN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA9G,iBAAA,CAAAhD,eAAA,CAA+C;UA8GzChE,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAA0N,GAAA,CAAAxM,YAAA,CAAAkF,IAAA,CAAuB;UAyBSxG,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAA0N,GAAA,CAAA7H,UAAA,CAAa;UAwGNjG,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAA0N,GAAA,CAAApI,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
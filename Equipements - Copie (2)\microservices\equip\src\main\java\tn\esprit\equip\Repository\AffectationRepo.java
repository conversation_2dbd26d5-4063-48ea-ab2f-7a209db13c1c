package tn.esprit.equip.Repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import tn.esprit.equip.Entity.Affectation;
import tn.esprit.equip.Entity.Equipement;

import java.util.List;

public interface AffectationRepo  extends JpaRepository<Affectation,Long> {

    @Query("SELECT a FROM Affectation a WHERE a.equipement.idEqui = :idEqui AND a.verrou = 'affecter'")
    Affectation findByEquipementIdEqui(@Param("idEqui") int idEqui);


    void deleteAllByEquipement(Equipement equipement);

    @Query("SELECT a FROM Affectation a WHERE a.equipement IN :equipements AND a.verrou = 'affecter'")
    List<Affectation> findByEquipementInAndCerouEqualsOne(@Param("equipements") List<Equipement> equipements);


 @Query("SELECT a.equipement From Affectation a WHERE a.verrou = 'affecter'")
    Page<Equipement> findEquipementsDSI(Pageable pageable);

}

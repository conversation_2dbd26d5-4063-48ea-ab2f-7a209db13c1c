{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MotpasseComponent } from './motpasse/motpasse.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport { WebcamModule } from 'ngx-webcam';\nimport { MarqueComponent } from './marque/marque.component';\nimport { ModelComponent } from './model/model.component';\nimport { EquipementComponent } from './equipement/equipement.component';\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\nimport { AffectaComponent } from './affecta/affecta.component';\nimport { NavebarComponent } from './navebar/navebar.component';\nimport { AgentComponent } from './agent/agent.component';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\n// Angular Material imports\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\nimport { MatOptionModule } from '@angular/material/core';\nimport { HistoriqueComponent } from './historique/historique.component';\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, HttpClientModule, FormsModule, WebcamModule, ReactiveFormsModule, BrowserAnimationsModule, MatFormFieldModule, MatInputModule, MatAutocompleteModule, MatOptionModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, DashboardComponent, UtilisateurComponent, MotpasseComponent, ResetPasswordComponent, MarqueComponent, ModelComponent, EquipementComponent, FournisseurComponent, UtilisateurEquipementComponent, AffectaComponent, NavebarComponent, HistoriqueComponent, AgentComponent, UserRegistrationComponent, EquipementsComponent],\n    imports: [BrowserModule, AppRoutingModule, HttpClientModule, FormsModule, WebcamModule, ReactiveFormsModule, BrowserAnimationsModule, MatFormFieldModule, MatInputModule, MatAutocompleteModule, MatOptionModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "DashboardComponent", "UtilisateurComponent", "HttpClientModule", "FormsModule", "ReactiveFormsModule", "MotpasseComponent", "ResetPasswordComponent", "WebcamModule", "MarqueComponent", "ModelComponent", "EquipementComponent", "FournisseurComponent", "UtilisateurEquipementComponent", "AffectaComponent", "NavebarComponent", "AgentComponent", "BrowserAnimationsModule", "MatFormFieldModule", "MatInputModule", "MatAutocompleteModule", "MatOptionModule", "HistoriqueComponent", "UserRegistrationComponent", "EquipementsComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\n\r\nimport { AppRoutingModule } from './app-routing.module';\r\nimport { AppComponent } from './app.component';\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\r\nimport { HttpClientModule } from '@angular/common/http';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MotpasseComponent } from './motpasse/motpasse.component';\r\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\r\nimport { WebcamModule } from 'ngx-webcam';\r\nimport { MarqueComponent } from './marque/marque.component';\r\nimport { ModelComponent } from './model/model.component';\r\nimport { EquipementComponent } from './equipement/equipement.component';\r\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\r\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\r\nimport { AffectaComponent } from './affecta/affecta.component';\r\nimport { NavebarComponent } from './navebar/navebar.component';\r\n\r\nimport { AgentComponent } from './agent/agent.component';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\n\r\n// Angular Material imports\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatAutocompleteModule } from '@angular/material/autocomplete';\r\nimport { MatOptionModule } from '@angular/material/core';\r\nimport { HistoriqueComponent } from './historique/historique.component';\r\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    AppComponent,\r\n    DashboardComponent,\r\n    UtilisateurComponent,\r\n    MotpasseComponent,\r\n    ResetPasswordComponent,\r\n    MarqueComponent,\r\n    ModelComponent,\r\n    EquipementComponent,\r\n    FournisseurComponent,\r\n    UtilisateurEquipementComponent,\r\n    AffectaComponent,\r\n    NavebarComponent,\r\n    HistoriqueComponent,\r\n    AgentComponent,\r\n    UserRegistrationComponent,\r\n    EquipementsComponent\r\n  ],\r\n  imports: [\r\n    BrowserModule,\r\n    AppRoutingModule,\r\n    HttpClientModule,\r\n    FormsModule,\r\n    WebcamModule,\r\n    ReactiveFormsModule,\r\n    BrowserAnimationsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatAutocompleteModule,\r\n    MatOptionModule\r\n  ],\r\n  providers: [],\r\n  bootstrap: [AppComponent]\r\n})\r\nexport class AppModule { }\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,YAAY,QAAQ,YAAY;AACzC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,8BAA8B,QAAQ,2DAA2D;AAC1G,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,uBAAuB,QAAQ,sCAAsC;AAE9E;AACA,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,oBAAoB,QAAQ,yCAAyC;;AAqC9E,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFR1B,YAAY;IAAA;EAAA;;;gBAbtBF,aAAa,EACbC,gBAAgB,EAChBI,gBAAgB,EAChBC,WAAW,EACXI,YAAY,EACZH,mBAAmB,EACnBY,uBAAuB,EACvBC,kBAAkB,EAClBC,cAAc,EACdC,qBAAqB,EACrBC,eAAe;IAAA;EAAA;;;2EAKNI,SAAS;IAAAE,YAAA,GAjClB3B,YAAY,EACZC,kBAAkB,EAClBC,oBAAoB,EACpBI,iBAAiB,EACjBC,sBAAsB,EACtBE,eAAe,EACfC,cAAc,EACdC,mBAAmB,EACnBC,oBAAoB,EACpBC,8BAA8B,EAC9BC,gBAAgB,EAChBC,gBAAgB,EAChBO,mBAAmB,EACnBN,cAAc,EACdO,yBAAyB,EACzBC,oBAAoB;IAAAI,OAAA,GAGpB9B,aAAa,EACbC,gBAAgB,EAChBI,gBAAgB,EAChBC,WAAW,EACXI,YAAY,EACZH,mBAAmB,EACnBY,uBAAuB,EACvBC,kBAAkB,EAClBC,cAAc,EACdC,qBAAqB,EACrBC,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
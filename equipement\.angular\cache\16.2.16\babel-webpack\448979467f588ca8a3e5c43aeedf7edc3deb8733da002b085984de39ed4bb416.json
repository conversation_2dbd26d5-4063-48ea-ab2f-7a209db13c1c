{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i2 from \"../../navebar/navebar.component\";\nexport class LayoutComponent {\n  constructor(utilisateurService) {\n    this.utilisateurService = utilisateurService;\n    this.navbarVisible = true;\n  }\n  ngOnInit() {\n    this.utilisateurService.currentUser$.subscribe(user => {\n      if (!user) {\n        this.utilisateurService.logout();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function LayoutComponent_Factory(t) {\n      return new (t || LayoutComponent)(i0.ɵɵdirectiveInject(i1.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LayoutComponent,\n      selectors: [[\"app-layout\"]],\n      decls: 1,\n      vars: 0,\n      template: function LayoutComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-navebar\");\n        }\n      },\n      dependencies: [i2.NavebarComponent],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LayoutComponent", "constructor", "utilisateurService", "navbarVisible", "ngOnInit", "currentUser$", "subscribe", "user", "logout", "i0", "ɵɵdirectiveInject", "i1", "UtilisateurService", "selectors", "decls", "vars", "template", "LayoutComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\Shared\\layout\\layout.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\Shared\\layout\\layout.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\n@Component({\n  selector: 'app-layout',\n  templateUrl: './layout.component.html',\n  styleUrls: ['./layout.component.css']\n})\nexport class LayoutComponent implements OnInit {\n  navbarVisible: boolean = true;\n\n  constructor(private utilisateurService: UtilisateurService) { }\n\n  ngOnInit(): void {\n\n\n  this.utilisateurService.currentUser$.subscribe(user => {\n      if (!user) {\n        this.utilisateurService.logout();\n      }\n    });\n  }\n\n\n\n}\n\n\n", "<app-navebar></app-navebar>"], "mappings": ";;;AAQA,OAAM,MAAOA,eAAe;EAG1BC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAFtC,KAAAC,aAAa,GAAY,IAAI;EAEiC;EAE9DC,QAAQA,CAAA;IAGR,IAAI,CAACF,kBAAkB,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAClD,IAAI,CAACA,IAAI,EAAE;QACT,IAAI,CAACL,kBAAkB,CAACM,MAAM,EAAE;;IAEpC,CAAC,CAAC;EACJ;;;uBAbWR,eAAe,EAAAS,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAfZ,eAAe;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR5BT,EAAA,CAAAW,SAAA,kBAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
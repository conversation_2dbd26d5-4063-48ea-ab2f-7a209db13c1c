<!doctype html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>
  
</head>

<body>
  <!--  Body Wrapper -->
  <div class="page-wrapper" id="main-wrapper" data-layout="vertical" data-navbarbg="skin6" data-sidebartype="full"
    data-sidebar-position="fixed" data-header-position="fixed">

    <!--  App Topstrip -->
    
    <!-- Sidebar Start -->
<app-layout></app-layout>
    <!--  Sidebar End -->
    <!--  Main wrapper -->
    <div class="body-wrapper">
      <!--  Header Start -->
      <header class="app-header">

      </header>
      <!--  Header End -->
      <div class="body-wrapper-inner">
        <div class="container-fluid">
                <div class="welcome-header">
  <h1>Bienvenue dans votre espace</h1>
  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>

</div>
<!-- Bouton pour ouvrir la modale -->
<
<div class="header-container">
  <div class="header-text">
    <h2>Types d'équipements</h2>
    <p>Gérez les différents types d'équipements informatiques

</p>
  </div>
<button class="add-user-btn" (click)="openModal()">
  <span class="icon">+</span>Nouveau Type

</button>
</div>
<div class="search-wrapper">
  <div class="custom-search">
    <input type="text" placeholder="Rechercher un type..." [(ngModel)]="searchText" />
    <span class="icon-search"></span>
  </div>
</div>
<!-- Modal -->
<div class="modal fade" id="updateModal" tabindex="-1" aria-labelledby="updateModalLabel"
     aria-hidden="true" data-bs-backdrop="false">

  <div class="modal-dialog modal-dialog-centered modal-lg">
    <div class="modal-content shadow rounded-4">
   
        <h5 class="modal-title" id="updateModalLabel">📝 Modifier les informations</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Fermer"></button>
   
      <div class="modal-body">
        <form>
          <!-- Nom Type -->
          <div class="mb-4">
            <label for="nomType" class="form-label fw-semibold fs-5">Nom du type</label>
            <input
              type="text"
              class="form-control"
              id="nomType"
              name="nomType"
              [(ngModel)]="newType.nomType"
              required
            />
          </div>
          <div *ngIf="signupErrors.nomType" style="color:red">{{ signupErrors.nomType }}</div>
<br>
          <!-- Description -->
          <div class="mb-4">
            <label for="description" class="form-label fw-semibold fs-5">Description </label>
            <textarea
              class="form-control"
              id="description"
              name="description"
              rows="3"
              [(ngModel)]="newType.description"

            ></textarea>
          </div>
          
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          Annuler
        </button>
        <button type="button" class="btn btn-success px-4" (click)="updateData()">
          💾 Sauvegarder
        </button>
      </div>
    </div>
  </div>
</div>

    








<!-- MODAL -->
<div class="modal" [ngClass]="{'show': isModalOpen}" (click)="closeOnOutsideClick($event)">
  <div class="modal-content" (click)="$event.stopPropagation()">
    <span class="close" (click)="closeModal()">&times;</span>
    <h3 style="font-size: 20px; margin-bottom: -10px;" >Ajouter un nouveau Type</h3>

   <form (ngSubmit)="onRegister()" #userForm="ngForm" novalidate>
<br>

  <label style="font-size: 14px;   font-weight: 500; color: #000000; margin-bottom:-40px" for="email">Nom du type</label>
<input
class="form-inputp"
  id="nomType"
  type="text"
  name="nomType"
  [(ngModel)]="newType.nomType"
  placeholder="Ex: Ordinateur portable"
  required
  
>
<div *ngIf="signupErrors.nomType" style="color:red">{{ signupErrors.nomType }}</div>
<br>
<br>

  <label style="font-size: 14px;   font-weight: 500; color: #000000;" for="email">Description</label>
<textarea
  id="description"
  name="description"
  [(ngModel)]="newType.description"
  placeholder="Description de type d'equipement"
  rows="3"
  cols="60"
  
  
></textarea>





  <button type="submit">Enregistrer</button>
</form>

  </div>
</div>




          <!--  Row 1 -->
          <div class="row">
           
    <style>
  .card {
    width: 385px;
    height: 190px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 0 0 1px #e5e7eb;
    font-family: 'Segoe UI', sans-serif;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    gap: 14px;
    margin-top: 20px;
    margin-left: 20px;
  }

  .card-icon {
    background-color: #e0edff;
    color: #2563eb;
    padding: 6px;
    border-radius: 8px;
    width: 43px;
    height: 43px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .card-icon svg {
    width: 25px;
    height: 25px;
  }

  .card-title {
    font-weight: 600;
    color: #111827;
    font-size: 20px;
    margin: 0;
  }

  .card-date {
    font-size: 15px;
    color: #9ca3af;
    margin: 2px 0 0 0;
  }

  .card-desc {
    color: #4b5563;
    margin: 0;
    font-size: 16px;
  }

  .card-badge {
    background-color: #e0edff;
    color: #0d00ff;
    padding: 4px 10px;
    border-radius: 990px;
    font-size: 12px;
  }

.card-button {
  padding: 6px 12px;
  font-size: 14px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
  color: #000000; /* Darker gray (high opacity) */
  font-weight: 500;;      /* ⬅️ makes text bold */


}


  .card-flex {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    padding: 10px;
    border-radius: 6px;
    border: 0px solid #e5e7eb;
  }
  
</style>

<!-- Simple Notification Bar -->
<div *ngIf="notification.show" class="simple-notification" [ngClass]="notification.type">
  {{ notification.message }}
</div>

<div *ngFor="let type of filterTypes()" class="card">
  <div class="card-flex">
    <div class="card-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
        <path d="M17.707 10.293l-7-7A1 1 0 0 0 10 3H4a1 1 0 0 0-1 1v6c0 .265.105.52.293.707l7 7a1 1 0 0 0 1.414 0l6-6a1 1 0 0 0 0-1.414zM6.5 7A1.5 1.5 0 1 1 9 7a1.5 1.5 0 0 1-2.5 0z"/>
      </svg>
    </div>

    <div>
      <p class="card-title">{{ type.nomType }}</p>
      <p class="card-date">Créé le 15/01/2024</p> <!-- ou {{ type.dateCreation }} si tu veux le rendre dynamique -->
    </div>
  </div>

  <p class="card-desc">{{ type.description }}</p>

  <div class="card-footer">
   <span class="card-badge">{{ type.marques.length }} marques</span>

    <button class="card-button" (click)="openModal1(type)">Modifier</button>
    <button class="card-button"(click)="confirmDelete(type.idType)">supprimer</button>
  </div>
</div>


       
          </div>
          <div class="py-6 px-6 text-center">
            <p class="mb-0 fs-4">Design and Developed by <a href="#"
                class="pe-1 text-primary text-decoration-underline">Wrappixel.com</a> Distributed by <a href="https://themewagon.com" target="_blank" >ThemeWagon</a></p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="./assets/libs/jquery/dist/jquery.min.js"></script>
  <script src="./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
  <script src="./assets/js/sidebarmenu.js"></script>
  <script src="./assets/js/app.min.js"></script>
  <script src="./assets/libs/apexcharts/dist/apexcharts.min.js"></script>
  <script src="./assets/libs/simplebar/dist/simplebar.js"></script>
  <script src="./assets/js/dashboard.js"></script>
  <!-- solar icons -->
  <script src="https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js"></script>
</body>

</html>
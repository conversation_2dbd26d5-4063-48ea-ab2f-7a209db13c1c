{"ast": null, "code": "export class Equip {}", "map": {"version": 3, "names": ["Equip"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\equipement\\equip.ts"], "sourcesContent": ["import { Fournisseur } from \"../fournisseur/Fournisseur\";\r\nimport { Model } from \"../model/Model\";\r\n\r\nexport class Equip {\r\n    idEqui!: number;\r\n    numSerie!: string;\r\n    statut!: string;\r\n  image!: string;\r\n    model!: Model|null;\r\n    dateAffectation!: Date;\r\n    description!: string;\r\n    fournisseur!:Fournisseur|null;\r\n    etat\r\n\r\n    \r\n    \r\n}"], "mappings": "AAGA,OAAM,MAAOA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { Equip } from 'src/app/equipement/equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Panne } from './Panne';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../Shared/layout/layout.component\";\nfunction EquipementsComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 80);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r9);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r9.firstName, \" \", user_r9.lastName, \" - \", user_r9.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.notification.message, \" \");\n  }\n}\nfunction EquipementsComponent_tr_74_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r10 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r11.NameUtilisateur[equip_r10.idEqui]), \" \");\n  }\n}\nfunction EquipementsComponent_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 82);\n    i0.ɵɵelement(2, \"img\", 83);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 82)(4, \"div\", 84)(5, \"h6\", 85);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 86);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 82)(10, \"span\", 87);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 82)(13, \"span\", 87);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 82)(17, \"span\", 88);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementsComponent_tr_74_div_19_Template, 3, 3, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 82)(21, \"span\", 90);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 82)(24, \"span\", 87);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 91)(27, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_74_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r14);\n      const equip_r10 = restoredCtx.$implicit;\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.openPanneModal(equip_r10));\n    });\n    i0.ɵɵelement(28, \"i\", 93);\n    i0.ɵɵtext(29, \" Panne \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const equip_r10 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r10.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r10.model == null ? null : equip_r10.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r10.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 13, equip_r10.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r10.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r10.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r10.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r10.statut.toLowerCase() === \"affecte\" || equip_r10.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", equip_r10.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r10.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((equip_r10.fournisseur == null ? null : equip_r10.fournisseur.nomFournisseur) || \"Aucun fournisseur\");\n  }\n}\nfunction EquipementsComponent_nav_75_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 97)(1, \"a\", 98);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r19);\n      const i_r17 = restoredCtx.index;\n      const ctx_r18 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r18.loadEquipements(i_r17));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r17 = ctx.index;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r17 === ctx_r15.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r17 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementsComponent_nav_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 95)(1, \"ul\", 96)(2, \"li\", 97)(3, \"a\", 98);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.loadEquipements(ctx_r20.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementsComponent_nav_75_li_5_Template, 3, 3, \"li\", 99);\n    i0.ɵɵelementStart(6, \"li\", 97)(7, \"a\", 98);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r22.loadEquipements(ctx_r22.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r4.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages - 1);\n  }\n}\nfunction EquipementsComponent_div_124_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getErrorMessage(\"titre\"), \" \");\n  }\n}\nfunction EquipementsComponent_div_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r6.getErrorMessage(\"priorite\"), \" \");\n  }\n}\nfunction EquipementsComponent_div_144_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r7.getErrorMessage(\"description\"), \" \");\n  }\n}\nfunction EquipementsComponent_div_155_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 101);\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"show\", ctx_r8.showPanneModal);\n  }\n}\nexport class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    // Propriétés pour les pannes\n    this.pannes = [];\n    this.selectedPanne = new Panne();\n    this.showPanneModal = false;\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.signupErrors = {};\n    // Initialisation du formulaire de panne\n    this.panneForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: ['', [Validators.required, Validators.minLength(10)]],\n      priorite: ['MOYENNE', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    // Construire correctement le tableau d'IDs\n    this.idsEqui = equiements.map(eq => eq.idEqui);\n    console.log('IDs des équipements:', this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // ==================== GESTION DES PANNES ====================\n  /**\n   * Ouvrir le modal pour déclarer une panne\n   */\n  openPanneModal(equipement) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n  onSubmitPanne() {\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement\n      };\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: response => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          // Optionnel: recharger la liste des équipements ou afficher un message de succès\n          this.loadEquipements(this.currentPage);\n        },\n        error: error => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n          // Afficher un message d'erreur à l'utilisateur\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.panneForm.controls).forEach(key => {\n        this.panneForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  /**\n   * Vérifier si un champ du formulaire a une erreur\n   */\n  hasError(fieldName, errorType) {\n    const field = this.panneForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && field.touched);\n  }\n  /**\n   * Obtenir le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.panneForm.get(fieldName);\n    if (field && field.touched && field.errors) {\n      if (field.errors['required']) {\n        return `Le ${fieldName} est requis`;\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `Le ${fieldName} doit contenir au moins ${requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function EquipementsComponent_Factory(t) {\n      return new (t || EquipementsComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementsComponent,\n      selectors: [[\"app-equipements\"]],\n      decls: 156,\n      vars: 32,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [\"tabindex\", \"-1\", \"role\", \"dialog\", \"aria-labelledby\", \"panneModalLabel\", \"aria-hidden\", \"true\", 1, \"modal\", \"fade\"], [\"role\", \"document\", 1, \"modal-dialog\", \"modal-lg\"], [1, \"modal-content\"], [1, \"modal-header\", \"bg-danger\", \"text-white\"], [\"id\", \"panneModalLabel\", 1, \"modal-title\"], [1, \"ti\", \"ti-alert-triangle\", \"me-2\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"btn-close\", \"btn-close-white\", 3, \"click\"], [3, \"formGroup\", \"ngSubmit\"], [1, \"modal-body\"], [1, \"alert\", \"alert-info\", \"mb-4\"], [1, \"alert-heading\"], [1, \"ti\", \"ti-info-circle\", \"me-2\"], [1, \"badge\", \"bg-success\", \"ms-1\"], [1, \"col-12\", \"mb-3\"], [\"for\", \"titre\", 1, \"form-label\"], [1, \"ti\", \"ti-edit\", \"me-1\"], [\"type\", \"text\", \"id\", \"titre\", \"formControlName\", \"titre\", \"placeholder\", \"Ex: \\u00C9cran ne s'allume plus, Clavier d\\u00E9faillant...\", 1, \"form-control\"], [\"class\", \"invalid-feedback\", 4, \"ngIf\"], [1, \"col-md-6\", \"mb-3\"], [\"for\", \"priorite\", 1, \"form-label\"], [1, \"ti\", \"ti-flag\", \"me-1\"], [\"id\", \"priorite\", \"formControlName\", \"priorite\", 1, \"form-select\"], [\"value\", \"FAIBLE\"], [\"value\", \"MOYENNE\"], [\"value\", \"HAUTE\"], [\"value\", \"CRITIQUE\"], [\"for\", \"description\", 1, \"form-label\"], [1, \"ti\", \"ti-file-text\", \"me-1\"], [\"id\", \"description\", \"formControlName\", \"description\", \"rows\", \"4\", \"placeholder\", \"D\\u00E9crivez pr\\u00E9cis\\u00E9ment le probl\\u00E8me rencontr\\u00E9, les circonstances, les messages d'erreur \\u00E9ventuels...\", 1, \"form-control\"], [1, \"form-text\"], [1, \"ti\", \"ti-info-circle\", \"me-1\"], [1, \"modal-footer\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", 3, \"click\"], [1, \"ti\", \"ti-x\", \"me-1\"], [\"type\", \"submit\", 1, \"btn\", \"btn-danger\", 3, \"disabled\"], [1, \"ti\", \"ti-alert-triangle\", \"me-1\"], [\"class\", \"modal-backdrop fade\", 3, \"show\", 4, \"ngIf\"], [3, \"value\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"fw-normal\", \"text-truncate\", 2, \"max-width\", \"150px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-1\", \"text-end\"], [\"title\", \"D\\u00E9clarer une panne\", 1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"ti\", \"ti-alert-triangle\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"invalid-feedback\"], [1, \"modal-backdrop\", \"fade\"]],\n      template: function EquipementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel Panne \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementsComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 24)(43, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementsComponent_Template_input_input_43_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"span\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"body\", 27)(46, \"div\", 28);\n          i0.ɵɵtemplate(47, EquipementsComponent_div_47_Template, 2, 2, \"div\", 29);\n          i0.ɵɵelementStart(48, \"div\", 7)(49, \"div\", 30)(50, \"div\", 31)(51, \"div\", 32)(52, \"div\", 14)(53, \"div\", 33)(54, \"table\", 34)(55, \"thead\")(56, \"tr\")(57, \"th\", 35);\n          i0.ɵɵtext(58, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 35);\n          i0.ɵɵtext(60, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 35);\n          i0.ɵɵtext(62, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 35);\n          i0.ɵɵtext(64, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 35);\n          i0.ɵɵtext(66, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 35);\n          i0.ɵɵtext(68, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 35);\n          i0.ɵɵtext(70, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 36);\n          i0.ɵɵtext(72, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"tbody\");\n          i0.ɵɵtemplate(74, EquipementsComponent_tr_74_Template, 30, 16, \"tr\", 37);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(75, EquipementsComponent_nav_75_Template, 9, 6, \"nav\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 30)(77, \"div\", 39)(78, \"p\", 40);\n          i0.ɵɵtext(79, \"Design and Developed by \");\n          i0.ɵɵelementStart(80, \"a\", 41);\n          i0.ɵɵtext(81, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(82, \" Distributed by \");\n          i0.ɵɵelementStart(83, \"a\", 42);\n          i0.ɵɵtext(84, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(85, \"div\", 43)(86, \"div\", 44)(87, \"div\", 45)(88, \"div\", 46)(89, \"h5\", 47);\n          i0.ɵɵelement(90, \"i\", 48);\n          i0.ɵɵtext(91);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"button\", 49);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_button_click_92_listener() {\n            return ctx.closePanneModal();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"form\", 50);\n          i0.ɵɵlistener(\"ngSubmit\", function EquipementsComponent_Template_form_ngSubmit_93_listener() {\n            return ctx.onSubmitPanne();\n          });\n          i0.ɵɵelementStart(94, \"div\", 51)(95, \"div\", 52)(96, \"h6\", 53);\n          i0.ɵɵelement(97, \"i\", 54);\n          i0.ɵɵtext(98, \"\\u00C9quipement concern\\u00E9 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"div\", 30)(100, \"div\", 17)(101, \"strong\");\n          i0.ɵɵtext(102, \"Mod\\u00E8le:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(103);\n          i0.ɵɵelement(104, \"br\");\n          i0.ɵɵelementStart(105, \"strong\");\n          i0.ɵɵtext(106, \"N\\u00B0 S\\u00E9rie:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(107);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 17)(109, \"strong\");\n          i0.ɵɵtext(110, \"Date d'affectation:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(111);\n          i0.ɵɵpipe(112, \"date\");\n          i0.ɵɵelement(113, \"br\");\n          i0.ɵɵelementStart(114, \"strong\");\n          i0.ɵɵtext(115, \"Statut actuel:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"span\", 55);\n          i0.ɵɵtext(117);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(118, \"div\", 30)(119, \"div\", 56)(120, \"label\", 57);\n          i0.ɵɵelement(121, \"i\", 58);\n          i0.ɵɵtext(122, \"Titre de la panne * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(123, \"input\", 59);\n          i0.ɵɵtemplate(124, EquipementsComponent_div_124_Template, 2, 1, \"div\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(125, \"div\", 61)(126, \"label\", 62);\n          i0.ɵɵelement(127, \"i\", 63);\n          i0.ɵɵtext(128, \"Priorit\\u00E9 * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(129, \"select\", 64)(130, \"option\", 65);\n          i0.ɵɵtext(131, \"\\uD83D\\uDFE2 Faible - Peut attendre\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"option\", 66);\n          i0.ɵɵtext(133, \"\\uD83D\\uDFE1 Moyenne - \\u00C0 traiter rapidement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(134, \"option\", 67);\n          i0.ɵɵtext(135, \"\\uD83D\\uDD34 Haute - Urgent\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"option\", 68);\n          i0.ɵɵtext(137, \"\\u26A0\\uFE0F Critique - Bloquant\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(138, EquipementsComponent_div_138_Template, 2, 1, \"div\", 60);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(139, \"div\", 56)(140, \"label\", 69);\n          i0.ɵɵelement(141, \"i\", 70);\n          i0.ɵɵtext(142, \"Description d\\u00E9taill\\u00E9e * \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(143, \"textarea\", 71);\n          i0.ɵɵtemplate(144, EquipementsComponent_div_144_Template, 2, 1, \"div\", 60);\n          i0.ɵɵelementStart(145, \"div\", 72);\n          i0.ɵɵelement(146, \"i\", 73);\n          i0.ɵɵtext(147, \" Plus la description est pr\\u00E9cise, plus la r\\u00E9solution sera rapide. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(148, \"div\", 74)(149, \"button\", 75);\n          i0.ɵɵlistener(\"click\", function EquipementsComponent_Template_button_click_149_listener() {\n            return ctx.closePanneModal();\n          });\n          i0.ɵɵelement(150, \"i\", 76);\n          i0.ɵɵtext(151, \"Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"button\", 77);\n          i0.ɵɵelement(153, \"i\", 78);\n          i0.ɵɵtext(154, \"D\\u00E9clarer la panne \");\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(155, EquipementsComponent_div_155_Template, 1, 2, \"div\", 79);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n          i0.ɵɵadvance(10);\n          i0.ɵɵstyleProp(\"display\", ctx.showPanneModal ? \"block\" : \"none\");\n          i0.ɵɵclassProp(\"show\", ctx.showPanneModal);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" D\\u00E9clarer une panne - \", ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.model == null ? null : ctx.selectedPanne.equipement.model.nomModel, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.panneForm);\n          i0.ɵɵadvance(10);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.model == null ? null : ctx.selectedPanne.equipement.model.nomModel, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.numSerie, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(112, 29, ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.dateAffectation, \"dd/MM/yyyy\"), \"\");\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(ctx.selectedPanne.equipement == null ? null : ctx.selectedPanne.equipement.statut);\n          i0.ɵɵadvance(6);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.hasError(\"titre\", \"required\") || ctx.hasError(\"titre\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"titre\", \"required\") || ctx.hasError(\"titre\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.hasError(\"priorite\", \"required\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"priorite\", \"required\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"is-invalid\", ctx.hasError(\"description\", \"required\") || ctx.hasError(\"description\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"description\", \"required\") || ctx.hasError(\"description\", \"minlength\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !ctx.panneForm.valid);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPanneModal);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.FormControlDirective, i3.FormGroupDirective, i3.FormControlName, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r9", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "ctx_r2", "notification", "type", "ɵɵtextInterpolate1", "message", "ɵɵpipeBind1", "ctx_r11", "NameUtilisateur", "equip_r10", "idEqui", "ɵɵelement", "ɵɵtemplate", "EquipementsComponent_tr_74_div_19_Template", "ɵɵlistener", "EquipementsComponent_tr_74_Template_button_click_27_listener", "restoredCtx", "ɵɵrestoreView", "_r14", "$implicit", "ctx_r13", "ɵɵnextContext", "ɵɵresetView", "openPanneModal", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "model", "nomModel", "numSerie", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "description", "<PERSON><PERSON><PERSON><PERSON>", "nomFournisseur", "EquipementsComponent_nav_75_li_5_Template_a_click_1_listener", "_r19", "i_r17", "index", "ctx_r18", "loadEquipements", "ɵɵclassProp", "ctx_r15", "currentPage", "EquipementsComponent_nav_75_Template_a_click_3_listener", "_r21", "ctx_r20", "EquipementsComponent_nav_75_li_5_Template", "EquipementsComponent_nav_75_Template_a_click_7_listener", "ctx_r22", "ctx_r4", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "ctx_r5", "getErrorMessage", "ctx_r6", "ctx_r7", "ctx_r8", "showPanneModal", "EquipementsComponent", "authservice", "http", "fb", "utilisateurService", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "pannes", "<PERSON><PERSON><PERSON>", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "signupErrors", "panneForm", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "priorite", "ngOnInit", "GetAllModels", "getFournisseur", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "searchUsers", "users", "displayUtilisateur", "displayModel", "getallFournisseur", "data", "onUserSearchSelected", "page", "keyword", "username", "userVal", "searchEquipements1", "getDSIEquipements", "log", "map", "eq", "getAffectationsByIds", "for<PERSON>ach", "affectation", "onSearchChange", "onFileSelected", "event", "file", "target", "files", "formData", "FormData", "append", "post", "response", "imageUrl", "fullUrl", "form", "patchValue", "resetErrors", "getAllModel", "showNotification", "setTimeout", "hideNotification", "formatDateForInput", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "reset", "closePanneModal", "onSubmitPanne", "valid", "panneData", "get", "declarer<PERSON><PERSON>", "Object", "keys", "controls", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "errors", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementsComponent_Template", "rf", "ctx", "EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementsComponent_mat_option_40_Template", "EquipementsComponent_Template_input_ngModelChange_43_listener", "EquipementsComponent_Template_input_input_43_listener", "EquipementsComponent_div_47_Template", "EquipementsComponent_tr_74_Template", "EquipementsComponent_nav_75_Template", "EquipementsComponent_Template_button_click_92_listener", "EquipementsComponent_Template_form_ngSubmit_93_listener", "EquipementsComponent_div_124_Template", "EquipementsComponent_div_138_Template", "EquipementsComponent_div_144_Template", "EquipementsComponent_Template_button_click_149_listener", "EquipementsComponent_div_155_Template", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.html"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\nimport { Panne } from './Panne';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\n\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\n// Propriétés pour les pannes\npannes: Panne[] = [];\nselectedPanne: Panne = new Panne();\nshowPanneModal: boolean = false;\npanneForm: FormGroup;\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[];\n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) {\n  // Initialisation du formulaire de panne\n  this.panneForm = this.fb.group({\n    titre: ['', [Validators.required, Validators.minLength(3)]],\n    description: ['', [Validators.required, Validators.minLength(10)]],\n    priorite: ['MOYENNE', [Validators.required]]\n  });\n}\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\n\n\n\n\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\n\n\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\n\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n\n\n\n\n\n\n\n\n \n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n\n\n\n\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n\n  // Construire correctement le tableau d'IDs\n  this.idsEqui = equiements.map(eq => eq.idEqui);\n  console.log('IDs des équipements:', this.idsEqui);\n\n  this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n  \n\n\n\n\n\n   \n\n\n\n\n\n\n\n    \n\n\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n \n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n  // ==================== GESTION DES PANNES ====================\n\n  /**\n   * Ouvrir le modal pour déclarer une panne\n   */\n  openPanneModal(equipement: Equip) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n\n\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n\n\n  onSubmitPanne() {\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement\n      };\n\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: (response) => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          // Optionnel: recharger la liste des équipements ou afficher un message de succès\n          this.loadEquipements(this.currentPage);\n        }\n        error: (error) => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n          // Afficher un message d'erreur à l'utilisateur\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.panneForm.controls).forEach(key => {\n        this.panneForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Vérifier si un champ du formulaire a une erreur\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.panneForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && field.touched);\n  }\n\n  /**\n   * Obtenir le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.panneForm.get(fieldName);\n    if (field && field.touched && field.errors) {\n      if (field.errors['required']) {\n        return `Le ${fieldName} est requis`;\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `Le ${fieldName} doit contenir au moins ${requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n\n}\n\n  \n\n\n\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Équipements</h2>\n    <p>Gérez les différents types d'équipements informatiques\n\n</p>\n  </div>\n<button class=\"add-user-btn\" >\n  <span class=\"icon\">+</span>Nouvel Panne \n\n</button>\n</div>\n\n<!-- Formulaire de recherche simple -->\n<div class=\"card mt-3 mb-4\">\n  <div class=\"card-body\">\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\n\n    <div class=\"row g-3\">\n      <!-- Recherche par utilisateur -->\n      <div class=\"col-md-6\">\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n          <mat-label>Utilisateur</mat-label>\n          <input\n            type=\"text\"\n            matInput\n            [formControl]=\"utilisateurSearchCtrl\"\n            [matAutocomplete]=\"autoUserSearch\"\n            placeholder=\"Rechercher un utilisateur...\">\n\n          <mat-autocomplete\n            #autoUserSearch=\"matAutocomplete\"\n            [displayWith]=\"displayUtilisateur\"\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n            </mat-option>\n          </mat-autocomplete>\n        </mat-form-field>\n      </div>\n\n      <!-- Recherche par statut -->\n\n</div>\n  </div>\n</div>\n\n<div class=\"search-wrapper\">\n  <div class=\"custom-search\">\n    <input\n      type=\"text\"\n      placeholder=\"Rechercher un equipement...\"\n      [(ngModel)]=\"searchTerm\"\n      (input)=\"onSearchChange()\"\n      class=\"form-control\"\n    />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n\n</div>\n<!-- Modal -->\n\n<!-- Modal de modification -->\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<style>\n    .card-custom {\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    }\n\n    .btn-outline-lightblue {\n      border: 1px solid #cfe2ff;\n      color: #0d6efd;\n      background-color: #e7f1ff;\n    }\n\n    .tag {\n      background-color: #e7f1ff;\n      color: #0d6efd;\n      padding: 3px 10px;\n      font-size: 0.8rem;\n      border-radius: 15px;\n      position: absolute;\n      right: 20px;\n      top: 20px;\n    }\n\n.icon-box {\n  font-size: 48px; /* optional - for icon size */\n  width: 100px;     /* increase width */\n  height: 100px;    /* set height */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #0d6efd;\n  margin-right: 10px;\n border-radius: 0% !important;\n}\n\n    .btn-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n.card-custom {\n  border-radius: 12px;\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\n  background-color: #fff;\n  color: #212529; /* Darker text */\n  font-size: 0.95rem; /* Slightly larger base font */\n}\n\n.card-custom strong {\n  font-weight: 600; /* Heavier for labels */\n  color: #1a1a1a;\n}\n\n.card-custom h5 {\n  font-weight: 600;\n  color: #000;\n}\n\n.card-custom small,\n.text-muted {\n  color: #495057 !important; /* Less faded gray */\n}\n\n.icon-box {\n  font-size: 32px;\n  color: #0d6efd;\n  margin-right: 10px;\n}\n\n.tag {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n  padding: 3px 10px;\n  font-size: 0.8rem;\n  border-radius: 15px;\n  position: absolute;\n  right: 20px;\n  top: 20px;\n}\n\n\n\n  </style>\n\n<body class=\"bg-light\">\n  <div class=\"container my-2\">\n\n    <!-- Simple Notification Bar -->\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\n      {{ notification.message }}\n    </div>\n\n    <!-- Tableau des équipements -->\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <div class=\"table-responsive mt-1\">\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Description</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Fournisseur</th>\n                      <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr *ngFor=\"let equip of equiements\">\n                      <!-- Image -->\n                      <td class=\"px-1\">\n                        <img [src]=\"equip.image\"\n                             alt=\"Équipement\"\n                             class=\"rounded-circle img-fluid\"\n                             width=\"40\" height=\"40\" />\n                      </td>\n\n                      <!-- Modèle -->\n                      <td class=\"px-1\">\n                        <div class=\"ms-3\">\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\n                          <span class=\"fw-normal text-muted\">Modèle</span>\n                        </div>\n                      </td>\n\n                      <!-- Numéro de série -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\n                      </td>\n\n                      <!-- Date d'acquisition -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\n                      </td>\n\n                      <!-- Statut -->\n                      <td class=\"px-1\">\n                        <span class=\"badge rounded-pill\"\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\n                              [style.color]=\"'white'\">\n                          {{ equip.statut }}\n                        </span>\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\n                             class=\"text-muted small mt-1\">\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\n                        </div>\n                      </td>\n\n                      <!-- Description -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal text-truncate\" style=\"max-width: 150px; display: inline-block;\"\n                              [title]=\"equip.description\">\n                          {{ equip.description }}\n                        </span>\n                      </td>\n\n                      <!-- Fournisseur -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>\n                      </td>\n\n                      <!-- Actions -->\n                      <td class=\"px-1 text-end\">\n                        <button class=\"btn btn-danger btn-sm\"\n                                (click)=\"openPanneModal(equip)\"\n                                title=\"Déclarer une panne\">\n                          <i class=\"ti ti-alert-triangle\"></i> Panne\n                        </button>\n                      </td>\n\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n    <!-- Pagination Bootstrap -->\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\n  <ul class=\"pagination\">\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\n    </li>\n\n    <li class=\"page-item\"\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\n        [class.active]=\"i === currentPage\">\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\n    </li>\n\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\n    </li>\n  </ul>\n</nav>\n\n  </div>\n</body>\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            \n          <div class=\"py-6 px-6 text-center\">\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modal de déclaration de panne -->\n  <div class=\"modal fade\" [class.show]=\"showPanneModal\" [style.display]=\"showPanneModal ? 'block' : 'none'\"\n       tabindex=\"-1\" role=\"dialog\" aria-labelledby=\"panneModalLabel\" aria-hidden=\"true\">\n    <div class=\"modal-dialog modal-lg\" role=\"document\">\n      <div class=\"modal-content\">\n        <div class=\"modal-header bg-danger text-white\">\n          <h5 class=\"modal-title\" id=\"panneModalLabel\">\n            <i class=\"ti ti-alert-triangle me-2\"></i>\n            Déclarer une panne - {{ selectedPanne.equipement?.model?.nomModel }}\n          </h5>\n          <button type=\"button\" class=\"btn-close btn-close-white\" (click)=\"closePanneModal()\" aria-label=\"Close\"></button>\n        </div>\n\n        <form [formGroup]=\"panneForm\" (ngSubmit)=\"onSubmitPanne()\">\n          <div class=\"modal-body\">\n            <!-- Informations de l'équipement -->\n            <div class=\"alert alert-info mb-4\">\n              <h6 class=\"alert-heading\">\n                <i class=\"ti ti-info-circle me-2\"></i>Équipement concerné\n              </h6>\n              <div class=\"row\">\n                <div class=\"col-md-6\">\n                  <strong>Modèle:</strong> {{ selectedPanne.equipement?.model?.nomModel }}<br>\n                  <strong>N° Série:</strong> {{ selectedPanne.equipement?.numSerie }}\n                </div>\n                <div class=\"col-md-6\">\n                  <strong>Date d'affectation:</strong> {{ selectedPanne.equipement?.dateAffectation | date:'dd/MM/yyyy' }}<br>\n                  <strong>Statut actuel:</strong>\n                  <span class=\"badge bg-success ms-1\">{{ selectedPanne.equipement?.statut }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- Formulaire de déclaration -->\n            <div class=\"row\">\n              <!-- Titre de la panne -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"titre\" class=\"form-label\">\n                  <i class=\"ti ti-edit me-1\"></i>Titre de la panne *\n                </label>\n                <input type=\"text\"\n                       class=\"form-control\"\n                       id=\"titre\"\n                       formControlName=\"titre\"\n                       [class.is-invalid]=\"hasError('titre', 'required') || hasError('titre', 'minlength')\"\n                       placeholder=\"Ex: Écran ne s'allume plus, Clavier défaillant...\">\n                <div class=\"invalid-feedback\" *ngIf=\"hasError('titre', 'required') || hasError('titre', 'minlength')\">\n                  {{ getErrorMessage('titre') }}\n                </div>\n              </div>\n\n              <!-- Priorité -->\n              <div class=\"col-md-6 mb-3\">\n                <label for=\"priorite\" class=\"form-label\">\n                  <i class=\"ti ti-flag me-1\"></i>Priorité *\n                </label>\n                <select class=\"form-select\"\n                        id=\"priorite\"\n                        formControlName=\"priorite\"\n                        [class.is-invalid]=\"hasError('priorite', 'required')\">\n                  <option value=\"FAIBLE\">🟢 Faible - Peut attendre</option>\n                  <option value=\"MOYENNE\">🟡 Moyenne - À traiter rapidement</option>\n                  <option value=\"HAUTE\">🔴 Haute - Urgent</option>\n                  <option value=\"CRITIQUE\">⚠️ Critique - Bloquant</option>\n                </select>\n                <div class=\"invalid-feedback\" *ngIf=\"hasError('priorite', 'required')\">\n                  {{ getErrorMessage('priorite') }}\n                </div>\n              </div>\n\n              <!-- Description -->\n              <div class=\"col-12 mb-3\">\n                <label for=\"description\" class=\"form-label\">\n                  <i class=\"ti ti-file-text me-1\"></i>Description détaillée *\n                </label>\n                <textarea class=\"form-control\"\n                          id=\"description\"\n                          formControlName=\"description\"\n                          rows=\"4\"\n                          [class.is-invalid]=\"hasError('description', 'required') || hasError('description', 'minlength')\"\n                          placeholder=\"Décrivez précisément le problème rencontré, les circonstances, les messages d'erreur éventuels...\"></textarea>\n                <div class=\"invalid-feedback\" *ngIf=\"hasError('description', 'required') || hasError('description', 'minlength')\">\n                  {{ getErrorMessage('description') }}\n                </div>\n                <div class=\"form-text\">\n                  <i class=\"ti ti-info-circle me-1\"></i>\n                  Plus la description est précise, plus la résolution sera rapide.\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"modal-footer\">\n            <button type=\"button\" class=\"btn btn-secondary\" (click)=\"closePanneModal()\">\n              <i class=\"ti ti-x me-1\"></i>Annuler\n            </button>\n            <button type=\"submit\"\n                    class=\"btn btn-danger\"\n                    [disabled]=\"!panneForm.valid\">\n              <i class=\"ti ti-alert-triangle me-1\"></i>Déclarer la panne\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  </div>\n\n  <!-- Backdrop pour le modal -->\n  <div class=\"modal-backdrop fade\" [class.show]=\"showPanneModal\" *ngIf=\"showPanneModal\"></div>\n\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\n  <script src=\"./assets/js/app.min.js\"></script>\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\n  <script src=\"./assets/js/dashboard.js\"></script>\n  <!-- solar icons -->\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\n</body>\n\n</html>"], "mappings": "AAEA,SAASA,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAG7E,SAASC,KAAK,QAAQ,SAAS;;;;;;;;;;;;;;ICsDnBC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,SAAA,OAAAH,OAAA,CAAAI,QAAA,SAAAJ,OAAA,CAAAK,KAAA,MACF;;;;;IA2JRV,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFb,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAH,MAAA,CAAAC,YAAA,CAAAG,OAAA,MACF;;;;;IAyDoBf,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,0BAAAd,EAAA,CAAAgB,WAAA,OAAAC,OAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,MAAA,QACF;;;;;;IArCJpB,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAAqB,SAAA,cAG8B;IAChCrB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,aAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,aAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,cAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAsB,UAAA,KAAAC,0CAAA,kBAGM;IACRvB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAiB;IAGbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/FH,EAAA,CAAAC,cAAA,cAA0B;IAEhBD,EAAA,CAAAwB,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAT,SAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAF,OAAA,CAAAG,cAAA,CAAAd,SAAA,CAAqB;IAAA,EAAC;IAErCnB,EAAA,CAAAqB,SAAA,aAAoC;IAACrB,EAAA,CAAAE,MAAA,eACvC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxDJH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAe,SAAA,CAAAe,KAAA,EAAAlC,EAAA,CAAAmC,aAAA,CAAmB;IASYnC,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAoC,iBAAA,CAAAjB,SAAA,CAAAkB,KAAA,kBAAAlB,SAAA,CAAAkB,KAAA,CAAAC,QAAA,CAA2B;IAOvCtC,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAoC,iBAAA,CAAAjB,SAAA,CAAAoB,QAAA,CAAoB;IAKpBvC,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAAwC,WAAA,SAAArB,SAAA,CAAAsB,eAAA,gBAAgD;IAMlEzC,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAA0C,WAAA,qBAAAvB,SAAA,CAAAwB,MAAA,gCAAAxB,SAAA,CAAAwB,MAAA,uCAA2H;IAE/H3C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAK,SAAA,CAAAwB,MAAA,MACF;IACM3C,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAe,SAAA,CAAAwB,MAAA,CAAAC,WAAA,oBAAAzB,SAAA,CAAAwB,MAAA,CAAAC,WAAA,sBAA0F;IAS1F5C,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAe,SAAA,CAAA0B,WAAA,CAA2B;IAC/B7C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAK,SAAA,CAAA0B,WAAA,MACF;IAKwB7C,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAoC,iBAAA,EAAAjB,SAAA,CAAA2B,WAAA,kBAAA3B,SAAA,CAAA2B,WAAA,CAAAC,cAAA,yBAA8D;;;;;;IA8B1G/C,EAAA,CAAAC,cAAA,aAEuC;IAChBD,EAAA,CAAAwB,UAAA,mBAAAwB,6DAAA;MAAA,MAAAtB,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAsB,IAAA;MAAA,MAAAC,KAAA,GAAAxB,WAAA,CAAAyB,KAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAoB,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAAClD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAAsD,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACcxD,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAoC,iBAAA,CAAAc,KAAA,KAAW;;;;;;;;;IATnElD,EAAA,CAAAC,cAAA,cAAuE;IAG5CD,EAAA,CAAAwB,UAAA,mBAAAiC,wDAAA;MAAAzD,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA2B,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAsB,UAAA,IAAAsC,yCAAA,iBAIK;IAEL5D,EAAA,CAAAC,cAAA,aAAwE;IACjDD,EAAA,CAAAwB,UAAA,mBAAAqC,wDAAA;MAAA7D,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAI,OAAA,GAAA9D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA8B,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,OAAoC;IAKrCxD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,MAAA,CAAAI,UAAA,EAA+B;IAK9BnE,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAI,UAAA,KAAiD;;;;;IAqE3DnE,EAAA,CAAAC,cAAA,eAAsG;IACpGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAsD,MAAA,CAAAC,eAAA,eACF;;;;;IAiBArE,EAAA,CAAAC,cAAA,eAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAwD,MAAA,CAAAD,eAAA,kBACF;;;;;IAcArE,EAAA,CAAAC,cAAA,eAAkH;IAChHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAyD,MAAA,CAAAF,eAAA,qBACF;;;;;IAyBdrE,EAAA,CAAAqB,SAAA,eAA4F;;;;IAA3DrB,EAAA,CAAAsD,WAAA,SAAAkB,MAAA,CAAAC,cAAA,CAA6B;;;AD5bhE,OAAM,MAAOC,oBAAoB;EA8EjCR,YACUS,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA9E5B,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAA1E,YAAY,GAAG;MACb2E,IAAI,EAAE,KAAK;MACX1E,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;IACD,KAAAyC,WAAW,GAAG,CAAC;IACf,KAAAgC,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBxE,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAIoD,IAAI,CAAJ,CAAI;MACxBhD,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IACD,KAAAgD,cAAc,GAAO;MACrB1E,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAIoD,IAAI,CAAJ,CAAI;MACxBhD,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IAID,KAAAiD,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdxD,eAAe,EAAC,IAAIoD,IAAI,EAAE;MAC1BK,IAAI,EAAC,IAAIzG,WAAW,EAAE;MACtB0G,UAAU,EAAC,IAAI7G,KAAK,EAAE;MACtB8G,MAAM,EAAC;KAER;IACD,KAAAlF,eAAe,GAAU,EAAE;IAC3B,KAAAmF,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B;IACA,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,aAAa,GAAU,IAAIzG,KAAK,EAAE;IAClC,KAAA0E,cAAc,GAAY,KAAK;IAG/B,KAAAgC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAIpH,WAAW,EAAE;IACnC,KAAAqH,qBAAqB,GAAG,IAAIrH,WAAW,EAAE;IACzC,KAAAsH,SAAS,GAAG,IAAItH,WAAW,EAAE;IA+I/B,KAAAuH,cAAc,GAAW,EAAE,CAAC,CAAC;IA+J7B,KAAAC,YAAY,GAAQ,EAAE;IAvSpB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACnC,EAAE,CAACoC,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC1H,UAAU,CAAC2H,QAAQ,EAAE3H,UAAU,CAAC4H,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DvE,WAAW,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAAC2H,QAAQ,EAAE3H,UAAU,CAAC4H,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClEC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAAC7H,UAAU,CAAC2H,QAAQ,CAAC;KAC5C,CAAC;EACJ;EACEG,QAAQA,CAAA;IACJ,IAAI,CAAC9D,WAAW,GAAG,CAAC;IAEtB,IAAI,CAAC+D,YAAY,EAAE;IACnB,IAAI,CAAClE,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAACgE,cAAc,EAAE;IAUzB;IACA,IAAI,CAACX,SAAS,CAACY,YAAY,CACxBC,IAAI,CACHhI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAAC8H,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAAClD,WAAW,CAACmD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAOhI,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmI,SAAS,CAAChD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IAIA;IACA,IAAI,CAAC6B,qBAAqB,CAACa,YAAY,CACpCC,IAAI,CACHhI,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAE6H,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAAChB,qBAAqB,CAACoB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAAC5E,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFxD,SAAS,CAAC8H,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAAClD,WAAW,CAACuD,iBAAiB,CAAC,IAAI,CAACzC,UAAU,EAAC,IAAI,CAACmB,qBAAqB,CAACe,KAAK,EAAC,CAAC,EAAC,IAAI,CAACnC,QAAQ,CAAC,CAACuC,SAAS,CAAC;YAC7GI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;cAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;cAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;YACzC,CAAC;YACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAACnF,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACsB,WAAW,CAAC+D,WAAW,CAACf,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAOhI,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACAmI,SAAS,CAACY,KAAK,IAAG;MACjB,IAAI,CAACxD,0BAA0B,GAAGwD,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAC,kBAAkBA,CAAC1C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAAC1F,SAAS,IAAI0F,IAAI,CAACzF,QAAQ,MAAMyF,IAAI,CAACxF,KAAK,EAAE,GAAG,EAAE;EACzE;EAGAmI,YAAYA,CAACxG,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACC,QAAQ,EAAE,GAAG,EAAE;EACzC;EAMCkF,cAAcA,CAAA;IAGb,IAAI,CAAC7C,WAAW,CAACmE,iBAAiB,EAAE,CAACf,SAAS,CAACgB,IAAI,IAAG;MACtD,IAAI,CAACrC,YAAY,GAAGqC,IAAI;IAE1B,CAAC,CAAC;EAGA;EAYAC,oBAAoBA,CAAC9C,IAAiB;IACpC,IAAI,CAAC7C,eAAe,CAAC,CAAC,CAAC;EAEzB;EAQFA,eAAeA,CAAC4F,IAAY;IAC1B,IAAI,CAACzF,WAAW,GAAGyF,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAACzD,UAAU,CAACmC,IAAI,EAAE;IACtC,MAAMjF,MAAM,GAAG,IAAI,CAACmE,cAAc,CAACc,IAAI,EAAE;IAEzC,IAAIuB,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAACxC,qBAAqB,CAACe,KAAK;IAEhD,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAACxB,IAAI,EAAE;KAC1B,MAAM,IAAIwB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACvB,IAAI,EAAE;;IAGpC;IACA,IAAIuB,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAACxE,WAAW,CAACuD,iBAAiB,CAACgB,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACuC,SAAS,CAAC;QACzFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;QACzC,CAAC;QACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIvG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACgC,WAAW,CAAC0E,kBAAkB,CAAC,EAAE,EAAE1G,MAAM,EAAEsG,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;QACzC,CAAC;QACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIvG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACgC,WAAW,CAACuD,iBAAiB,CAACgB,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;QACzC,CAAC;QACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIvG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAACgC,WAAW,CAAC0E,kBAAkB,CAACH,OAAO,EAAEvG,MAAM,EAAEsG,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACuC,SAAS,CAAC;QAClFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;UAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;QACzC,CAAC;QACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAAC7D,WAAW,CAAC2E,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAACzD,QAAQ,CAAC,CAACuC,SAAS,CAAC;MAChEI,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACpD,UAAU,GAAGoD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAAClE,UAAU,GAAGiE,GAAG,CAACjE,UAAU;QAChC,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAACtD,UAAU,CAAC;MACzC,CAAC;MACDuD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACtD,UAAiB;IACzCyD,OAAO,CAACc,GAAG,CAACvE,UAAU,CAAC;IAEvB;IACA,IAAI,CAACqB,OAAO,GAAGrB,UAAU,CAACwE,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACrI,MAAM,CAAC;IAC9CqH,OAAO,CAACc,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAClD,OAAO,CAAC;IAEjD,IAAI,CAAC1B,WAAW,CAAC+E,oBAAoB,CAAC,IAAI,CAACrD,OAAO,CAAC,CAAC0B,SAAS,CAACgB,IAAI,IAAG;MAEjEA,IAAI,CAACY,OAAO,CAACC,WAAW,IAAG;QACzB,IAAI,CAACtD,gBAAgB,CAACsD,WAAW,CAACzD,UAAU,CAAC/E,MAAM,CAAC,GAAGwI,WAAW;QAClE,IAAI,CAAC1I,eAAe,CAAC0I,WAAW,CAACzD,UAAU,CAAC/E,MAAM,CAAC,GAAGwI,WAAW,CAAC1D,IAAI,CAAC1F,SAAS,GAAG,GAAG,GAAGoJ,WAAW,CAAC1D,IAAI,CAACzF,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGEoJ,cAAcA,CAAA;IAEZ,IAAI,CAACxG,eAAe,CAAC,CAAC,CAAC;EACzB;EAuBFyG,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,IAAI,CAACpF,IAAI,CAAC0F,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACpC,SAAS,CACpEwC,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBF,QAAQ,CAACC,QAAQ,EAAE;UAC3D/B,OAAO,CAACc,GAAG,CAAC,mBAAmB,EAAEkB,OAAO,CAAC;UAGzC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC;YACnBzI,KAAK,EAAEuI;WACR,CAAC;SACH,MAAM;UACLhC,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEqC,WAAWA,CAAA;IACT,IAAI,CAAC7D,YAAY,GAAG,EAAE;EACxB;EAEIQ,YAAYA,CAAA;IAEZ,IAAI,CAAC5C,WAAW,CAACkG,WAAW,EAAE,CAAC9C,SAAS,CAACgB,IAAI,IAAG;MAChD,IAAI,CAAChE,MAAM,GAAGgE,IAAI;IAEpB,CAAC,CAAC;EACF;EAMF;EACA+B,gBAAgBA,CAACjK,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClB2E,IAAI,EAAE,IAAI;MACV1E,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAED;IACAgK,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACpK,YAAY,CAAC2E,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACA0F,kBAAkBA,CAACC,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMC,OAAO,GAAG,IAAItF,IAAI,CAACqF,IAAI,CAAC;MAC9B,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAOhD,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAiD,QAAQA,CAACvC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAAC9E,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAAC4F,IAAI,CAAC;;EAE9B;EAEAwC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjI,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAkI,YAAYA,CAAA;IACV,IAAI,IAAI,CAAClI,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAmI,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxI,WAAW,GAAGuI,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAChI,UAAU,GAAG,CAAC,EAAE2H,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;EAEA;EAEA;;;EAGA3J,cAAcA,CAACkE,UAAiB;IAC9B,IAAI,CAACK,aAAa,GAAG,IAAIzG,KAAK,EAAE;IAChC,IAAI,CAACyG,aAAa,CAACL,UAAU,GAAGA,UAAU;IAC1C,IAAI,CAACa,SAAS,CAACsF,KAAK,EAAE;IACtB,IAAI,CAACtF,SAAS,CAAC2D,UAAU,CAAC;MACxBtD,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAAC5C,cAAc,GAAG,IAAI;EAC5B;EAGA8H,eAAeA,CAAA;IACb,IAAI,CAAC9H,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC+B,aAAa,GAAG,IAAIzG,KAAK,EAAE;IAChC,IAAI,CAACiH,SAAS,CAACsF,KAAK,EAAE;EACxB;EAGAE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACxF,SAAS,CAACyF,KAAK,EAAE;MACxB,MAAMC,SAAS,GAAG;QAChBxF,KAAK,EAAE,IAAI,CAACF,SAAS,CAAC2F,GAAG,CAAC,OAAO,CAAC,EAAEhF,KAAK;QACzC9E,WAAW,EAAE,IAAI,CAACmE,SAAS,CAAC2F,GAAG,CAAC,aAAa,CAAC,EAAEhF,KAAK;QACrDN,QAAQ,EAAE,IAAI,CAACL,SAAS,CAAC2F,GAAG,CAAC,UAAU,CAAC,EAAEhF,KAAK;QAC/CxB,UAAU,EAAE,IAAI,CAACK,aAAa,CAACL;OAChC;MAED,IAAI,CAACxB,WAAW,CAACiI,aAAa,CAACF,SAAS,CAAC,CAAC3E,SAAS,CAAC;QAClDI,IAAI,EAAGoC,QAAQ,IAAI;UACjB9B,OAAO,CAACc,GAAG,CAAC,6BAA6B,EAAEgB,QAAQ,CAAC;UACpD,IAAI,CAACgC,eAAe,EAAE;UACtB;UACA,IAAI,CAAClJ,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;QACxC,CAAC;QACD+E,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D;QACF;OACD,CAAC;KACH,MAAM;MACL;MACAsE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9F,SAAS,CAAC+F,QAAQ,CAAC,CAACpD,OAAO,CAACqD,GAAG,IAAG;QACjD,IAAI,CAAChG,SAAS,CAAC2F,GAAG,CAACK,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC1C,CAAC,CAAC;;EAEN;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAACrG,SAAS,CAAC2F,GAAG,CAACQ,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,IAAIA,KAAK,CAACH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,CAACC,OAAO,CAAC;EAChE;EAEA;;;EAGAjJ,eAAeA,CAAC8I,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAACrG,SAAS,CAAC2F,GAAG,CAACQ,SAAS,CAAC;IAC3C,IAAIE,KAAK,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,EAAE;MAC1C,IAAIF,KAAK,CAACE,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,MAAMJ,SAAS,aAAa;;MAErC,IAAIE,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,MAAMC,cAAc,GAAGH,KAAK,CAACE,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc;QAC/D,OAAO,MAAML,SAAS,2BAA2BK,cAAc,aAAa;;;IAGhF,OAAO,EAAE;EACX;;;uBA3iBW9I,oBAAoB,EAAA1E,EAAA,CAAAyN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA3N,EAAA,CAAAyN,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAA7N,EAAA,CAAAyN,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA/N,EAAA,CAAAyN,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApBvJ,oBAAoB;MAAAwJ,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBjCxO,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAqB,SAAA,cAAsB;UAEtBrB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAqB,SAAA,iBAAyB;UAIrBrB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAqB,SAAA,iBAES;UAETrB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAA8B;UACTD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAqB,SAAA,iBAK6C;UAE7CrB,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAAwB,UAAA,4BAAAkN,0EAAAC,MAAA;YAAA,OAAkBF,GAAA,CAAAzF,oBAAA,CAAA2F,MAAA,CAAAC,MAAA,CAAAjH,KAAA,CAAyC;UAAA,EAAC;UAC5D3H,EAAA,CAAAsB,UAAA,KAAAuN,2CAAA,yBAEa;UACf7O,EAAA,CAAAG,YAAA,EAAmB;UAU7BH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAAwB,UAAA,2BAAAsN,8DAAAH,MAAA;YAAA,OAAAF,GAAA,CAAAhJ,UAAA,GAAAkJ,MAAA;UAAA,EAAwB,mBAAAI,sDAAA;YAAA,OACfN,GAAA,CAAA5E,cAAA,EAAgB;UAAA,EADD;UAH1B7J,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAqB,SAAA,gBAAiC;UACnCrB,EAAA,CAAAG,YAAA,EAAM;UAkIRH,EAAA,CAAAC,cAAA,gBAAuB;UAInBD,EAAA,CAAAsB,UAAA,KAAA0N,oCAAA,kBAEM;UAGNhP,EAAA,CAAAC,cAAA,cAA6B;UAS6BD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,cAAiD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAsB,UAAA,KAAA2N,mCAAA,mBA8DK;UACPjP,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAsB,UAAA,KAAA4N,oCAAA,kBAgBM;UAEJlP,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,eAAiB;UAGMD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAC,cAAA,aACW;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAAkD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAQnKH,EAAA,CAAAC,cAAA,eACsF;UAK5ED,EAAA,CAAAqB,SAAA,aAAyC;UACzCrB,EAAA,CAAAE,MAAA,IACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,kBAAuG;UAA/CD,EAAA,CAAAwB,UAAA,mBAAA2N,uDAAA;YAAA,OAASV,GAAA,CAAAlC,eAAA,EAAiB;UAAA,EAAC;UAAoBvM,EAAA,CAAAG,YAAA,EAAS;UAGlHH,EAAA,CAAAC,cAAA,gBAA2D;UAA7BD,EAAA,CAAAwB,UAAA,sBAAA4N,wDAAA;YAAA,OAAYX,GAAA,CAAAjC,aAAA,EAAe;UAAA,EAAC;UACxDxM,EAAA,CAAAC,cAAA,eAAwB;UAIlBD,EAAA,CAAAqB,SAAA,aAAsC;UAAArB,EAAA,CAAAE,MAAA,sCACxC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAiB;UAELD,EAAA,CAAAE,MAAA,qBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,KAA+C;UAAAF,EAAA,CAAAqB,SAAA,WAAI;UAC5ErB,EAAA,CAAAC,cAAA,eAAQ;UAAAD,EAAA,CAAAE,MAAA,4BAAS;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,KAC7B;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,gBAAsB;UACZD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAE,MAAA,KAAmE;;UAAAF,EAAA,CAAAqB,SAAA,WAAI;UAC5GrB,EAAA,CAAAC,cAAA,eAAQ;UAAAD,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC/BH,EAAA,CAAAC,cAAA,iBAAoC;UAAAD,EAAA,CAAAE,MAAA,KAAsC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAMvFH,EAAA,CAAAC,cAAA,gBAAiB;UAIXD,EAAA,CAAAqB,SAAA,cAA+B;UAAArB,EAAA,CAAAE,MAAA,6BACjC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAqB,SAAA,kBAKuE;UACvErB,EAAA,CAAAsB,UAAA,MAAA+N,qCAAA,kBAEM;UACRrP,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,gBAA2B;UAEvBD,EAAA,CAAAqB,SAAA,cAA+B;UAAArB,EAAA,CAAAE,MAAA,yBACjC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAC,cAAA,mBAG8D;UACrCD,EAAA,CAAAE,MAAA,4CAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzDH,EAAA,CAAAC,cAAA,mBAAwB;UAAAD,EAAA,CAAAE,MAAA,yDAAiC;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClEH,EAAA,CAAAC,cAAA,mBAAsB;UAAAD,EAAA,CAAAE,MAAA,oCAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAChDH,EAAA,CAAAC,cAAA,mBAAyB;UAAAD,EAAA,CAAAE,MAAA,yCAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAE1DH,EAAA,CAAAsB,UAAA,MAAAgO,qCAAA,kBAEM;UACRtP,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,gBAAyB;UAErBD,EAAA,CAAAqB,SAAA,cAAoC;UAAArB,EAAA,CAAAE,MAAA,2CACtC;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACRH,EAAA,CAAAqB,SAAA,qBAKqI;UACrIrB,EAAA,CAAAsB,UAAA,MAAAiO,qCAAA,kBAEM;UACNvP,EAAA,CAAAC,cAAA,gBAAuB;UACrBD,EAAA,CAAAqB,SAAA,cAAsC;UACtCrB,EAAA,CAAAE,MAAA,qFACF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAKZH,EAAA,CAAAC,cAAA,gBAA0B;UACwBD,EAAA,CAAAwB,UAAA,mBAAAgO,wDAAA;YAAA,OAASf,GAAA,CAAAlC,eAAA,EAAiB;UAAA,EAAC;UACzEvM,EAAA,CAAAqB,SAAA,cAA4B;UAAArB,EAAA,CAAAE,MAAA,iBAC9B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,mBAEsC;UACpCD,EAAA,CAAAqB,SAAA,cAAyC;UAAArB,EAAA,CAAAE,MAAA,gCAC3C;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAQnBH,EAAA,CAAAsB,UAAA,MAAAmO,qCAAA,kBAA4F;UAW9FzP,EAAA,CAAAG,YAAA,EAAO;;;;UAlaKH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAAqO,GAAA,CAAA7H,qBAAA,CAAqC,oBAAA8I,GAAA;UAMrC1P,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAqO,GAAA,CAAA7F,kBAAA,CAAkC;UAEL5I,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAqO,GAAA,CAAAtJ,0BAAA,CAA6B;UAkBhEnF,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAAqO,GAAA,CAAAhJ,UAAA,CAAwB;UA2IpBzF,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAA7N,YAAA,CAAA2E,IAAA,CAAuB;UAyBSvF,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAAqO,GAAA,CAAAzJ,UAAA,CAAa;UA0ENhF,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAAtK,UAAA,KAAoB;UAoCbnE,EAAA,CAAAM,SAAA,IAAmD;UAAnDN,EAAA,CAAA0C,WAAA,YAAA+L,GAAA,CAAAhK,cAAA,oBAAmD;UAAjFzE,EAAA,CAAAsD,WAAA,SAAAmL,GAAA,CAAAhK,cAAA,CAA6B;UAO3CzE,EAAA,CAAAM,SAAA,GACF;UADEN,EAAA,CAAAc,kBAAA,gCAAA2N,GAAA,CAAAjI,aAAA,CAAAL,UAAA,kBAAAsI,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA9D,KAAA,kBAAAoM,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA9D,KAAA,CAAAC,QAAA,MACF;UAIItC,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,cAAAqO,GAAA,CAAAzH,SAAA,CAAuB;UASMhH,EAAA,CAAAM,SAAA,IAA+C;UAA/CN,EAAA,CAAAc,kBAAA,MAAA2N,GAAA,CAAAjI,aAAA,CAAAL,UAAA,kBAAAsI,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA9D,KAAA,kBAAAoM,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA9D,KAAA,CAAAC,QAAA,KAA+C;UAC7CtC,EAAA,CAAAM,SAAA,GAC7B;UAD6BN,EAAA,CAAAc,kBAAA,MAAA2N,GAAA,CAAAjI,aAAA,CAAAL,UAAA,kBAAAsI,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA5D,QAAA,MAC7B;UAEuCvC,EAAA,CAAAM,SAAA,GAAmE;UAAnEN,EAAA,CAAAc,kBAAA,MAAAd,EAAA,CAAAwC,WAAA,UAAAiM,GAAA,CAAAjI,aAAA,CAAAL,UAAA,kBAAAsI,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAA1D,eAAA,oBAAmE;UAEpEzC,EAAA,CAAAM,SAAA,GAAsC;UAAtCN,EAAA,CAAAoC,iBAAA,CAAAqM,GAAA,CAAAjI,aAAA,CAAAL,UAAA,kBAAAsI,GAAA,CAAAjI,aAAA,CAAAL,UAAA,CAAAxD,MAAA,CAAsC;UAgBrE3C,EAAA,CAAAM,SAAA,GAAoF;UAApFN,EAAA,CAAAsD,WAAA,eAAAmL,GAAA,CAAAvB,QAAA,yBAAAuB,GAAA,CAAAvB,QAAA,uBAAoF;UAE5DlN,EAAA,CAAAM,SAAA,GAAqE;UAArEN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAAvB,QAAA,yBAAAuB,GAAA,CAAAvB,QAAA,uBAAqE;UAa5FlN,EAAA,CAAAM,SAAA,GAAqD;UAArDN,EAAA,CAAAsD,WAAA,eAAAmL,GAAA,CAAAvB,QAAA,yBAAqD;UAM9BlN,EAAA,CAAAM,SAAA,GAAsC;UAAtCN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAAvB,QAAA,yBAAsC;UAc3DlN,EAAA,CAAAM,SAAA,GAAgG;UAAhGN,EAAA,CAAAsD,WAAA,eAAAmL,GAAA,CAAAvB,QAAA,+BAAAuB,GAAA,CAAAvB,QAAA,6BAAgG;UAE3ElN,EAAA,CAAAM,SAAA,GAAiF;UAAjFN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAAvB,QAAA,+BAAAuB,GAAA,CAAAvB,QAAA,6BAAiF;UAiB5GlN,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,cAAAqO,GAAA,CAAAzH,SAAA,CAAAyF,KAAA,CAA6B;UAUiBzM,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAqO,GAAA,CAAAhK,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./utilisateur/utilisateur.service\";\nimport * as i2 from \"@angular/router\";\nexport class AppComponent {\n  constructor(utilisateurService, router) {\n    this.utilisateurService = utilisateurService;\n    this.router = router;\n    this.title = 'conseil';\n  }\n  ngOnInit() {\n    // Check if user is authenticated on app startup\n    if (this.utilisateurService.isAuthenticated()) {\n      console.log('User is authenticated, staying on current route');\n      // User is authenticated, let them stay where they are\n      // or redirect to appropriate dashboard based on role\n      const currentUrl = this.router.url;\n      if (currentUrl === '/' || currentUrl === '/utilisateur') {\n        this.utilisateurService.redirectToDashboard();\n      }\n    } else {\n      console.log('User not authenticated, redirecting to login');\n      // Only redirect to login if not already on a public route\n      const currentUrl = this.router.url;\n      const publicRoutes = ['/utilisateur', '/motpasseoublie', '/reset-password', '/user-registration'];\n      if (!publicRoutes.includes(currentUrl)) {\n        this.router.navigate(['/utilisateur']);\n      }\n    }\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.UtilisateurService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 1,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"router-outlet\");\n        }\n      },\n      dependencies: [i2.RouterOutlet],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "utilisateurService", "router", "title", "ngOnInit", "isAuthenticated", "console", "log", "currentUrl", "url", "redirectToDashboard", "publicRoutes", "includes", "navigate", "i0", "ɵɵdirectiveInject", "i1", "UtilisateurService", "i2", "Router", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UtilisateurService } from './utilisateur/utilisateur.service';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.css']\r\n})\r\nexport class AppComponent implements OnInit {\r\n  title = 'conseil';\r\n\r\n  constructor(\r\n    private utilisateurService: UtilisateurService,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    // Check if user is authenticated on app startup\r\n    if (this.utilisateurService.isAuthenticated()) {\r\n      console.log('User is authenticated, staying on current route');\r\n      // User is authenticated, let them stay where they are\r\n      // or redirect to appropriate dashboard based on role\r\n      const currentUrl = this.router.url;\r\n      if (currentUrl === '/' || currentUrl === '/utilisateur') {\r\n        this.utilisateurService.redirectToDashboard();\r\n      }\r\n    } else {\r\n      console.log('User not authenticated, redirecting to login');\r\n      // Only redirect to login if not already on a public route\r\n      const currentUrl = this.router.url;\r\n      const publicRoutes = ['/utilisateur', '/motpasseoublie', '/reset-password', '/user-registration'];\r\n      if (!publicRoutes.includes(currentUrl)) {\r\n        this.router.navigate(['/utilisateur']);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<router-outlet> </router-outlet>"], "mappings": ";;;AASA,OAAM,MAAOA,YAAY;EAGvBC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;IAJhB,KAAAC,KAAK,GAAG,SAAS;EAKd;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACH,kBAAkB,CAACI,eAAe,EAAE,EAAE;MAC7CC,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D;MACA;MACA,MAAMC,UAAU,GAAG,IAAI,CAACN,MAAM,CAACO,GAAG;MAClC,IAAID,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,cAAc,EAAE;QACvD,IAAI,CAACP,kBAAkB,CAACS,mBAAmB,EAAE;;KAEhD,MAAM;MACLJ,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;MAC3D;MACA,MAAMC,UAAU,GAAG,IAAI,CAACN,MAAM,CAACO,GAAG;MAClC,MAAME,YAAY,GAAG,CAAC,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,oBAAoB,CAAC;MACjG,IAAI,CAACA,YAAY,CAACC,QAAQ,CAACJ,UAAU,CAAC,EAAE;QACtC,IAAI,CAACN,MAAM,CAACW,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;;;EAG5C;;;uBA3BWd,YAAY,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZpB,YAAY;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzBX,EAAA,CAAAa,SAAA,oBAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
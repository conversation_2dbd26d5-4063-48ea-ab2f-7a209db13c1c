{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../utilisateur/utilisateur.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(utilisateurService, router) {\n    this.utilisateurService = utilisateurService;\n    this.router = router;\n  }\n  canActivate() {\n    if (this.utilisateurService.isAuthenticated()) {\n      return true;\n    } else {\n      this.router.navigate(['/utilisateur']);\n      return false;\n    }\n  }\n  static {\n    this.ɵfac = function AuthGuard_Factory(t) {\n      return new (t || AuthGuard)(i0.ɵɵinject(i1.UtilisateurService), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthGuard,\n      factory: AuthGuard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "utilisateurService", "router", "canActivate", "isAuthenticated", "navigate", "i0", "ɵɵinject", "i1", "UtilisateurService", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { CanActivate, Router } from '@angular/router';\nimport { UtilisateurService } from '../utilisateur/utilisateur.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthGuard implements CanActivate {\n\n  constructor(\n    private utilisateurService: UtilisateurService,\n    private router: Router\n  ) {}\n\n  canActivate(): boolean {\n    if (this.utilisateurService.isAuthenticated()) {\n      return true;\n    } else {\n      this.router.navigate(['/utilisateur']);\n      return false;\n    }\n  }\n}\n"], "mappings": ";;;AAOA,OAAM,MAAOA,SAAS;EAEpBC,YACUC,kBAAsC,EACtCC,MAAc;IADd,KAAAD,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACF,kBAAkB,CAACG,eAAe,EAAE,EAAE;MAC7C,OAAO,IAAI;KACZ,MAAM;MACL,IAAI,CAACF,MAAM,CAACG,QAAQ,CAAC,CAAC,cAAc,CAAC,CAAC;MACtC,OAAO,KAAK;;EAEhB;;;uBAdWN,SAAS,EAAAO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAATZ,SAAS;MAAAa,OAAA,EAATb,SAAS,CAAAc,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
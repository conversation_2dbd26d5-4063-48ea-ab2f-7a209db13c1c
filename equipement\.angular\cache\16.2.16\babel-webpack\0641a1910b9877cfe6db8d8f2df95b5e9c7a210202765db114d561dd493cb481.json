{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { Utilisateur } from './utilisateur';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class UtilisateurService {\n  constructor(httpClient, router) {\n    this.httpClient = httpClient;\n    this.router = router;\n    this.baseURL = \"http://localhost:8085/auth\";\n    this.baseURL1 = \"http://localhost:8085/api/users\";\n    this.user = new Utilisateur();\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n  }\n  login(loginRequest) {\n    return this.httpClient.post(`${this.baseURL}/login`, loginRequest).pipe(tap(user => {\n      if (user && user.token) {\n        // Store user data in session storage\n        sessionStorage.setItem('token', user.token);\n        sessionStorage.setItem('username', user.username);\n        sessionStorage.setItem('registrationNumber', user.id.toString());\n        sessionStorage.setItem('role', user.role);\n        sessionStorage.setItem('email', user.email);\n        // Update current user subject\n        this.currentUserSubject.next(user);\n      }\n    }));\n  }\n  register(agent) {\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  getUtilisateur() {\n    return this.httpClient.get(`${this.baseURL}/AllUsers`);\n  }\n  getAgents() {\n    return this.httpClient.get(`${this.baseURL1}/agents`);\n  }\n  forgotPassword(email) {\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, {\n      email\n    }, {\n      responseType: 'text'\n    });\n  }\n  resetPassword(token, newPassword) {\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, {\n      password: newPassword\n    }, {\n      responseType: 'text'\n    });\n  }\n  checkEmailAvailability(email) {\n    return this.httpClient.get(`${this.baseURL}/check-email?email=${email}`);\n  }\n  loadUserFromStorage() {\n    const token = sessionStorage.getItem('token');\n    const username = sessionStorage.getItem('username');\n    const id = sessionStorage.getItem('id');\n    const role = sessionStorage.getItem('role');\n    const email = sessionStorage.getItem('email');\n    if (token && username && id && role && email) {\n      const user = {\n        id: parseInt(id),\n        username,\n        email,\n        role,\n        token\n      };\n      this.currentUserSubject.next(user);\n    }\n  }\n  redirectToDashboard() {\n    const role = this.getUserRole();\n    if (role === 'USER') {\n      this.router.navigate(['/equipementDSI']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  getUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  static {\n    this.ɵfac = function UtilisateurService_Factory(t) {\n      return new (t || UtilisateurService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilisateurService,\n      factory: UtilisateurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "Utilisa<PERSON>ur", "UtilisateurService", "constructor", "httpClient", "router", "baseURL", "baseURL1", "user", "currentUserSubject", "currentUser$", "asObservable", "login", "loginRequest", "post", "pipe", "token", "sessionStorage", "setItem", "username", "id", "toString", "role", "email", "next", "register", "agent", "headers", "getUtilisateur", "get", "getAgents", "forgotPassword", "responseType", "resetPassword", "newPassword", "password", "checkEmailAvailability", "loadUserFromStorage", "getItem", "parseInt", "redirectToDashboard", "getUserRole", "navigate", "getCurrentUser", "value", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient,HttpClientModule } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\r\nimport {  Agent, Utilisateur } from './utilisateur';\r\nimport { Router } from '@angular/router';\r\nexport interface LoginRequest {\r\n  registrationNumber: string;\r\n  password: string;\r\n}\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilisateurService {\r\n   private baseURL=\"http://localhost:8085/auth\";\r\n  private baseURL1=\"http://localhost:8085/api/users\";\r\n  constructor(private httpClient:HttpClient,private router: Router) { }\r\n\r\nuser: Utilisateur = new Utilisateur();\r\n  private currentUserSubject = new BehaviorSubject<Utilisateur | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  \r\n  login(loginRequest: LoginRequest): Observable<any> {\r\n    return this.httpClient.post<any>(`${this.baseURL}/login`, loginRequest)\r\n       .pipe(\r\n        tap(user => {\r\n          if (user && user.token) {\r\n            // Store user data in session storage\r\n            sessionStorage.setItem('token', user.token);\r\n            sessionStorage.setItem('username', user.username);\r\n            sessionStorage.setItem('registrationNumber', user.id.toString());\r\n            sessionStorage.setItem('role', user.role);\r\n            sessionStorage.setItem('email', user.email);\r\n            \r\n            // Update current user subject\r\n            this.currentUserSubject.next(user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n  \r\n\r\n  register(agent: Utilisateur): Observable<any> {\r\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n    getUtilisateur(): Observable<Utilisateur[]> {\r\n    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);\r\n  }\r\n    getAgents(): Observable<Agent[]> {\r\n    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);\r\n  }\r\n  forgotPassword(email: string) {\r\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });\r\n  }\r\n  resetPassword(token: string, newPassword: string) {\r\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });\r\n  }\r\n  \r\n  checkEmailAvailability(email: string): Observable<any> {\r\n    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);\r\n  }\r\n\r\n\r\n  private loadUserFromStorage(): void {\r\n    const token = sessionStorage.getItem('token');\r\n    const username = sessionStorage.getItem('username');\r\n    const id = sessionStorage.getItem('id');\r\n    const role = sessionStorage.getItem('role');\r\n    const email = sessionStorage.getItem('email');\r\n\r\n    if (token && username && id && role && email) {\r\n      const user: Utilisateur = {\r\n        id: parseInt(id),\r\n        username,\r\n        email,\r\n        role,\r\n        token\r\n      };\r\n      this.currentUserSubject.next(user);\r\n    }\r\n  }\r\n\r\n\r\n\r\n\r\n  redirectToDashboard(): void {\r\n    const role = this.getUserRole();\r\n    if (role === 'USER') {\r\n      this.router.navigate(['/equipementDSI']);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n    getUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  getCurrentUser(): Utilisateur | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AACvD,SAAiBC,WAAW,QAAQ,eAAe;;;;AASnD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,UAAqB,EAASC,MAAc;IAA5C,KAAAD,UAAU,GAAVA,UAAU;IAAoB,KAAAC,MAAM,GAANA,MAAM;IAF/C,KAAAC,OAAO,GAAC,4BAA4B;IACrC,KAAAC,QAAQ,GAAC,iCAAiC;IAGpD,KAAAC,IAAI,GAAgB,IAAIP,WAAW,EAAE;IAC3B,KAAAQ,kBAAkB,GAAG,IAAIV,eAAe,CAAqB,IAAI,CAAC;IACnE,KAAAW,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAJQ;EAMpEC,KAAKA,CAACC,YAA0B;IAC9B,OAAO,IAAI,CAACT,UAAU,CAACU,IAAI,CAAM,GAAG,IAAI,CAACR,OAAO,QAAQ,EAAEO,YAAY,CAAC,CACnEE,IAAI,CACJf,GAAG,CAACQ,IAAI,IAAG;MACT,IAAIA,IAAI,IAAIA,IAAI,CAACQ,KAAK,EAAE;QACtB;QACAC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEV,IAAI,CAACQ,KAAK,CAAC;QAC3CC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEV,IAAI,CAACW,QAAQ,CAAC;QACjDF,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAEV,IAAI,CAACY,EAAE,CAACC,QAAQ,EAAE,CAAC;QAChEJ,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACc,IAAI,CAAC;QACzCL,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEV,IAAI,CAACe,KAAK,CAAC;QAE3C;QACA,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAAChB,IAAI,CAAC;;IAEtC,CAAC,CAAC,CACH;EACL;EAGAiB,QAAQA,CAACC,KAAkB;IACzB,OAAO,IAAI,CAACtB,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,WAAW,EAAEoB,KAAK,EAAE;MAC7DC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAC9C,CAAC;EACJ;EACEC,cAAcA,CAAA;IACd,OAAO,IAAI,CAACxB,UAAU,CAACyB,GAAG,CAAgB,GAAG,IAAI,CAACvB,OAAO,WAAW,CAAC;EACvE;EACEwB,SAASA,CAAA;IACT,OAAO,IAAI,CAAC1B,UAAU,CAACyB,GAAG,CAAU,GAAG,IAAI,CAACtB,QAAQ,SAAS,CAAC;EAChE;EACAwB,cAAcA,CAACR,KAAa;IAC1B,OAAO,IAAI,CAACnB,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,kBAAkB,EAAE;MAAEiB;IAAK,CAAE,EAAE;MAAES,YAAY,EAAE;IAAM,CAAE,CAAC;EACrG;EACAC,aAAaA,CAACjB,KAAa,EAAEkB,WAAmB;IAC9C,OAAO,IAAI,CAAC9B,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,yBAAyBU,KAAK,EAAE,EAAE;MAAEmB,QAAQ,EAAED;IAAW,CAAE,EAAE;MAAEF,YAAY,EAAE;IAAM,CAAE,CAAC;EACnI;EAEAI,sBAAsBA,CAACb,KAAa;IAClC,OAAO,IAAI,CAACnB,UAAU,CAACyB,GAAG,CAAM,GAAG,IAAI,CAACvB,OAAO,sBAAsBiB,KAAK,EAAE,CAAC;EAC/E;EAGQc,mBAAmBA,CAAA;IACzB,MAAMrB,KAAK,GAAGC,cAAc,CAACqB,OAAO,CAAC,OAAO,CAAC;IAC7C,MAAMnB,QAAQ,GAAGF,cAAc,CAACqB,OAAO,CAAC,UAAU,CAAC;IACnD,MAAMlB,EAAE,GAAGH,cAAc,CAACqB,OAAO,CAAC,IAAI,CAAC;IACvC,MAAMhB,IAAI,GAAGL,cAAc,CAACqB,OAAO,CAAC,MAAM,CAAC;IAC3C,MAAMf,KAAK,GAAGN,cAAc,CAACqB,OAAO,CAAC,OAAO,CAAC;IAE7C,IAAItB,KAAK,IAAIG,QAAQ,IAAIC,EAAE,IAAIE,IAAI,IAAIC,KAAK,EAAE;MAC5C,MAAMf,IAAI,GAAgB;QACxBY,EAAE,EAAEmB,QAAQ,CAACnB,EAAE,CAAC;QAChBD,QAAQ;QACRI,KAAK;QACLD,IAAI;QACJN;OACD;MACD,IAAI,CAACP,kBAAkB,CAACe,IAAI,CAAChB,IAAI,CAAC;;EAEtC;EAKAgC,mBAAmBA,CAAA;IACjB,MAAMlB,IAAI,GAAG,IAAI,CAACmB,WAAW,EAAE;IAC/B,IAAInB,IAAI,KAAK,MAAM,EAAE;MACnB,IAAI,CAACjB,MAAM,CAACqC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;KACzC,MAAM;MACL,IAAI,CAACrC,MAAM,CAACqC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEED,WAAWA,CAAA;IACX,MAAMjC,IAAI,GAAG,IAAI,CAACmC,cAAc,EAAE;IAClC,OAAOnC,IAAI,GAAGA,IAAI,CAACc,IAAI,GAAG,IAAI;EAChC;EAEAqB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClC,kBAAkB,CAACmC,KAAK;EACtC;;;uBA1FW1C,kBAAkB,EAAA2C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAlBhD,kBAAkB;MAAAiD,OAAA,EAAlBjD,kBAAkB,CAAAkD,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
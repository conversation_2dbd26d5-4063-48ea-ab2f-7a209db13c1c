{"ast": null, "code": "import * as bootstrap from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../dashboard/type.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../Shared/layout/layout.component\";\nfunction ModelComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r1.signupErrors.nomModel);\n  }\n}\nfunction ModelComponent_option_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marque_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", marque_r20);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", marque_r20.nomMarque, \" \");\n  }\n}\nfunction ModelComponent_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r3.signupErrors.marque);\n  }\n}\nfunction ModelComponent_div_50_option_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r22 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r22.nomType);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r22.nomType, \" \");\n  }\n}\nfunction ModelComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"label\", 85);\n    i0.ɵɵtext(2, \"Type associ\\u00E9\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"select\", 86);\n    i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_div_50_Template_select_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.newModal.typeAssociee = $event);\n    });\n    i0.ɵɵelementStart(4, \"option\", 87);\n    i0.ɵɵtext(5, \"S\\u00E9lectionner un type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ModelComponent_div_50_option_6_Template, 2, 2, \"option\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r4.newModal.typeAssociee);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"value\", null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.newModal.marque == null ? null : ctx_r4.newModal.marque.types);\n  }\n}\nfunction ModelComponent_option_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r25);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r25.nomFournisseur, \" \");\n  }\n}\nfunction ModelComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(ctx_r6.signupErrors.fournisseur);\n  }\n}\nfunction ModelComponent_div_79_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModelComponent_div_79_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Minimum 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModelComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtemplate(1, ModelComponent_div_79_div_1_Template, 2, 0, \"div\", 26);\n    i0.ɵɵtemplate(2, ModelComponent_div_79_div_2_Template, 2, 0, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r8 = i0.ɵɵreference(78);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r8.errors == null ? null : _r8.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r8.errors == null ? null : _r8.errors[\"minlength\"]);\n  }\n}\nfunction ModelComponent_option_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marque_r28 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", marque_r28);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", marque_r28.nomMarque, \" \");\n  }\n}\nfunction ModelComponent_div_87_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1, \"La marque est requise\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModelComponent_option_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 84);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const fournisseur_r29 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", fournisseur_r29);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", fournisseur_r29.nomFournisseur, \" \");\n  }\n}\nfunction ModelComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 83);\n    i0.ɵɵtext(1, \"Le fournisseur est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModelComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r16.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r16.notification.message, \"\\n\");\n  }\n}\nfunction ModelComponent_div_106_span_8_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \", \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ModelComponent_div_106_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, ModelComponent_div_106_span_8_span_2_Template, 2, 0, \"span\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r32 = ctx.$implicit;\n    const i_r33 = ctx.index;\n    const marque_r30 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r32.nomType, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", i_r33 < marque_r30.types.length - 1);\n  }\n}\nfunction ModelComponent_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"div\")(3, \"p\", 92);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 93);\n    i0.ɵɵtext(6, \"Cr\\u00E9\\u00E9 le 15/01/2024\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"p\", 94);\n    i0.ɵɵtemplate(8, ModelComponent_div_106_span_8_Template, 3, 2, \"span\", 78);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 95)(10, \"span\", 96);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\");\n    i0.ɵɵtext(13, \" model\\n\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const marque_r30 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(marque_r30.nomMarque);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", marque_r30.types);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(marque_r30.models.length);\n  }\n}\nfunction ModelComponent_option_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 88);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const marque_r36 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", marque_r36.nomMarque);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(marque_r36.nomMarque);\n  }\n}\nfunction ModelComponent_tr_152_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 97)(2, \"div\", 98)(3, \"h5\", 99);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(5, \"td\", 100)(6, \"span\", 101);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"td\", 100)(9, \"span\", 101);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"td\", 100)(12, \"div\", 98)(13, \"h6\", 102);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"td\", 103)(16, \"button\", 104);\n    i0.ɵɵlistener(\"click\", function ModelComponent_tr_152_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const model_r37 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.openModal1(model_r37));\n    });\n    i0.ɵɵtext(17, \" \\u270F\\uFE0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"button\", 105);\n    i0.ɵɵlistener(\"click\", function ModelComponent_tr_152_Template_button_click_18_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const model_r37 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.confirmDelete(model_r37.idModel));\n    });\n    i0.ɵɵtext(19, \" \\uD83D\\uDDD1\\uFE0F \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const model_r37 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(model_r37.nomModel);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", model_r37.marque == null ? null : model_r37.marque.nomMarque, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", model_r37.typeAssociee, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(model_r37.specification);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class ModelComponent {\n  constructor(authservice) {\n    this.authservice = authservice;\n    this.marques = [];\n    this.models = [];\n    this.searchText = '';\n    this.isModalOpen = false;\n    this.fournisseurs = [];\n    // Notification system\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.newModal = {\n      idModel: 0,\n      nomModel: '',\n      specification: '',\n      marque: null,\n      equipements: [],\n      fournisseur: null,\n      typeAssociee: ''\n    };\n    this.newModal1 = {\n      idModel: 0,\n      nomModel: '',\n      specification: '',\n      marque: null,\n      equipements: [],\n      fournisseur: null,\n      typeAssociee: ''\n    };\n    this.selectedMarqueName = ''; // Holds selected marque name (or id)\n    this.signupErrors = {};\n  }\n  ngOnInit() {\n    this.GetAllMarques();\n    this.GetAllModels();\n    this.GetAllFournisseur();\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  get filteredModels() {\n    return this.models.filter(model => {\n      const matchMarque = !this.selectedMarqueName || model.marque?.nomMarque === this.selectedMarqueName;\n      const matchSearch = !this.searchText || model.nomModel.toLowerCase().includes(this.searchText.toLowerCase());\n      return matchMarque && matchSearch;\n    });\n  }\n  GetAllFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  GetAllMarques() {\n    this.authservice.getAllMarques().subscribe(data => {\n      this.marques = data;\n    });\n  }\n  deleteModel(id) {\n    this.authservice.deleteModel(id).subscribe(() => {\n      this.models = this.models.filter(model => model.idModel !== id);\n    });\n  }\n  confirmDelete(ModelId) {\n    console.log(ModelId);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n    this.showNotification('success', 'Modèle supprimer avec succès');\n    this.deleteModel(ModelId);\n  }\n  openModal1(Modal) {\n    this.signupErrors.nomModel = '';\n    this.signupErrors.marque = '';\n    this.newModal1 = {\n      ...Modal\n    };\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = new bootstrap.Modal(modalElement);\n      modal.show();\n    } else {\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\n    }\n  }\n  onUpdateClick(form) {\n    if (form.invalid) {\n      form.form.markAllAsTouched(); // pour forcer l'affichage des erreurs\n      return;\n    }\n    this.updateData(); // méthode existante\n  }\n\n  closeModal1() {\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\n      modal.hide();\n    }\n  }\n  updateData() {\n    console.log('Données mises à jour:', this.newModal1);\n    this.authservice.updateModel(this.newModal1).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Modèle modifié avec succès');\n      this.closeModal1();\n      this.GetAllModels();\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Erreur lors de la modification');\n    });\n  }\n  compareMarques(m1, m2) {\n    return m1 && m2 ? m1.idMarque === m2.idMarque : m1 === m2;\n  }\n  compareFournisseurs(m1, m2) {\n    return m1 && m2 ? m1.idFournisseur === m2.idFournisseur : m1 === m2;\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  validateSignup() {\n    this.resetErrors();\n    let isValid = true;\n    // Username\n    if (!this.newModal.nomModel || this.newModal.nomModel.trim().length === 0) {\n      this.signupErrors.nomModel = 'Le nom de model est requis';\n      isValid = false;\n    } else if (this.newModal.nomModel.length < 3) {\n      this.signupErrors.nomModel = 'Le nom de model doit contenir au moins 3 caractères';\n      isValid = false;\n    }\n    if (!this.newModal.specification || this.newModal.specification.trim().length === 0) {\n      this.signupErrors.specification = 'La specification de type est requis';\n      isValid = false;\n    } else if (this.newModal.specification.length < 3) {\n      this.signupErrors.specification = 'La specification doit contenir au moins 3 caractères';\n      isValid = false;\n    }\n    if (this.newModal.marque == null) {\n      this.signupErrors.marque = 'La marque de type est requis';\n      isValid = false;\n    }\n    if (this.newModal.typeAssociee == null) {\n      this.signupErrors.marque = '  type est requis';\n      isValid = false;\n    }\n    return isValid;\n  }\n  onRegister() {\n    if (!this.validateSignup()) {\n      return;\n    }\n    console.log('User Data:', this.newModal);\n    this.authservice.addModel(this.newModal).subscribe({\n      next: response => {\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Modèle ajouté avec succès');\n        // 🟢 Mettre à jour le tableau local pour affichage immédiat\n        this.models.push(response);\n        // 🧹 Réinitialiser le formulaire et fermer le modal\n        this.closeModal();\n        window.scrollTo({\n          top: 0,\n          behavior: 'smooth'\n        });\n      },\n      error: error => {\n        console.error('Registration failed:', error);\n        this.showNotification('error', 'Erreur lors de l\\'ajout');\n      }\n    });\n  }\n  static {\n    this.ɵfac = function ModelComponent_Factory(t) {\n      return new (t || ModelComponent)(i0.ɵɵdirectiveInject(i1.TypeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ModelComponent,\n      selectors: [[\"app-model\"]],\n      decls: 161,\n      vars: 35,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\", 3, \"click\"], [1, \"icon\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"ngSubmit\"], [\"userForm\", \"ngForm\"], [\"for\", \"email\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"nomType\", \"type\", \"text\", \"name\", \"nomType\", \"placeholder\", \"Ex: Latitude 5520, Elitebook 845G...\", \"required\", \"\", 1, \"form-inputp\", 3, \"ngModel\", \"ngModelChange\"], [\"style\", \"color:red\", 4, \"ngIf\"], [\"for\", \"type\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"type\", \"name\", \"marque\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\", 3, \"ngModel\", \"ngModelChange\"], [\"disabled\", \"\", \"hidden\", \"\", 3, \"ngValue\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"id\", \"type\", \"name\", \"fourniseeur\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"email\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"description\", \"name\", \"description\", \"placeholder\", \"Ex: Intel i5, 8GB RAM, 256GB SSD...\", \"rows\", \"3\", \"cols\", \"60\", 3, \"ngModel\", \"ngModelChange\"], [\"type\", \"submit\"], [\"id\", \"updateModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"updateModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [1, \"modal-content\", \"shadow\", \"rounded-4\"], [\"id\", \"updateModalLabel\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [\"updateForm\", \"ngForm\"], [1, \"mb-4\"], [\"for\", \"nomModel\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"type\", \"text\", \"id\", \"nomModel\", \"name\", \"nomModel\", \"required\", \"\", \"minlength\", \"2\", 1, \"form-inputp\", 3, \"ngModel\", \"ngModelChange\"], [\"nomModel\", \"ngModel\"], [\"for\", \"type\", 1, \"form-label\"], [\"id\", \"type\", \"name\", \"marque\", \"required\", \"\", 1, \"form-inputp\", 3, \"ngModel\", \"compareWith\", \"ngModelChange\"], [\"marque\", \"ngModel\"], [\"id\", \"type\", \"name\", \"fournisseur\", \"required\", \"\", 1, \"form-inputp\", 3, \"ngModel\", \"compareWith\", \"ngModelChange\"], [\"fournisseur\", \"ngModel\"], [\"for\", \"description\", 1, \"form-label\"], [\"id\", \"description\", \"name\", \"description\", \"rows\", \"3\", \"cols\", \"60\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"px-4\", 3, \"disabled\", \"click\"], [1, \"row\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"card1\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"d-md-flex\", \"align-items-center\"], [1, \"card-title\"], [1, \"card-subtitle\"], [1, \"ms-auto\", \"mt-3\", \"mt-md-0\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"theme-select\", \"border-0\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", \"mt-3\"], [1, \"search-wrapper\", \"flex-grow-1\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un model...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [1, \"icon-search\"], [1, \"form-select\", 2, \"min-width\", \"200px\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [2, \"color\", \"red\"], [3, \"ngValue\"], [2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-top\", \"10px\"], [\"name\", \"selectedType\", \"required\", \"\", 1, \"form-inputp\", 2, \"width\", \"100%\", 3, \"ngModel\", \"ngModelChange\"], [\"disabled\", \"\", \"hidden\", \"\", 3, \"value\"], [3, \"value\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"card1\"], [1, \"card1-flex\"], [1, \"card1-title\"], [1, \"card1-date\"], [1, \"card1-desc\"], [1, \"card1-footer\"], [1, \"card1-badge\"], [1, \"px-1\"], [1, \"ms-3\"], [1, \"mb-0\", \"fw-bolder\", 2, \"font-size\", \"18px\", \"color\", \"#2c3e50\"], [1, \"px-0\"], [1, \"badge\", \"bg-info\", \"me-1\"], [1, \"mb-0\", \"fw-bolder\"], [1, \"text-end\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-smc\", 2, \"color\", \"blue\", \"font-size\", \"18px\", \"border\", \"none\", \"background\", \"none\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-sm\", 2, \"color\", \"red\", \"font-size\", \"18px\", \"border\", \"none\", \"background\", \"none\", 3, \"click\"]],\n      template: function ModelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r41 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"Marques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les marques d'\\u00E9quipements par type \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function ModelComponent_Template_button_click_24_listener() {\n            return ctx.openModal();\n          });\n          i0.ɵɵelementStart(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouveau Model \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13);\n          i0.ɵɵlistener(\"click\", function ModelComponent_Template_div_click_28_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(29, \"div\", 14);\n          i0.ɵɵlistener(\"click\", function ModelComponent_Template_div_click_29_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(30, \"span\", 15);\n          i0.ɵɵlistener(\"click\", function ModelComponent_Template_span_click_30_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(31, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"h3\", 16);\n          i0.ɵɵtext(33, \"Ajouter un nouveau Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"form\", 17, 18);\n          i0.ɵɵlistener(\"ngSubmit\", function ModelComponent_Template_form_ngSubmit_34_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(36, \"br\");\n          i0.ɵɵelementStart(37, \"label\", 19);\n          i0.ɵɵtext(38, \"Nom du model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"input\", 20);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.newModal.nomModel = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, ModelComponent_div_40_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelement(41, \"br\");\n          i0.ɵɵelementStart(42, \"label\", 22);\n          i0.ɵɵtext(43, \"Marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"br\");\n          i0.ɵɵelementStart(45, \"select\", 23);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_select_ngModelChange_45_listener($event) {\n            return ctx.newModal.marque = $event;\n          });\n          i0.ɵɵelementStart(46, \"option\", 24);\n          i0.ɵɵtext(47, \"S\\u00E9lectionner une marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, ModelComponent_option_48_Template, 2, 2, \"option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(49, ModelComponent_div_49_Template, 2, 1, \"div\", 21);\n          i0.ɵɵtemplate(50, ModelComponent_div_50_Template, 7, 3, \"div\", 26);\n          i0.ɵɵelement(51, \"br\");\n          i0.ɵɵelementStart(52, \"label\", 22);\n          i0.ɵɵtext(53, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(54, \"br\");\n          i0.ɵɵelementStart(55, \"select\", 27);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_select_ngModelChange_55_listener($event) {\n            return ctx.newModal.fournisseur = $event;\n          });\n          i0.ɵɵelementStart(56, \"option\", 24);\n          i0.ɵɵtext(57, \"S\\u00E9lectionner fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(58, ModelComponent_option_58_Template, 2, 2, \"option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, ModelComponent_div_59_Template, 2, 1, \"div\", 21);\n          i0.ɵɵelementStart(60, \"label\", 28);\n          i0.ɵɵtext(61, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"textarea\", 29);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_textarea_ngModelChange_62_listener($event) {\n            return ctx.newModal.specification = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"button\", 30);\n          i0.ɵɵtext(64, \"Enregistrer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(65, \"div\", 31)(66, \"div\", 32)(67, \"div\", 33)(68, \"h5\", 34);\n          i0.ɵɵtext(69, \"\\uD83D\\uDCDD Modifier les informations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(70, \"button\", 35);\n          i0.ɵɵelementStart(71, \"div\", 36)(72, \"form\", null, 37)(74, \"div\", 38)(75, \"label\", 39);\n          i0.ɵɵtext(76, \"Nom du Model\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"input\", 40, 41);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_input_ngModelChange_77_listener($event) {\n            return ctx.newModal1.nomModel = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(79, ModelComponent_div_79_Template, 3, 2, \"div\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"label\", 42);\n          i0.ɵɵtext(81, \"Marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"select\", 43, 44);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_select_ngModelChange_82_listener($event) {\n            return ctx.newModal1.marque = $event;\n          });\n          i0.ɵɵelementStart(84, \"option\", 24);\n          i0.ɵɵtext(85, \"S\\u00E9lectionner une marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(86, ModelComponent_option_86_Template, 2, 2, \"option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(87, ModelComponent_div_87_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementStart(88, \"label\", 42);\n          i0.ɵɵtext(89, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"select\", 45, 46);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_select_ngModelChange_90_listener($event) {\n            return ctx.newModal1.fournisseur = $event;\n          });\n          i0.ɵɵelementStart(92, \"option\", 24);\n          i0.ɵɵtext(93, \"S\\u00E9lectionner Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(94, ModelComponent_option_94_Template, 2, 2, \"option\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(95, ModelComponent_div_95_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementStart(96, \"label\", 47);\n          i0.ɵɵtext(97, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"textarea\", 48);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_textarea_ngModelChange_98_listener($event) {\n            return ctx.newModal1.specification = $event;\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(99, \"div\", 49)(100, \"button\", 50);\n          i0.ɵɵtext(101, \"Annuler\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"button\", 51);\n          i0.ɵɵlistener(\"click\", function ModelComponent_Template_button_click_102_listener() {\n            i0.ɵɵrestoreView(_r41);\n            const _r7 = i0.ɵɵreference(73);\n            return i0.ɵɵresetView(ctx.onUpdateClick(_r7));\n          });\n          i0.ɵɵtext(103, \" \\uD83D\\uDCBE Sauvegarder \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(104, \"div\", 52);\n          i0.ɵɵtemplate(105, ModelComponent_div_105_Template, 2, 2, \"div\", 53);\n          i0.ɵɵtemplate(106, ModelComponent_div_106_Template, 14, 3, \"div\", 54);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(107, \"br\")(108, \"br\");\n          i0.ɵɵelementStart(109, \"div\", 55)(110, \"div\", 56)(111, \"div\", 57)(112, \"div\", 58)(113, \"div\")(114, \"h4\", 59);\n          i0.ɵɵtext(115, \"Liste Des Models\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(116, \"p\", 60);\n          i0.ɵɵtext(117, \" visualiser les models disponibles \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(118, \"div\", 61)(119, \"select\", 62)(120, \"option\", 63);\n          i0.ɵɵtext(121, \"March 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"option\", 64);\n          i0.ɵɵtext(123, \"March 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"option\", 65);\n          i0.ɵɵtext(125, \"March 2025\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(126, \"br\");\n          i0.ɵɵelementStart(127, \"div\", 66)(128, \"div\", 67)(129, \"div\", 68)(130, \"input\", 69);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_input_ngModelChange_130_listener($event) {\n            return ctx.searchText = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(131, \"span\", 70);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(132, \"div\")(133, \"select\", 71);\n          i0.ɵɵlistener(\"ngModelChange\", function ModelComponent_Template_select_ngModelChange_133_listener($event) {\n            return ctx.selectedMarqueName = $event;\n          });\n          i0.ɵɵelementStart(134, \"option\", 72);\n          i0.ɵɵtext(135, \"Filtrer par marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(136, ModelComponent_option_136_Template, 2, 2, \"option\", 73);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(137, \"div\", 74)(138, \"table\", 75)(139, \"thead\")(140, \"tr\")(141, \"th\", 76);\n          i0.ɵɵtext(142, \" Nom Modele \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(143, \"th\", 76);\n          i0.ɵɵtext(144, \"Marque Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(145, \"th\", 76);\n          i0.ɵɵtext(146, \"Type Associes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(147, \"th\", 76);\n          i0.ɵɵtext(148, \" Sp\\u00E9cifications \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"th\", 77);\n          i0.ɵɵtext(150, \" Actions \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(151, \"tbody\");\n          i0.ɵɵtemplate(152, ModelComponent_tr_152_Template, 20, 4, \"tr\", 78);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(153, \"div\", 79)(154, \"p\", 80);\n          i0.ɵɵtext(155, \"Design and Developed by \");\n          i0.ɵɵelementStart(156, \"a\", 81);\n          i0.ɵɵtext(157, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(158, \" Distributed by \");\n          i0.ɵɵelementStart(159, \"a\", 82);\n          i0.ɵɵtext(160, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r7 = i0.ɵɵreference(73);\n          const _r8 = i0.ɵɵreference(78);\n          const _r10 = i0.ɵɵreference(83);\n          const _r13 = i0.ɵɵreference(91);\n          i0.ɵɵadvance(28);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(33, _c0, ctx.isModalOpen));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal.nomModel);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.signupErrors.nomModel);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal.marque);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.marques);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.signupErrors.marque);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.newModal.marque == null ? null : ctx.newModal.marque.types == null ? null : ctx.newModal.marque.types.length);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal.fournisseur);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.signupErrors.fournisseur);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal.specification);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal1.nomModel);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r8.invalid && _r8.touched);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal1.marque)(\"compareWith\", ctx.compareMarques);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.marques);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r10.invalid && _r10.touched);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal1.fournisseur)(\"compareWith\", ctx.compareFournisseurs);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngValue\", null);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fournisseurs);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r13.invalid && _r13.touched);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.newModal1.specification);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"disabled\", _r7.invalid);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.marques);\n          i0.ɵɵadvance(24);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchText);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedMarqueName);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.marques);\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredModels);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.NgModel, i3.NgForm, i4.LayoutComponent],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #000000; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #000000;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n\\n\\n\\nselect[multiple][_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;  \\n\\n  padding: 5px;\\n  border-radius: 5px;\\n  border: 1px solid #ccc;\\n  font-size: 14px;\\n  background-color: white;\\n  box-sizing: border-box;\\n  \\n\\n  overflow-y: auto;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \".card1[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 190px;\\n    padding: 20px;\\n    background-color: #fff;\\n    border-radius: 12px;\\n    box-shadow: 0 0 0 1px #e5e7eb;\\n    font-family: 'Segoe UI', sans-serif;\\n    font-size: 14px;\\n    display: flex;\\n    flex-direction: column;\\n    gap: 14px;\\n    margin-top: 20px;\\n    margin-left: 20px;\\n  }\\n\\n  .card1-icon[_ngcontent-%COMP%] {\\n    background-color: #e0edff;\\n    color: #2563eb;\\n    padding: 6px;\\n    border-radius: 8px;\\n    width: 43px;\\n    height: 43px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .card1-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 25px;\\n    height: 25px;\\n  }\\n\\n  .card1-title[_ngcontent-%COMP%] {\\n    font-weight: 600;\\n    color: #111827;\\n    font-size: 20px;\\n    margin: 0;\\n  }\\n\\n  .card1-date[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n    color: #9ca3af;\\n    margin: 2px 0 0 0;\\n  }\\n\\n  .card1-desc[_ngcontent-%COMP%] {\\n    color: #4b5563;\\n    margin: 0;\\n    font-size: 16px;\\n  }\\n\\n  .card1-badge[_ngcontent-%COMP%] {\\n    background-color: #ffffff;\\n    color: #0d00ff;\\n    padding: 4px 10px;\\n    border-radius: 990px;\\n    font-size: 20px;\\n    font-weight: 600;\\n  }\\n\\n.card1-button[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  cursor: pointer;\\n  color: #000000; \\n\\n  font-weight: 500;;      \\n\\n\\n\\n}\\n\\n\\n  .card1-flex[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n  }\\n\\n  .card1-footer[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    background-color: white;\\n    padding: 10px;\\n    border-radius: 6px;\\n    border: 0px solid #e5e7eb;\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["bootstrap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "signupErrors", "nomModel", "ɵɵproperty", "marque_r20", "ɵɵtextInterpolate1", "nomMarque", "ctx_r3", "marque", "type_r22", "nomType", "ɵɵlistener", "ModelComponent_div_50_Template_select_ngModelChange_3_listener", "$event", "ɵɵrestoreView", "_r24", "ctx_r23", "ɵɵnextContext", "ɵɵresetView", "newModal", "typeAssociee", "ɵɵtemplate", "ModelComponent_div_50_option_6_Template", "ctx_r4", "types", "fournisseur_r25", "nomFournisseur", "ctx_r6", "<PERSON><PERSON><PERSON><PERSON>", "ModelComponent_div_79_div_1_Template", "ModelComponent_div_79_div_2_Template", "_r8", "errors", "marque_r28", "fournisseur_r29", "ctx_r16", "notification", "type", "message", "ModelComponent_div_106_span_8_span_2_Template", "type_r32", "i_r33", "marque_r30", "length", "ModelComponent_div_106_span_8_Template", "models", "marque_r36", "ModelComponent_tr_152_Template_button_click_16_listener", "restoredCtx", "_r39", "model_r37", "$implicit", "ctx_r38", "openModal1", "ModelComponent_tr_152_Template_button_click_18_listener", "ctx_r40", "confirmDelete", "idModel", "specification", "ModelComponent", "constructor", "authservice", "marques", "searchText", "isModalOpen", "fournisseurs", "show", "equipements", "newModal1", "selectedMarqueName", "ngOnInit", "GetAllMarques", "GetAllModels", "GetAllFournisseur", "closeOnOutsideClick", "event", "target", "classList", "contains", "closeModal", "openModal", "showNotification", "setTimeout", "hideNotification", "filteredModels", "filter", "model", "matchMarque", "matchSearch", "toLowerCase", "includes", "getallFournisseur", "subscribe", "data", "getAllMarques", "deleteModel", "id", "ModelId", "console", "log", "window", "scrollTo", "top", "behavior", "Modal", "modalElement", "document", "getElementById", "modal", "error", "onUpdateClick", "form", "invalid", "mark<PERSON>llAsTouched", "updateData", "closeModal1", "getInstance", "hide", "updateModel", "response", "compareMarques", "m1", "m2", "idMarque", "compareFournisseurs", "idFournisseur", "getAllModel", "resetErrors", "validateSignup", "<PERSON><PERSON><PERSON><PERSON>", "trim", "onRegister", "addModel", "next", "push", "ɵɵdirectiveInject", "i1", "TypeService", "selectors", "decls", "vars", "consts", "template", "ModelComponent_Template", "rf", "ctx", "ɵɵelement", "ModelComponent_Template_button_click_24_listener", "ModelComponent_Template_div_click_28_listener", "ModelComponent_Template_div_click_29_listener", "stopPropagation", "ModelComponent_Template_span_click_30_listener", "ModelComponent_Template_form_ngSubmit_34_listener", "ModelComponent_Template_input_ngModelChange_39_listener", "ModelComponent_div_40_Template", "ModelComponent_Template_select_ngModelChange_45_listener", "ModelComponent_option_48_Template", "ModelComponent_div_49_Template", "ModelComponent_div_50_Template", "ModelComponent_Template_select_ngModelChange_55_listener", "ModelComponent_option_58_Template", "ModelComponent_div_59_Template", "ModelComponent_Template_textarea_ngModelChange_62_listener", "ModelComponent_Template_input_ngModelChange_77_listener", "ModelComponent_div_79_Template", "ModelComponent_Template_select_ngModelChange_82_listener", "ModelComponent_option_86_Template", "ModelComponent_div_87_Template", "ModelComponent_Template_select_ngModelChange_90_listener", "ModelComponent_option_94_Template", "ModelComponent_div_95_Template", "ModelComponent_Template_textarea_ngModelChange_98_listener", "ModelComponent_Template_button_click_102_listener", "_r41", "_r7", "ɵɵreference", "ModelComponent_div_105_Template", "ModelComponent_div_106_Template", "ModelComponent_Template_input_ngModelChange_130_listener", "ModelComponent_Template_select_ngModelChange_133_listener", "ModelComponent_option_136_Template", "ModelComponent_tr_152_Template", "ɵɵpureFunction1", "_c0", "touched", "_r10", "_r13"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\model\\model.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\model\\model.component.html"], "sourcesContent": ["import { Component,OnInit } from '@angular/core';\r\nimport { Marque } from '../marque/Marque';\r\nimport { TypeService } from '../dashboard/type.service';\r\nimport { Model } from './Model';\r\nimport * as bootstrap from 'bootstrap';\r\n// or for just Modal:\r\nimport { Modal } from 'bootstrap';\r\nimport { Fournisseur } from '../fournisseur/Fournisseur';\r\nimport { NgForm } from '@angular/forms';\r\n@Component({\r\n  selector: 'app-model',\r\n  templateUrl: './model.component.html',\r\n  styleUrls: ['./model.component.css']\r\n})\r\nexport class ModelComponent implements OnInit {\r\nmarques:Marque[]=[];\r\nmodels:Model[]=[];\r\nsearchText: string = '';\r\n  isModalOpen = false;\r\n  fournisseurs: Fournisseur[] = [];\r\n\r\n// Notification system\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\nnewModal: Model = {\r\n  idModel: 0,\r\n  nomModel: '',\r\n  specification: '',\r\n  marque: null,\r\n  equipements: [],\r\n  fournisseur: null,\r\n  typeAssociee: ''\r\n};\r\n\r\n\r\nnewModal1: Model = {\r\n  idModel: 0,\r\n  nomModel: '',\r\n  specification: '',\r\n  marque: null,\r\n  equipements: [],\r\n  fournisseur: null,\r\n  typeAssociee: ''\r\n}\r\n\r\n\r\n\r\nconstructor(private authservice:TypeService) { }\r\n  ngOnInit(): void {\r\n\r\n    this.GetAllMarques();\r\n  \r\n    this.GetAllModels();\r\n    this.GetAllFournisseur();\r\n    }\r\n      closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n    openModal() {\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\nselectedMarqueName: string = ''; // Holds selected marque name (or id)\r\n\r\n\r\nget filteredModels() {\r\n  return this.models.filter(model => {\r\n    const matchMarque = !this.selectedMarqueName || model.marque?.nomMarque === this.selectedMarqueName;\r\n    const matchSearch = !this.searchText || model.nomModel.toLowerCase().includes(this.searchText.toLowerCase());\r\n    return matchMarque && matchSearch;\r\n  });\r\n}\r\n\r\nGetAllFournisseur()\r\n{\r\n  this.authservice.getallFournisseur().subscribe(data => {\r\n  this.fournisseurs =data;\r\n \r\n});\r\n}\r\n\r\n\r\n    GetAllMarques()\r\n    {\r\n      this.authservice.getAllMarques().subscribe(data => {\r\n      this.marques = data;\r\n   \r\n    });\r\n    }\r\n\r\n  deleteModel(id: number) {\r\n    this.authservice.deleteModel(id).subscribe(() => {\r\n      this.models = this.models.filter(model => model.idModel !== id);\r\n    });\r\n  }\r\n    confirmDelete(ModelId: number): void {\r\n    console.log(ModelId);\r\n       window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    this.showNotification('success', 'Modèle supprimer avec succès');\r\n      this.deleteModel(ModelId);\r\n \r\n  }\r\n\r\n  openModal1(Modal: Model) {\r\n       this.signupErrors.nomModel = '';\r\n             this.signupErrors.marque = '';\r\n    this.newModal1 = { ...Modal };\r\n    const modalElement = document.getElementById('updateModal');\r\n    if (modalElement) {\r\n      const modal = new bootstrap.Modal(modalElement);\r\n      modal.show();\r\n    } else {\r\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\r\n    }\r\n  }\r\n  onUpdateClick(form: NgForm) {\r\n  if (form.invalid) {\r\n    form.form.markAllAsTouched(); // pour forcer l'affichage des erreurs\r\n    return;\r\n  }\r\n\r\n  this.updateData(); // méthode existante\r\n}\r\n\r\n\r\ncloseModal1() {\r\n  const modalElement = document.getElementById('updateModal');\r\n  if (modalElement) {\r\n    const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\r\n    modal.hide();\r\n  }\r\n}\r\n\r\n  updateData() {\r\n    console.log('Données mises à jour:', this.newModal1);\r\n    this.authservice.updateModel(this.newModal1).subscribe(\r\n      (response) => {\r\n        console.log('Update successful:', response);\r\n        this.showNotification('success', 'Modèle modifié avec succès');\r\n        this.closeModal1();\r\n        this.GetAllModels();\r\n\r\n        window.scrollTo({ top: 0, behavior: 'smooth' });\r\n\r\n      },\r\n      (error) => {\r\n        console.error('Update failed:', error);\r\n        this.showNotification('error', 'Erreur lors de la modification');\r\n      }\r\n    );\r\n  }\r\n\r\ncompareMarques(m1: Marque, m2: Marque): boolean {\r\n  return m1 && m2 ? m1.idMarque === m2.idMarque : m1 === m2;\r\n}\r\n\r\ncompareFournisseurs(m1: Fournisseur, m2: Fournisseur): boolean {\r\n  return m1 && m2 ? m1.idFournisseur === m2.idFournisseur : m1 === m2;\r\n}\r\n\r\n\r\n    GetAllModels()\r\n    {\r\n      this.authservice.getAllModel().subscribe(data => {\r\n      this.models = data;\r\n   \r\n    });\r\n    }\r\n    \r\n  signupErrors: any = {};\r\n    \r\n  resetErrors() {\r\n    this.signupErrors = {};\r\n  }\r\n\r\n  validateSignup(): boolean {\r\n    this.resetErrors();\r\n    let isValid = true;\r\n\r\n    // Username\r\n    if (!this.newModal.nomModel || this.newModal.nomModel.trim().length === 0) {\r\n      this.signupErrors.nomModel = 'Le nom de model est requis';\r\n      isValid = false;\r\n    } else if (this.newModal.nomModel.length < 3) {\r\n      this.signupErrors.nomModel = 'Le nom de model doit contenir au moins 3 caractères';\r\n      isValid = false;\r\n    }\r\n\r\nif (!this.newModal.specification || this.newModal.specification.trim().length === 0) {\r\n      this.signupErrors.specification = 'La specification de type est requis';\r\n      isValid = false;\r\n    } else if (this.newModal.specification.length < 3) {\r\n      this.signupErrors.specification = 'La specification doit contenir au moins 3 caractères';\r\n      isValid = false;\r\n    }\r\n  \r\nif(this.newModal.marque==null){\r\n      this.signupErrors.marque = 'La marque de type est requis';\r\n      isValid = false;\r\n    }\r\n  \r\n  if(this.newModal.typeAssociee==null){\r\n      this.signupErrors.marque = '  type est requis';\r\n      isValid = false;\r\n    }\r\n\r\n\r\n    \r\n\r\n    return isValid;\r\n  }\r\n\r\n\r\n  onRegister(): void {\r\n  if (!this.validateSignup()) {\r\n    return; \r\n  }\r\n  console.log('User Data:', this.newModal);\r\n\r\n  this.authservice.addModel(this.newModal).subscribe({\r\n\r\n    next: (response) => {\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Modèle ajouté avec succès');\r\n\r\n      // 🟢 Mettre à jour le tableau local pour affichage immédiat\r\n      this.models.push(response);\r\n\r\n      // 🧹 Réinitialiser le formulaire et fermer le modal\r\n     \r\n      this.closeModal();\r\n             window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      this.showNotification('error', 'Erreur lors de l\\'ajout');\r\n    }\r\n  });\r\n}\r\n\r\n\r\n\r\n}\r\n", "\r\n<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\r\n  \r\n</head>\r\n\r\n<body>\r\n  <!--  Body Wrapper -->\r\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\r\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\r\n\r\n    <!--  App Topstrip -->\r\n    \r\n    <!-- Sidebar Start -->\r\n<app-layout></app-layout>\r\n\r\n\r\n    <!--  Sidebar End -->\r\n    <!--  Main wrapper -->\r\n    <div class=\"body-wrapper\">\r\n      <!--  Header Start -->\r\n      <header class=\"app-header\">\r\n\r\n      </header>\r\n      <!--  Header End -->\r\n      <div class=\"body-wrapper-inner\">\r\n        <div class=\"container-fluid\">\r\n                <div class=\"welcome-header\">\r\n  <h1>Bienvenue dans votre espace</h1>\r\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\r\n\r\n</div>\r\n<!-- Bouton pour ouvrir la modale -->\r\n\r\n<div class=\"header-container\">\r\n  <div class=\"header-text\">\r\n    <h2>Marques</h2>\r\n    <p>Gérez les marques d'équipements par type\r\n\r\n\r\n\r\n</p>\r\n  </div>\r\n<button class=\"add-user-btn\" (click)=\"openModal()\" >\r\n  <span class=\"icon\">+</span>Nouveau Model\r\n\r\n</button>\r\n</div>\r\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\" >Ajouter un nouveau Type</h3>\r\n\r\n   <form (ngSubmit)=\"onRegister()\" #userForm=\"ngForm\" novalidate>\r\n<br>\r\n\r\n  <label style=\"font-size: 14px;   font-weight: 500; color: #000000; margin-bottom:-40px\" for=\"email\">Nom du model</label>\r\n<input\r\nclass=\"form-inputp\"\r\n  id=\"nomType\"\r\n  type=\"text\"\r\n  name=\"nomType\"\r\n  [(ngModel)]=\"newModal.nomModel\"\r\n  placeholder=\"Ex: Latitude 5520, Elitebook 845G...\"\r\n  required\r\n  \r\n>\r\n\r\n<div *ngIf=\"signupErrors.nomModel\" style=\"color:red\">{{ signupErrors.nomModel }}</div>\r\n\r\n<br>\r\n  <label style=\"font-size: 14px; font-weight: 500; color: #000000;\" for=\"type\">Marque</label>\r\n  <br>\r\n<select\r\n  class=\"form-inputp\"\r\n  id=\"type\"\r\n  name=\"marque\"\r\n  [(ngModel)]=\"newModal.marque\"\r\n  style=\"width: 100%;\"\r\n  required\r\n>\r\n  <!-- ✅ Option placeholder -->\r\n  <option [ngValue]=\"null\" disabled hidden>Sélectionner une marque</option>\r\n\r\n  <!-- ✅ Liste des vraies marques -->\r\n  <option *ngFor=\"let marque of marques\" [ngValue]=\"marque\">\r\n    {{ marque.nomMarque }}\r\n  </option>\r\n</select>\r\n<div *ngIf=\"signupErrors.marque\" style=\"color:red\">{{ signupErrors.marque }}</div>\r\n\r\n<div *ngIf=\"newModal.marque?.types?.length\">\r\n  <label style=\"font-size: 14px; font-weight: 500; color: #000000; margin-top: 10px;\">Type associé</label>\r\n  <select\r\n    class=\"form-inputp\"\r\n    [(ngModel)]=\"newModal.typeAssociee\"\r\n    name=\"selectedType\"\r\n    style=\"width: 100%;\"\r\n    required\r\n  >\r\n    <option [value]=\"null\" disabled hidden>Sélectionner un type</option>\r\n    <option *ngFor=\"let type of newModal.marque?.types\" [value]=\"type.nomType\">\r\n      {{ type.nomType }}\r\n    </option>\r\n  </select>\r\n</div>\r\n<br>\r\n  <label style=\"font-size: 14px; font-weight: 500; color: #000000;\" for=\"type\">Fournisseur</label>\r\n  <br>\r\n<select\r\n  class=\"form-inputp\"\r\n  id=\"type\"\r\n  name=\"fourniseeur\"\r\n  [(ngModel)]=\"newModal.fournisseur\"\r\n  style=\"width: 100%;\"\r\n  required\r\n>\r\n <option [ngValue]=\"null\" disabled hidden>Sélectionner fournisseur</option>\r\n  <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\r\n    {{ fournisseur.nomFournisseur }}\r\n  </option>\r\n</select>\r\n<div *ngIf=\"signupErrors.fournisseur\" style=\"color:red\">{{ signupErrors.fournisseur }}</div>\r\n  <label style=\"font-size: 14px;   font-weight: 500; color: #000000;\" for=\"email\">Description</label>\r\n<textarea\r\n  id=\"description\"\r\n  name=\"description\"\r\n  [(ngModel)]=\"newModal.specification\"\r\n  placeholder=\"Ex: Intel i5, 8GB RAM, 256GB SSD...\"\r\n  rows=\"3\"\r\n  cols=\"60\"\r\n  \r\n  \r\n></textarea>\r\n\r\n\r\n\r\n\r\n\r\n  <button type=\"submit\">Enregistrer</button>\r\n</form>\r\n\r\n  </div>\r\n</div>\r\n\r\n<div class=\"modal fade\" id=\"updateModal\" tabindex=\"-1\" aria-labelledby=\"updateModalLabel\" aria-hidden=\"true\" data-bs-backdrop=\"false\">\r\n  <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n    <div class=\"modal-content shadow rounded-4\">\r\n\r\n      <h5 id=\"updateModalLabel\">📝 Modifier les informations</h5>\r\n      <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n\r\n      <div class=\"modal-body\">\r\n        <form #updateForm=\"ngForm\">\r\n\r\n          <!-- Nom du modèle -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"nomModel\" class=\"form-label fw-semibold fs-5\">Nom du Model</label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-inputp\"\r\n              id=\"nomModel\"\r\n              name=\"nomModel\"\r\n              [(ngModel)]=\"newModal1.nomModel\"\r\n              #nomModel=\"ngModel\"\r\n              required\r\n              minlength=\"2\"\r\n            />\r\n            <div *ngIf=\"nomModel.invalid && nomModel.touched\" style=\"color:red\">\r\n              <div *ngIf=\"nomModel.errors?.['required']\">Le nom est requis</div>\r\n              <div *ngIf=\"nomModel.errors?.['minlength']\">Minimum 2 caractères</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Marque -->\r\n          <label class=\"form-label\" for=\"type\">Marque</label>\r\n          <select\r\n            class=\"form-inputp\"\r\n            id=\"type\"\r\n            name=\"marque\"\r\n            [(ngModel)]=\"newModal1.marque\"\r\n            #marque=\"ngModel\"\r\n            [compareWith]=\"compareMarques\"\r\n            required\r\n          >\r\n            <option [ngValue]=\"null\" disabled hidden>Sélectionner une marque</option>\r\n            <option *ngFor=\"let marque of marques\" [ngValue]=\"marque\">\r\n              {{ marque.nomMarque }}\r\n            </option>\r\n          </select>\r\n          <div *ngIf=\"marque.invalid && marque.touched\" style=\"color:red\">La marque est requise</div>\r\n\r\n          <!-- Fournisseur -->\r\n          <label class=\"form-label\" for=\"type\">Fournisseur</label>\r\n          <select\r\n            class=\"form-inputp\"\r\n            id=\"type\"\r\n            name=\"fournisseur\"\r\n            [(ngModel)]=\"newModal1.fournisseur\"\r\n            #fournisseur=\"ngModel\"\r\n            [compareWith]=\"compareFournisseurs\"\r\n            required\r\n          >\r\n            <option [ngValue]=\"null\" disabled hidden>Sélectionner Fournisseur</option>\r\n            <option *ngFor=\"let fournisseur of fournisseurs\" [ngValue]=\"fournisseur\">\r\n              {{ fournisseur.nomFournisseur }}\r\n            </option>\r\n          </select>\r\n          <div *ngIf=\"fournisseur.invalid && fournisseur.touched\" style=\"color:red\">Le fournisseur est requis</div>\r\n\r\n          <!-- Description -->\r\n          <label class=\"form-label\" for=\"description\">Description</label>\r\n          <textarea\r\n            id=\"description\"\r\n            name=\"description\"\r\n            [(ngModel)]=\"newModal1.specification\"\r\n            rows=\"3\"\r\n            cols=\"60\"\r\n            class=\"form-control\"\r\n          ></textarea>\r\n\r\n        </form>\r\n      </div>\r\n\r\n      <!-- Boutons -->\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">Annuler</button>\r\n        <button\r\n          type=\"button\"\r\n          class=\"btn btn-success px-4\"\r\n          [disabled]=\"updateForm.invalid\"\r\n          (click)=\"onUpdateClick(updateForm)\">\r\n          💾 Sauvegarder\r\n        </button>\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n          <!--  Row 1 -->\r\n          <div class=\"row\">\r\n           \r\n    <style>\r\n  .card1 {\r\n    width: 280px;\r\n    height: 190px;\r\n    padding: 20px;\r\n    background-color: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 0 0 1px #e5e7eb;\r\n    font-family: 'Segoe UI', sans-serif;\r\n    font-size: 14px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 14px;\r\n    margin-top: 20px;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .card1-icon {\r\n    background-color: #e0edff;\r\n    color: #2563eb;\r\n    padding: 6px;\r\n    border-radius: 8px;\r\n    width: 43px;\r\n    height: 43px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .card1-icon svg {\r\n    width: 25px;\r\n    height: 25px;\r\n  }\r\n\r\n  .card1-title {\r\n    font-weight: 600;\r\n    color: #111827;\r\n    font-size: 20px;\r\n    margin: 0;\r\n  }\r\n\r\n  .card1-date {\r\n    font-size: 15px;\r\n    color: #9ca3af;\r\n    margin: 2px 0 0 0;\r\n  }\r\n\r\n  .card1-desc {\r\n    color: #4b5563;\r\n    margin: 0;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .card1-badge {\r\n    background-color: #ffffff;\r\n    color: #0d00ff;\r\n    padding: 4px 10px;\r\n    border-radius: 990px;\r\n    font-size: 20px;\r\n    font-weight: 600;\r\n  }\r\n\r\n.card1-button {\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background-color: #fff;\r\n  cursor: pointer;\r\n  color: #000000; /* Darker gray (high opacity) */\r\n  font-weight: 500;;      /* ⬅️ makes text bold */\r\n\r\n\r\n}\r\n\r\n\r\n  .card1-flex {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .card1-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: white;\r\n    padding: 10px;\r\n    border-radius: 6px;\r\n    border: 0px solid #e5e7eb;\r\n  }\r\n  \r\n</style>\r\n\r\n<!-- Simple Notification Bar -->\r\n<div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n  {{ notification.message }}\r\n</div>\r\n\r\n<div *ngFor=\"let marque of marques\" class=\"card1\">\r\n  <div class=\"card1-flex\">\r\n   \r\n\r\n    <div>\r\n      <p class=\"card1-title\">{{ marque.nomMarque }}</p>\r\n      <p class=\"card1-date\">Créé le 15/01/2024</p> <!-- ou {{ type.dateCreation }} si tu veux le rendre dynamique -->\r\n    </div>\r\n  </div>\r\n\r\n  <p class=\"card1-desc\">\r\n  <span *ngFor=\"let type of marque.types; let i = index\">\r\n    {{ type.nomType }}<span *ngIf=\"i < marque.types.length - 1\">, </span>\r\n  </span>\r\n</p>\r\n\r\n  <div class=\"card1-footer\">\r\n   <span class=\"card1-badge\">{{ marque.models.length }}</span>\r\n<div>\r\n    model\r\n</div>\r\n</div>\r\n</div>\r\n\r\n\r\n\r\n       \r\n\r\n          </div>\r\n          \r\n<br>\r\n<br>\r\n                <div class=\"col-12\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\" style=\"\">\r\n                  <div class=\"d-md-flex align-items-center\">\r\n                    <div>\r\n                      <h4 class=\"card-title\">Liste Des Models</h4>\r\n                      <p class=\"card-subtitle\">\r\n                    visualiser les models disponibles\r\n                      </p>\r\n                    </div>\r\n            <div class=\"ms-auto mt-3 mt-md-0\">\r\n                      <select class=\"form-select theme-select border-0\" aria-label=\"Default select example\">\r\n                        <option value=\"1\">March 2025</option>\r\n                        <option value=\"2\">March 2025</option>\r\n                        <option value=\"3\">March 2025</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n                  <br>\r\n<div class=\"d-flex align-items-center gap-2 mt-3\">\r\n  <!-- Champ de recherche -->\r\n  <div class=\"search-wrapper flex-grow-1\">\r\n    <div class=\"custom-search\">\r\n      <input type=\"text\"  placeholder=\"Rechercher un model...\" class=\"form-control\" [(ngModel)]=\"searchText\" />\r\n      <span class=\"icon-search\"></span>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Select à côté -->\r\n  <div>\r\n<select class=\"form-select\" style=\"min-width: 200px;\" [(ngModel)]=\"selectedMarqueName\">\r\n  <option value=\"\">Filtrer par marque</option>\r\n  <option *ngFor=\"let marque of marques\" [value]=\"marque.nomMarque\">{{ marque.nomMarque }}</option>\r\n</select>\r\n\r\n\r\n  </div>\r\n</div>\r\n\r\n                  <div class=\"table-responsive mt-4\">\r\n                    <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\r\n                      <thead>\r\n                        <tr>\r\n                          <th scope=\"col\" class=\"px-0 text-muted\">\r\n                            Nom Modele\r\n                          </th>\r\n                          <th scope=\"col\" class=\"px-0 text-muted\">Marque Name</th>\r\n\r\n                           <th scope=\"col\" class=\"px-0 text-muted\">Type Associes</th>\r\n                        \r\n                           <th scope=\"col\" class=\"px-0 text-muted\">\r\n                            Spécifications\t\r\n                          </th>\r\n                         \r\n                          <th scope=\"col\" class=\"px-0 text-muted text-end\">\r\n                            Actions\r\n                          </th>\r\n                        </tr>\r\n                      </thead>\r\n               <tbody>\r\n      <tr *ngFor=\"let model of filteredModels\">\r\n    <td class=\"px-1\">\r\n     <div class=\"ms-3\">\r\n     <h5 class=\"mb-0 fw-bolder\" style=\"font-size: 18px; color: #2c3e50;\">{{ model.nomModel }}</h5>\r\n      </div>\r\n    </td>\r\n\r\n    <td class=\"px-0\"> <span\r\n      class=\"badge bg-info me-1\">\r\n      {{ model.marque?.nomMarque}}\r\n    </span></td>\r\n <td class=\"px-0\"> <span\r\n      class=\"badge bg-info me-1\">\r\n      {{ model.typeAssociee}}\r\n    </span></td>\r\n\r\n\r\n   <td class=\"px-0\">\r\n \r\n        <div class=\"ms-3\">\r\n          <h6 class=\"mb-0 fw-bolder\">{{ model.specification }}</h6>\r\n         \r\n     \r\n      </div>\r\n</td>\r\n\r\n  <td class=\"text-end\">\r\n  <button class=\"btn btn-smc\" (click)=\"openModal1(model)\"  title=\"Modifier\" style=\"color:blue; font-size: 18px; border: none; background: none;\">\r\n    ✏️\r\n  </button>\r\n  <button class=\"btn btn-sm\"(click)=\"confirmDelete(model.idModel)\" title=\"Supprimer\" style=\"color:red; font-size: 18px; border: none; background: none;\">\r\n    🗑️\r\n  </button>\r\n</td>\r\n\r\n\r\n\r\n\r\n  </tr>\r\n\r\n\r\n</tbody>\r\n\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          \r\n          <div class=\"py-6 px-6 text-center\">\r\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\r\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\r\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\r\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\r\n  <script src=\"./assets/js/app.min.js\"></script>\r\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\r\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\r\n  <script src=\"./assets/js/dashboard.js\"></script>\r\n  <!-- solar icons -->\r\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\r\n</body>\r\n\r\n</html>"], "mappings": "AAIA,OAAO,KAAKA,SAAS,MAAM,WAAW;;;;;;;;ICqEtCC,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAjCH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAAC,QAAA,CAA2B;;;;;IAiB9ER,EAAA,CAAAC,cAAA,iBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF8BH,EAAA,CAAAS,UAAA,YAAAC,UAAA,CAAkB;IACvDV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAD,UAAA,CAAAE,SAAA,MACF;;;;;IAEFZ,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAA/BH,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAQ,MAAA,CAAAN,YAAA,CAAAO,MAAA,CAAyB;;;;;IAYxEd,EAAA,CAAAC,cAAA,iBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF2CH,EAAA,CAAAS,UAAA,UAAAM,QAAA,CAAAC,OAAA,CAAsB;IACxEhB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAI,QAAA,CAAAC,OAAA,MACF;;;;;;IAZJhB,EAAA,CAAAC,cAAA,UAA4C;IAC0CD,EAAA,CAAAE,MAAA,wBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxGH,EAAA,CAAAC,cAAA,iBAMC;IAJCD,EAAA,CAAAiB,UAAA,2BAAAC,+DAAAC,MAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAtB,EAAA,CAAAuB,aAAA;MAAA,OAAavB,EAAA,CAAAwB,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAC,YAAA,GAAAP,MAAA,CACZ;IAAA,EADkC;IAKnCnB,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAA2B,UAAA,IAAAC,uCAAA,qBAES;IACX5B,EAAA,CAAAG,YAAA,EAAS;;;;IATPH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAS,UAAA,YAAAoB,MAAA,CAAAJ,QAAA,CAAAC,YAAA,CAAmC;IAK3B1B,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAS,UAAA,eAAc;IACGT,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAS,UAAA,YAAAoB,MAAA,CAAAJ,QAAA,CAAAX,MAAA,kBAAAe,MAAA,CAAAJ,QAAA,CAAAX,MAAA,CAAAgB,KAAA,CAAyB;;;;;IAiBpD9B,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAS,UAAA,YAAAsB,eAAA,CAAuB;IACtE/B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAAoB,eAAA,CAAAC,cAAA,MACF;;;;;IAEFhC,EAAA,CAAAC,cAAA,cAAwD;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAApCH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAK,iBAAA,CAAA4B,MAAA,CAAA1B,YAAA,CAAA2B,WAAA,CAA8B;;;;;IA+CxElC,EAAA,CAAAC,cAAA,UAA2C;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAClEH,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,gCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFxEH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAA2B,UAAA,IAAAQ,oCAAA,kBAAkE;IAClEnC,EAAA,CAAA2B,UAAA,IAAAS,oCAAA,kBAAsE;IACxEpC,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAS,UAAA,SAAA4B,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAmC;IACnCtC,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAS,UAAA,SAAA4B,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,cAAoC;;;;;IAgB5CtC,EAAA,CAAAC,cAAA,iBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAF8BH,EAAA,CAAAS,UAAA,YAAA8B,UAAA,CAAkB;IACvDvC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAA4B,UAAA,CAAA3B,SAAA,MACF;;;;;IAEFZ,EAAA,CAAAC,cAAA,cAAgE;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAczFH,EAAA,CAAAC,cAAA,iBAAyE;IACvED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFwCH,EAAA,CAAAS,UAAA,YAAA+B,eAAA,CAAuB;IACtExC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAA6B,eAAA,CAAAR,cAAA,MACF;;;;;IAEFhC,EAAA,CAAAC,cAAA,cAA0E;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAwInHH,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAS,UAAA,YAAAgC,OAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtF3C,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAA8B,OAAA,CAAAC,YAAA,CAAAE,OAAA,OACF;;;;;IAcsB5C,EAAA,CAAAC,cAAA,WAA0C;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADvEH,EAAA,CAAAC,cAAA,WAAuD;IACrDD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAA2B,UAAA,IAAAkB,6CAAA,mBAAmD;IACvE7C,EAAA,CAAAG,YAAA,EAAO;;;;;;IADLH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAW,kBAAA,MAAAmC,QAAA,CAAA9B,OAAA,KAAkB;IAAOhB,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAS,UAAA,SAAAsC,KAAA,GAAAC,UAAA,CAAAlB,KAAA,CAAAmB,MAAA,KAAiC;;;;;IAZ9DjD,EAAA,CAAAC,cAAA,cAAkD;IAKrBD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjDH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,mCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIhDH,EAAA,CAAAC,cAAA,YAAsB;IACtBD,EAAA,CAAA2B,UAAA,IAAAuB,sCAAA,mBAEO;IACTlD,EAAA,CAAAG,YAAA,EAAI;IAEFH,EAAA,CAAAC,cAAA,cAA0B;IACCD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,WAAK;IACDD,EAAA,CAAAE,MAAA,gBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAfuBH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA2C,UAAA,CAAApC,SAAA,CAAsB;IAM1BZ,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,UAAA,YAAAuC,UAAA,CAAAlB,KAAA,CAAiB;IAMb9B,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAA2C,UAAA,CAAAG,MAAA,CAAAF,MAAA,CAA0B;;;;;IA+CrDjD,EAAA,CAAAC,cAAA,iBAAkE;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA1DH,EAAA,CAAAS,UAAA,UAAA2C,UAAA,CAAAxC,SAAA,CAA0B;IAACZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA+C,UAAA,CAAAxC,SAAA,CAAsB;;;;;;IA4BpFZ,EAAA,CAAAC,cAAA,SAAyC;IAG0BD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAI9FH,EAAA,CAAAC,cAAA,cAAiB;IAEfD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACVH,EAAA,CAAAC,cAAA,cAAiB;IAEZD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAGRH,EAAA,CAAAC,cAAA,eAAiB;IAGiBD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAMjEH,EAAA,CAAAC,cAAA,eAAqB;IACOD,EAAA,CAAAiB,UAAA,mBAAAoC,wDAAA;MAAA,MAAAC,WAAA,GAAAtD,EAAA,CAAAoB,aAAA,CAAAmC,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA1D,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAkC,OAAA,CAAAC,UAAA,CAAAH,SAAA,CAAiB;IAAA,EAAC;IACrDxD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAuJ;IAA7HD,EAAA,CAAAiB,UAAA,mBAAA2C,wDAAA;MAAA,MAAAN,WAAA,GAAAtD,EAAA,CAAAoB,aAAA,CAAAmC,IAAA;MAAA,MAAAC,SAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAI,OAAA,GAAA7D,EAAA,CAAAuB,aAAA;MAAA,OAASvB,EAAA,CAAAwB,WAAA,CAAAqC,OAAA,CAAAC,aAAA,CAAAN,SAAA,CAAAO,OAAA,CAA4B;IAAA,EAAC;IAC9D/D,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA7B8DH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAmD,SAAA,CAAAhD,QAAA,CAAoB;IAMvFR,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAA6C,SAAA,CAAA1C,MAAA,kBAAA0C,SAAA,CAAA1C,MAAA,CAAAF,SAAA,MACF;IAGEZ,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAW,kBAAA,MAAA6C,SAAA,CAAA9B,YAAA,MACF;IAMiC1B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAmD,SAAA,CAAAQ,aAAA,CAAyB;;;;;;;;ADnc9D,OAAM,MAAOC,cAAc;EAoC3BC,YAAoBC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAnC/B,KAAAC,OAAO,GAAU,EAAE;IACnB,KAAAjB,MAAM,GAAS,EAAE;IACjB,KAAAkB,UAAU,GAAW,EAAE;IACrB,KAAAC,WAAW,GAAG,KAAK;IACnB,KAAAC,YAAY,GAAkB,EAAE;IAElC;IACA,KAAA7B,YAAY,GAAG;MACb8B,IAAI,EAAE,KAAK;MACX7B,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE;KACV;IACD,KAAAnB,QAAQ,GAAU;MAChBsC,OAAO,EAAE,CAAC;MACVvD,QAAQ,EAAE,EAAE;MACZwD,aAAa,EAAE,EAAE;MACjBlD,MAAM,EAAE,IAAI;MACZ2D,WAAW,EAAE,EAAE;MACfvC,WAAW,EAAE,IAAI;MACjBR,YAAY,EAAE;KACf;IAGD,KAAAgD,SAAS,GAAU;MACjBX,OAAO,EAAE,CAAC;MACVvD,QAAQ,EAAE,EAAE;MACZwD,aAAa,EAAE,EAAE;MACjBlD,MAAM,EAAE,IAAI;MACZ2D,WAAW,EAAE,EAAE;MACfvC,WAAW,EAAE,IAAI;MACjBR,YAAY,EAAE;KACf;IA0CD,KAAAiD,kBAAkB,GAAW,EAAE,CAAC,CAAC;IA2G/B,KAAApE,YAAY,GAAQ,EAAE;EAjJuB;EAC7CqE,QAAQA,CAAA;IAEN,IAAI,CAACC,aAAa,EAAE;IAEpB,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,iBAAiB,EAAE;EACxB;EACEC,mBAAmBA,CAACC,KAAiB;IACvC,IAAKA,KAAK,CAACC,MAAsB,CAACC,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACC,UAAU,EAAE;;EAErB;EACEC,SAASA,CAAA;IACT,IAAI,CAAChB,WAAW,GAAG,IAAI;EACzB;EAEAe,UAAUA,CAAA;IACR,IAAI,CAACf,WAAW,GAAG,KAAK;EAC1B;EAEA;EACAiB,gBAAgBA,CAAC5C,IAAyB,EAAEC,OAAe;IACzD,IAAI,CAACF,YAAY,GAAG;MAClB8B,IAAI,EAAE,IAAI;MACV7B,IAAI,EAAEA,IAAI;MACVC,OAAO,EAAEA;KACV;IAED;IACA4C,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC/C,YAAY,CAAC8B,IAAI,GAAG,KAAK;EAChC;EAIF,IAAIkB,cAAcA,CAAA;IAChB,OAAO,IAAI,CAACvC,MAAM,CAACwC,MAAM,CAACC,KAAK,IAAG;MAChC,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAClB,kBAAkB,IAAIiB,KAAK,CAAC9E,MAAM,EAAEF,SAAS,KAAK,IAAI,CAAC+D,kBAAkB;MACnG,MAAMmB,WAAW,GAAG,CAAC,IAAI,CAACzB,UAAU,IAAIuB,KAAK,CAACpF,QAAQ,CAACuF,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC3B,UAAU,CAAC0B,WAAW,EAAE,CAAC;MAC5G,OAAOF,WAAW,IAAIC,WAAW;IACnC,CAAC,CAAC;EACJ;EAEAf,iBAAiBA,CAAA;IAEf,IAAI,CAACZ,WAAW,CAAC8B,iBAAiB,EAAE,CAACC,SAAS,CAACC,IAAI,IAAG;MACtD,IAAI,CAAC5B,YAAY,GAAE4B,IAAI;IAEzB,CAAC,CAAC;EACF;EAGItB,aAAaA,CAAA;IAEX,IAAI,CAACV,WAAW,CAACiC,aAAa,EAAE,CAACF,SAAS,CAACC,IAAI,IAAG;MAClD,IAAI,CAAC/B,OAAO,GAAG+B,IAAI;IAErB,CAAC,CAAC;EACF;EAEFE,WAAWA,CAACC,EAAU;IACpB,IAAI,CAACnC,WAAW,CAACkC,WAAW,CAACC,EAAE,CAAC,CAACJ,SAAS,CAAC,MAAK;MAC9C,IAAI,CAAC/C,MAAM,GAAG,IAAI,CAACA,MAAM,CAACwC,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC7B,OAAO,KAAKuC,EAAE,CAAC;IACjE,CAAC,CAAC;EACJ;EACExC,aAAaA,CAACyC,OAAe;IAC7BC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IACjBG,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;IAClD,IAAI,CAACtB,gBAAgB,CAAC,SAAS,EAAE,8BAA8B,CAAC;IAC9D,IAAI,CAACc,WAAW,CAACE,OAAO,CAAC;EAE7B;EAEA5C,UAAUA,CAACmD,KAAY;IAClB,IAAI,CAACvG,YAAY,CAACC,QAAQ,GAAG,EAAE;IACzB,IAAI,CAACD,YAAY,CAACO,MAAM,GAAG,EAAE;IACtC,IAAI,CAAC4D,SAAS,GAAG;MAAE,GAAGoC;IAAK,CAAE;IAC7B,MAAMC,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAInH,SAAS,CAAC+G,KAAK,CAACC,YAAY,CAAC;MAC/CG,KAAK,CAAC1C,IAAI,EAAE;KACb,MAAM;MACLgC,OAAO,CAACW,KAAK,CAAC,0DAA0D,CAAC;;EAE7E;EACAC,aAAaA,CAACC,IAAY;IAC1B,IAAIA,IAAI,CAACC,OAAO,EAAE;MAChBD,IAAI,CAACA,IAAI,CAACE,gBAAgB,EAAE,CAAC,CAAC;MAC9B;;IAGF,IAAI,CAACC,UAAU,EAAE,CAAC,CAAC;EACrB;;EAGAC,WAAWA,CAAA;IACT,MAAMV,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAGnH,SAAS,CAAC+G,KAAK,CAACY,WAAW,CAACX,YAAY,CAAC,IAAI,IAAIhH,SAAS,CAAC+G,KAAK,CAACC,YAAY,CAAC;MAC5FG,KAAK,CAACS,IAAI,EAAE;;EAEhB;EAEEH,UAAUA,CAAA;IACRhB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC/B,SAAS,CAAC;IACpD,IAAI,CAACP,WAAW,CAACyD,WAAW,CAAC,IAAI,CAAClD,SAAS,CAAC,CAACwB,SAAS,CACnD2B,QAAQ,IAAI;MACXrB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoB,QAAQ,CAAC;MAC3C,IAAI,CAACtC,gBAAgB,CAAC,SAAS,EAAE,4BAA4B,CAAC;MAC9D,IAAI,CAACkC,WAAW,EAAE;MAClB,IAAI,CAAC3C,YAAY,EAAE;MAEnB4B,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;IAEjD,CAAC,EACAM,KAAK,IAAI;MACRX,OAAO,CAACW,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAAC5B,gBAAgB,CAAC,OAAO,EAAE,gCAAgC,CAAC;IAClE,CAAC,CACF;EACH;EAEFuC,cAAcA,CAACC,EAAU,EAAEC,EAAU;IACnC,OAAOD,EAAE,IAAIC,EAAE,GAAGD,EAAE,CAACE,QAAQ,KAAKD,EAAE,CAACC,QAAQ,GAAGF,EAAE,KAAKC,EAAE;EAC3D;EAEAE,mBAAmBA,CAACH,EAAe,EAAEC,EAAe;IAClD,OAAOD,EAAE,IAAIC,EAAE,GAAGD,EAAE,CAACI,aAAa,KAAKH,EAAE,CAACG,aAAa,GAAGJ,EAAE,KAAKC,EAAE;EACrE;EAGIlD,YAAYA,CAAA;IAEV,IAAI,CAACX,WAAW,CAACiE,WAAW,EAAE,CAAClC,SAAS,CAACC,IAAI,IAAG;MAChD,IAAI,CAAChD,MAAM,GAAGgD,IAAI;IAEpB,CAAC,CAAC;EACF;EAIFkC,WAAWA,CAAA;IACT,IAAI,CAAC9H,YAAY,GAAG,EAAE;EACxB;EAEA+H,cAAcA,CAAA;IACZ,IAAI,CAACD,WAAW,EAAE;IAClB,IAAIE,OAAO,GAAG,IAAI;IAElB;IACA,IAAI,CAAC,IAAI,CAAC9G,QAAQ,CAACjB,QAAQ,IAAI,IAAI,CAACiB,QAAQ,CAACjB,QAAQ,CAACgI,IAAI,EAAE,CAACvF,MAAM,KAAK,CAAC,EAAE;MACzE,IAAI,CAAC1C,YAAY,CAACC,QAAQ,GAAG,4BAA4B;MACzD+H,OAAO,GAAG,KAAK;KAChB,MAAM,IAAI,IAAI,CAAC9G,QAAQ,CAACjB,QAAQ,CAACyC,MAAM,GAAG,CAAC,EAAE;MAC5C,IAAI,CAAC1C,YAAY,CAACC,QAAQ,GAAG,qDAAqD;MAClF+H,OAAO,GAAG,KAAK;;IAGrB,IAAI,CAAC,IAAI,CAAC9G,QAAQ,CAACuC,aAAa,IAAI,IAAI,CAACvC,QAAQ,CAACuC,aAAa,CAACwE,IAAI,EAAE,CAACvF,MAAM,KAAK,CAAC,EAAE;MAC/E,IAAI,CAAC1C,YAAY,CAACyD,aAAa,GAAG,qCAAqC;MACvEuE,OAAO,GAAG,KAAK;KAChB,MAAM,IAAI,IAAI,CAAC9G,QAAQ,CAACuC,aAAa,CAACf,MAAM,GAAG,CAAC,EAAE;MACjD,IAAI,CAAC1C,YAAY,CAACyD,aAAa,GAAG,sDAAsD;MACxFuE,OAAO,GAAG,KAAK;;IAGrB,IAAG,IAAI,CAAC9G,QAAQ,CAACX,MAAM,IAAE,IAAI,EAAC;MACxB,IAAI,CAACP,YAAY,CAACO,MAAM,GAAG,8BAA8B;MACzDyH,OAAO,GAAG,KAAK;;IAGnB,IAAG,IAAI,CAAC9G,QAAQ,CAACC,YAAY,IAAE,IAAI,EAAC;MAChC,IAAI,CAACnB,YAAY,CAACO,MAAM,GAAG,mBAAmB;MAC9CyH,OAAO,GAAG,KAAK;;IAMjB,OAAOA,OAAO;EAChB;EAGAE,UAAUA,CAAA;IACV,IAAI,CAAC,IAAI,CAACH,cAAc,EAAE,EAAE;MAC1B;;IAEF9B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAChF,QAAQ,CAAC;IAExC,IAAI,CAAC0C,WAAW,CAACuE,QAAQ,CAAC,IAAI,CAACjH,QAAQ,CAAC,CAACyE,SAAS,CAAC;MAEjDyC,IAAI,EAAGd,QAAQ,IAAI;QACjBrB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEoB,QAAQ,CAAC;QACrD,IAAI,CAACtC,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,CAAC;QAE7D;QACA,IAAI,CAACpC,MAAM,CAACyF,IAAI,CAACf,QAAQ,CAAC;QAE1B;QAEA,IAAI,CAACxC,UAAU,EAAE;QACVqB,MAAM,CAACC,QAAQ,CAAC;UAAEC,GAAG,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAQ,CAAE,CAAC;MACxD,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfX,OAAO,CAACW,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,IAAI,CAAC5B,gBAAgB,CAAC,OAAO,EAAE,yBAAyB,CAAC;MAC3D;KACD,CAAC;EACJ;;;uBA1PatB,cAAc,EAAAjE,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd9E,cAAc;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCZ3BtJ,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAwJ,SAAA,cAAsB;UAEtBxJ,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAwJ,SAAA,iBAAyB;UAKrBxJ,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAwJ,SAAA,iBAES;UAETxJ,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2DAIP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAAoD;UAAvBD,EAAA,CAAAiB,UAAA,mBAAAwI,iDAAA;YAAA,OAASF,GAAA,CAAAjE,SAAA,EAAW;UAAA,EAAC;UAChDtF,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,sBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,eAA2F;UAAtCD,EAAA,CAAAiB,UAAA,mBAAAyI,8CAAAvI,MAAA;YAAA,OAASoI,GAAA,CAAAvE,mBAAA,CAAA7D,MAAA,CAA2B;UAAA,EAAC;UACxFnB,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAiB,UAAA,mBAAA0I,8CAAAxI,MAAA;YAAA,OAASA,MAAA,CAAAyI,eAAA,EAAwB;UAAA,EAAC;UAC3D5J,EAAA,CAAAC,cAAA,gBAA2C;UAAvBD,EAAA,CAAAiB,UAAA,mBAAA4I,+CAAA;YAAA,OAASN,GAAA,CAAAlE,UAAA,EAAY;UAAA,EAAC;UAACrF,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,cAAoD;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEjFH,EAAA,CAAAC,cAAA,oBAA8D;UAAxDD,EAAA,CAAAiB,UAAA,sBAAA6I,kDAAA;YAAA,OAAYP,GAAA,CAAAd,UAAA,EAAY;UAAA,EAAC;UAClCzI,EAAA,CAAAwJ,SAAA,UAAI;UAEFxJ,EAAA,CAAAC,cAAA,iBAAoG;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC1HH,EAAA,CAAAC,cAAA,iBASC;UAJCD,EAAA,CAAAiB,UAAA,2BAAA8I,wDAAA5I,MAAA;YAAA,OAAAoI,GAAA,CAAA9H,QAAA,CAAAjB,QAAA,GAAAW,MAAA;UAAA,EAA+B;UALjCnB,EAAA,CAAAG,YAAA,EASC;UAEDH,EAAA,CAAA2B,UAAA,KAAAqI,8BAAA,kBAAsF;UAEtFhK,EAAA,CAAAwJ,SAAA,UAAI;UACFxJ,EAAA,CAAAC,cAAA,iBAA6E;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC3FH,EAAA,CAAAwJ,SAAA,UAAI;UACNxJ,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAiB,UAAA,2BAAAgJ,yDAAA9I,MAAA;YAAA,OAAAoI,GAAA,CAAA9H,QAAA,CAAAX,MAAA,GAAAK,MAAA;UAAA,EAA6B;UAK7BnB,EAAA,CAAAC,cAAA,kBAAyC;UAAAD,EAAA,CAAAE,MAAA,oCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGzEH,EAAA,CAAA2B,UAAA,KAAAuI,iCAAA,qBAES;UACXlK,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA2B,UAAA,KAAAwI,8BAAA,kBAAkF;UAElFnK,EAAA,CAAA2B,UAAA,KAAAyI,8BAAA,kBAcM;UACNpK,EAAA,CAAAwJ,SAAA,UAAI;UACFxJ,EAAA,CAAAC,cAAA,iBAA6E;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAChGH,EAAA,CAAAwJ,SAAA,UAAI;UACNxJ,EAAA,CAAAC,cAAA,kBAOC;UAHCD,EAAA,CAAAiB,UAAA,2BAAAoJ,yDAAAlJ,MAAA;YAAA,OAAAoI,GAAA,CAAA9H,QAAA,CAAAS,WAAA,GAAAf,MAAA;UAAA,EAAkC;UAInCnB,EAAA,CAAAC,cAAA,kBAAyC;UAAAD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAA2B,UAAA,KAAA2I,iCAAA,qBAES;UACXtK,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA2B,UAAA,KAAA4I,8BAAA,kBAA4F;UAC1FvK,EAAA,CAAAC,cAAA,iBAAgF;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrGH,EAAA,CAAAC,cAAA,oBASC;UANCD,EAAA,CAAAiB,UAAA,2BAAAuJ,2DAAArJ,MAAA;YAAA,OAAAoI,GAAA,CAAA9H,QAAA,CAAAuC,aAAA,GAAA7C,MAAA;UAAA,EAAoC;UAMrCnB,EAAA,CAAAG,YAAA,EAAW;UAMVH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAM5CH,EAAA,CAAAC,cAAA,eAAsI;UAItGD,EAAA,CAAAE,MAAA,8CAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAwJ,SAAA,kBAA6G;UAE7GxJ,EAAA,CAAAC,cAAA,eAAwB;UAKwCD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAC,cAAA,qBASE;UAJAD,EAAA,CAAAiB,UAAA,2BAAAwJ,wDAAAtJ,MAAA;YAAA,OAAAoI,GAAA,CAAA7E,SAAA,CAAAlE,QAAA,GAAAW,MAAA;UAAA,EAAgC;UALlCnB,EAAA,CAAAG,YAAA,EASE;UACFH,EAAA,CAAA2B,UAAA,KAAA+I,8BAAA,kBAGM;UACR1K,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,iBAAqC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACnDH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAiB,UAAA,2BAAA0J,yDAAAxJ,MAAA;YAAA,OAAAoI,GAAA,CAAA7E,SAAA,CAAA5D,MAAA,GAAAK,MAAA;UAAA,EAA8B;UAK9BnB,EAAA,CAAAC,cAAA,kBAAyC;UAAAD,EAAA,CAAAE,MAAA,oCAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzEH,EAAA,CAAA2B,UAAA,KAAAiJ,iCAAA,qBAES;UACX5K,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA2B,UAAA,KAAAkJ,8BAAA,kBAA2F;UAG3F7K,EAAA,CAAAC,cAAA,iBAAqC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxDH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAiB,UAAA,2BAAA6J,yDAAA3J,MAAA;YAAA,OAAAoI,GAAA,CAAA7E,SAAA,CAAAxC,WAAA,GAAAf,MAAA;UAAA,EAAmC;UAKnCnB,EAAA,CAAAC,cAAA,kBAAyC;UAAAD,EAAA,CAAAE,MAAA,qCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1EH,EAAA,CAAA2B,UAAA,KAAAoJ,iCAAA,qBAES;UACX/K,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAA2B,UAAA,KAAAqJ,8BAAA,kBAAyG;UAGzGhL,EAAA,CAAAC,cAAA,iBAA4C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC/DH,EAAA,CAAAC,cAAA,oBAOC;UAJCD,EAAA,CAAAiB,UAAA,2BAAAgK,2DAAA9J,MAAA;YAAA,OAAAoI,GAAA,CAAA7E,SAAA,CAAAV,aAAA,GAAA7C,MAAA;UAAA,EAAqC;UAItCnB,EAAA,CAAAG,YAAA,EAAW;UAMhBH,EAAA,CAAAC,cAAA,eAA0B;UACgDD,EAAA,CAAAE,MAAA,gBAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACxFH,EAAA,CAAAC,cAAA,mBAIsC;UAApCD,EAAA,CAAAiB,UAAA,mBAAAiK,kDAAA;YAAAlL,EAAA,CAAAoB,aAAA,CAAA+J,IAAA;YAAA,MAAAC,GAAA,GAAApL,EAAA,CAAAqL,WAAA;YAAA,OAASrL,EAAA,CAAAwB,WAAA,CAAA+H,GAAA,CAAAnC,aAAA,CAAAgE,GAAA,CAAyB;UAAA,EAAC;UACnCpL,EAAA,CAAAE,MAAA,mCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAcPH,EAAA,CAAAC,cAAA,gBAAiB;UAiG3BD,EAAA,CAAA2B,UAAA,MAAA2J,+BAAA,kBAEM;UAENtL,EAAA,CAAA2B,UAAA,MAAA4J,+BAAA,mBAsBM;UAMIvL,EAAA,CAAAG,YAAA,EAAM;UAEhBH,EAAA,CAAAwJ,SAAA,WAAI;UAEYxJ,EAAA,CAAAC,cAAA,gBAAoB;UAKSD,EAAA,CAAAE,MAAA,yBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5CH,EAAA,CAAAC,cAAA,cAAyB;UAC3BD,EAAA,CAAAE,MAAA,4CACE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEdH,EAAA,CAAAC,cAAA,gBAAkC;UAEJD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,mBAAkB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI3CH,EAAA,CAAAwJ,SAAA,WAAI;UACtBxJ,EAAA,CAAAC,cAAA,gBAAkD;UAIkCD,EAAA,CAAAiB,UAAA,2BAAAuK,yDAAArK,MAAA;YAAA,OAAAoI,GAAA,CAAAlF,UAAA,GAAAlD,MAAA;UAAA,EAAwB;UAAtGnB,EAAA,CAAAG,YAAA,EAAyG;UACzGH,EAAA,CAAAwJ,SAAA,iBAAiC;UACnCxJ,EAAA,CAAAG,YAAA,EAAM;UAIRH,EAAA,CAAAC,cAAA,YAAK;UAC+CD,EAAA,CAAAiB,UAAA,2BAAAwK,0DAAAtK,MAAA;YAAA,OAAAoI,GAAA,CAAA5E,kBAAA,GAAAxD,MAAA;UAAA,EAAgC;UACpFnB,EAAA,CAAAC,cAAA,mBAAiB;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC5CH,EAAA,CAAA2B,UAAA,MAAA+J,kCAAA,qBAAiG;UACnG1L,EAAA,CAAAG,YAAA,EAAS;UAMSH,EAAA,CAAAC,cAAA,gBAAmC;UAKzBD,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEvDH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE1DH,EAAA,CAAAC,cAAA,eAAwC;UACvCD,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGhBH,EAAA,CAAAC,cAAA,cAAO;UAChBD,EAAA,CAAA2B,UAAA,MAAAgK,8BAAA,kBAsCC;UAGP3L,EAAA,CAAAG,YAAA,EAAQ;UAQEH,EAAA,CAAAC,cAAA,gBAAmC;UACZD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;UA3blJH,EAAA,CAAAI,SAAA,IAAiC;UAAjCJ,EAAA,CAAAS,UAAA,YAAAT,EAAA,CAAA4L,eAAA,KAAAC,GAAA,EAAAtC,GAAA,CAAAjF,WAAA,EAAiC;UAclDtE,EAAA,CAAAI,SAAA,IAA+B;UAA/BJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA9H,QAAA,CAAAjB,QAAA,CAA+B;UAM3BR,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAAhJ,YAAA,CAAAC,QAAA,CAA2B;UAS/BR,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA9H,QAAA,CAAAX,MAAA,CAA6B;UAKrBd,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,iBAAgB;UAGGT,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAnF,OAAA,CAAU;UAIjCpE,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAAhJ,YAAA,CAAAO,MAAA,CAAyB;UAEzBd,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAA9H,QAAA,CAAAX,MAAA,kBAAAyI,GAAA,CAAA9H,QAAA,CAAAX,MAAA,CAAAgB,KAAA,kBAAAyH,GAAA,CAAA9H,QAAA,CAAAX,MAAA,CAAAgB,KAAA,CAAAmB,MAAA,CAAoC;UAsBxCjD,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA9H,QAAA,CAAAS,WAAA,CAAkC;UAI3BlC,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,iBAAgB;UACST,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAhF,YAAA,CAAe;UAI3CvE,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAAhJ,YAAA,CAAA2B,WAAA,CAA8B;UAKlClC,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA9H,QAAA,CAAAuC,aAAA,CAAoC;UAoCxBhE,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA7E,SAAA,CAAAlE,QAAA,CAAgC;UAK5BR,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAS,UAAA,SAAA4B,GAAA,CAAAiF,OAAA,IAAAjF,GAAA,CAAAyJ,OAAA,CAA0C;UAYhD9L,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA7E,SAAA,CAAA5D,MAAA,CAA8B,gBAAAyI,GAAA,CAAAzB,cAAA;UAKtB9H,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,iBAAgB;UACGT,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAnF,OAAA,CAAU;UAIjCpE,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAS,UAAA,SAAAsL,IAAA,CAAAzE,OAAA,IAAAyE,IAAA,CAAAD,OAAA,CAAsC;UAQ1C9L,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA7E,SAAA,CAAAxC,WAAA,CAAmC,gBAAAqH,GAAA,CAAArB,mBAAA;UAK3BlI,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,iBAAgB;UACQT,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAhF,YAAA,CAAe;UAI3CvE,EAAA,CAAAI,SAAA,GAAgD;UAAhDJ,EAAA,CAAAS,UAAA,SAAAuL,IAAA,CAAA1E,OAAA,IAAA0E,IAAA,CAAAF,OAAA,CAAgD;UAOpD9L,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA7E,SAAA,CAAAV,aAAA,CAAqC;UAevChE,EAAA,CAAAI,SAAA,GAA+B;UAA/BJ,EAAA,CAAAS,UAAA,aAAA2K,GAAA,CAAA9D,OAAA,CAA+B;UAkHnCtH,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAS,UAAA,SAAA8I,GAAA,CAAA7G,YAAA,CAAA8B,IAAA,CAAuB;UAILxE,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAnF,OAAA,CAAU;UAuDkDpE,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAlF,UAAA,CAAwB;UAOtDrE,EAAA,CAAAI,SAAA,GAAgC;UAAhCJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA5E,kBAAA,CAAgC;UAEzD3E,EAAA,CAAAI,SAAA,GAAU;UAAVJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAAnF,OAAA,CAAU;UA4BXpE,EAAA,CAAAI,SAAA,IAAiB;UAAjBJ,EAAA,CAAAS,UAAA,YAAA8I,GAAA,CAAA7D,cAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
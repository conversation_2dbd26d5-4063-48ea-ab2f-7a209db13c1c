spring.application.name=equip



### JPA / HIBERNATE ###
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=dlglxrxhhzasabgc
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true






spring.jpa.show-sql=true

spring.jpa.hibernate.ddl-auto=update
server.port=8085
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect



spring.jpa.properties.hibernate.format_sql=true
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

spring.datasource.url=******************************************************************************************************************************************************************

spring.datasource.username=root

spring.datasource.password=

{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../dashboard/type.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../Shared/layout/layout.component\";\nfunction HistoriqueComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"p\");\n    i0.ɵɵtext(2, \"Aucun historique disponible\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HistoriqueComponent_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 24)(2, \"div\", 25)(3, \"div\", 26)(4, \"h6\", 27);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 21);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 28);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const historique_r3 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Action #\", historique_r3.id, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 3, historique_r3.date, \"dd/MM/yyyy HH:mm\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(historique_r3.commentaire);\n  }\n}\nfunction HistoriqueComponent_div_32_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 32)(1, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function HistoriqueComponent_div_32_li_6_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r7);\n      const page_r5 = restoredCtx.$implicit;\n      const ctx_r6 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r6.goToPage(page_r5));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const page_r5 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", page_r5 === ctx_r4.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", page_r5 + 1, \" \");\n  }\n}\nfunction HistoriqueComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"nav\", 30)(2, \"ul\", 31)(3, \"li\", 32)(4, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function HistoriqueComponent_div_32_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.previousPage());\n    });\n    i0.ɵɵtext(5, \" Pr\\u00E9c\\u00E9dent \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, HistoriqueComponent_div_32_li_6_Template, 3, 3, \"li\", 34);\n    i0.ɵɵelementStart(7, \"li\", 32)(8, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function HistoriqueComponent_div_32_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.nextPage());\n    });\n    i0.ɵɵtext(9, \" Suivant \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.currentPage === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getPageNumbers());\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r2.currentPage === ctx_r2.totalPages - 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.currentPage === ctx_r2.totalPages - 1);\n  }\n}\nexport class HistoriqueComponent {\n  constructor(authservice) {\n    this.authservice = authservice;\n    this.size = 10;\n    this.currentPage = 0;\n    this.totalPages = 0;\n    this.searchTerm = '';\n    this.historiques = [];\n  }\n  ngOnInit() {\n    this.loadHistoriques(0);\n  }\n  loadHistoriques(page) {\n    this.currentPage = page;\n    console.log('Chargement des historiques...');\n    console.log('Terme de recherche:', this.searchTerm);\n    if (this.searchTerm === '' || this.searchTerm.trim() === '') {\n      this.authservice.getHistoriques(page, this.size).subscribe({\n        next: res => {\n          console.log('Historiques reçus:', res);\n          this.historiques = res.content;\n          this.totalPages = res.totalPages;\n          console.log('Nombre d\\'historiques:', this.historiques.length);\n        },\n        error: err => {\n          console.error('Erreur lors du chargement des historiques:', err);\n        }\n      });\n    } else {\n      console.log('Recherche avec le terme:', this.searchTerm);\n      this.authservice.searchHistorique(this.searchTerm.trim(), page, this.size).subscribe({\n        next: res => {\n          console.log('Historiques de recherche reçus:', res);\n          this.historiques = res.content;\n          this.totalPages = res.totalPages;\n          console.log('Nombre d\\'historiques trouvés:', this.historiques.length);\n        },\n        error: err => {\n          console.error('Erreur lors de la recherche des historiques:', err);\n        }\n      });\n    }\n  }\n  onSearch() {\n    console.log('Recherche déclenchée avec:', this.searchTerm);\n    this.loadHistoriques(0);\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadHistoriques(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadHistoriques(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadHistoriques(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  static {\n    this.ɵfac = function HistoriqueComponent_Factory(t) {\n      return new (t || HistoriqueComponent)(i0.ɵɵdirectiveInject(i1.TypeService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HistoriqueComponent,\n      selectors: [[\"app-historique\"]],\n      decls: 36,\n      vars: 7,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\", 2, \"width\", \"100%\", \"max-width\", \"100%\", \"padding\", \"0\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"search-wrapper\", 2, \"margin-bottom\", \"3rem\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un historique...\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"row\"], [1, \"col-12\", 2, \"width\", \"100%\", \"padding\", \"0\"], [\"class\", \"text-center py-4\", 4, \"ngIf\"], [\"class\", \"mb-3\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"d-flex justify-content-center mt-4\", 4, \"ngIf\"], [1, \"text-center\", \"mt-2\"], [1, \"text-muted\"], [1, \"text-center\", \"py-4\"], [1, \"mb-3\"], [1, \"card\", \"shadow-sm\"], [1, \"card-body\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-start\", \"mb-2\"], [1, \"mb-0\", \"text-primary\"], [1, \"mb-0\"], [1, \"d-flex\", \"justify-content-center\", \"mt-4\"], [\"aria-label\", \"Navigation des pages\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"disabled\", \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"], [1, \"page-link\", 3, \"click\"]],\n      template: function HistoriqueComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents fournisseurs \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"div\", 11)(25, \"div\", 12)(26, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function HistoriqueComponent_Template_input_ngModelChange_26_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function HistoriqueComponent_Template_input_input_26_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"span\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 15)(29, \"div\", 16);\n          i0.ɵɵtemplate(30, HistoriqueComponent_div_30_Template, 3, 0, \"div\", 17);\n          i0.ɵɵtemplate(31, HistoriqueComponent_div_31_Template, 11, 6, \"div\", 18);\n          i0.ɵɵtemplate(32, HistoriqueComponent_div_32_Template, 10, 7, \"div\", 19);\n          i0.ɵɵelementStart(33, \"div\", 20)(34, \"small\", 21);\n          i0.ɵɵtext(35);\n          i0.ɵɵelementEnd()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(26);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.historiques.length === 0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.historiques);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate3(\" Page \", ctx.currentPage + 1, \" sur \", ctx.totalPages, \" (\", ctx.historiques.length, \" \\u00E9l\\u00E9ments affich\\u00E9s, 3 par page) \");\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.LayoutComponent, i2.DatePipe],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #0051ff; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #d1d5db;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n*[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  font-family: 'Inter', sans-serif;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  background-color: #f6f7fb;\\n  padding: 30px;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 20px;\\n  margin-left: -30px;\\n  width: 1300px; \\n\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  width: 1150px; \\n\\n  background-color: white;\\n  border-radius: 12px;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  padding: 20px;\\n  box-sizing: border-box;\\n}\\n\\n\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-bottom: 15px;\\n  position: relative;\\n}\\n\\n.icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background-color: #e7f8ed;\\n  padding: 6px;\\n  border-radius: 8px;\\n}\\n\\n.info[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n}\\n\\n.info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #777;\\n}\\n\\n.status[_ngcontent-%COMP%] {\\n  background-color: #e0f7e9;\\n  color: #28a745;\\n  font-size: 12px;\\n  padding: 3px 10px;\\n  border-radius: 12px;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n}\\n\\n.details[_ngcontent-%COMP%] {\\n  margin-top: -12px; \\n\\n}\\n\\n.details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  margin-top: 13px; \\n\\n  color: #666666;\\n}\\n\\n.actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 20px;\\n  margin-top: 15px;\\n}\\n\\n.view[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n    width: 500px; \\n\\n  height: 37px;\\n  padding: 10px 24px;\\n  border: 1px solid #ccc;\\n  border-radius: 10px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  background-color: #fff;\\n  color: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n\\n}\\n\\n.edit[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n    width: 500px; \\n\\n  height: 37px;\\n  padding: 10px 24px;\\n  border: 1px solid #ccc;\\n  border-radius: 10px;\\n  font-size: 14px;\\n  cursor: pointer;\\n  background-color: #fff;\\n  color: #000;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n  \\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "historique_r3", "id", "ɵɵtextInterpolate", "ɵɵpipeBind2", "date", "commentaire", "ɵɵlistener", "HistoriqueComponent_div_32_li_6_Template_button_click_1_listener", "restoredCtx", "ɵɵrestoreView", "_r7", "page_r5", "$implicit", "ctx_r6", "ɵɵnextContext", "ɵɵresetView", "goToPage", "ɵɵclassProp", "ctx_r4", "currentPage", "HistoriqueComponent_div_32_Template_button_click_4_listener", "_r9", "ctx_r8", "previousPage", "ɵɵtemplate", "HistoriqueComponent_div_32_li_6_Template", "HistoriqueComponent_div_32_Template_button_click_8_listener", "ctx_r10", "nextPage", "ctx_r2", "ɵɵproperty", "getPageNumbers", "totalPages", "HistoriqueComponent", "constructor", "authservice", "size", "searchTerm", "historiques", "ngOnInit", "loadHistoriques", "page", "console", "log", "trim", "getHistoriques", "subscribe", "next", "res", "content", "length", "error", "err", "searchHistorique", "onSearch", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "ɵɵdirectiveInject", "i1", "TypeService", "selectors", "decls", "vars", "consts", "template", "HistoriqueComponent_Template", "rf", "ctx", "ɵɵelement", "HistoriqueComponent_Template_input_ngModelChange_26_listener", "$event", "HistoriqueComponent_Template_input_input_26_listener", "HistoriqueComponent_div_30_Template", "HistoriqueComponent_div_31_Template", "HistoriqueComponent_div_32_Template", "ɵɵtextInterpolate3"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\historique\\historique.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\historique\\historique.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Historique } from '../equipement/Historique';\nimport { TypeService } from '../dashboard/type.service';\n\n@Component({\n  selector: 'app-historique',\n  templateUrl: './historique.component.html',\n  styleUrls: ['./historique.component.css']\n})\nexport class HistoriqueComponent implements OnInit {\n  constructor(private authservice:TypeService) { }\n\n size:number=10;\ncurrentPage:number=0;\ntotalPages:number=0;\nsearchTerm:string='';\n\nhistoriques:Historique[]=[];\n\n  ngOnInit(): void {\n    this.loadHistoriques(0);\n  }\n\n\n\n  loadHistoriques(page: number): void {\n    this.currentPage = page;\n    console.log('Chargement des historiques...');\n    console.log('Terme de recherche:', this.searchTerm);\n\n    if(this.searchTerm === '' || this.searchTerm.trim() === '') {\n      this.authservice.getHistoriques(page, this.size).subscribe({\n        next: (res) => {\n          console.log('Historiques reçus:', res);\n          this.historiques = res.content;\n          this.totalPages = res.totalPages;\n          console.log('Nombre d\\'historiques:', this.historiques.length);\n        },\n        error: (err) => {\n          console.error('Erreur lors du chargement des historiques:', err);\n        }\n      });\n    } else {\n      console.log('Recherche avec le terme:', this.searchTerm);\n      this.authservice.searchHistorique(this.searchTerm.trim(), page, this.size).subscribe({\n        next: (res: any) => {\n          console.log('Historiques de recherche reçus:', res);\n          this.historiques = res.content;\n          this.totalPages = res.totalPages;\n          console.log('Nombre d\\'historiques trouvés:', this.historiques.length);\n        },\n        error: (err) => {\n          console.error('Erreur lors de la recherche des historiques:', err);\n        }\n      });\n    }\n  }\n\n  onSearch(): void {\n    console.log('Recherche déclenchée avec:', this.searchTerm);\n    this.loadHistoriques(0);\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadHistoriques(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadHistoriques(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadHistoriques(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n}\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\" style=\"width: 100%; max-width: 100%; padding: 0;\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p><PERSON><PERSON><PERSON> efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Fournisseur</h2>\n    <p>Gérez les différents fournisseurs\n\n</p>\n  </div>\n\n</div>\n<div class=\"search-wrapper\" style=\"margin-bottom: 3rem;\">\n  <div class=\"custom-search\">\n    <input type=\"text\" [(ngModel)]=\"searchTerm\" (input)=\"onSearch()\" placeholder=\"Rechercher un historique...\" />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n<!-- Modal -->\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<!-- MODAL -->\n\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            <div class=\"col-12\" style=\"width: 100%; padding: 0;\">\n              <!-- Message si aucun historique -->\n              <div *ngIf=\"historiques.length === 0\" class=\"text-center py-4\">\n                <p>Aucun historique disponible</p>\n              </div>\n\n              <!-- Liste des historiques -->\n              <div *ngFor=\"let historique of historiques\" class=\"mb-3\">\n                <div class=\"card shadow-sm\">\n                  <div class=\"card-body\">\n                    <div class=\"d-flex justify-content-between align-items-start mb-2\">\n                      <h6 class=\"mb-0 text-primary\">Action #{{ historique.id }}</h6>\n                      <small class=\"text-muted\">{{ historique.date | date:'dd/MM/yyyy HH:mm' }}</small>\n                    </div>\n                    <p class=\"mb-0\">{{ historique.commentaire }}</p>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Pagination -->\n              <div *ngIf=\"totalPages > 1\" class=\"d-flex justify-content-center mt-4\">\n                <nav aria-label=\"Navigation des pages\">\n                  <ul class=\"pagination\">\n                    <!-- Bouton Précédent -->\n                    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n                      <button class=\"page-link\" (click)=\"previousPage()\" [disabled]=\"currentPage === 0\">\n                        Précédent\n                      </button>\n                    </li>\n\n                    <!-- Numéros de pages -->\n                    <li *ngFor=\"let page of getPageNumbers()\"\n                        class=\"page-item\"\n                        [class.active]=\"page === currentPage\">\n                      <button class=\"page-link\" (click)=\"goToPage(page)\">\n                        {{ page + 1 }}\n                      </button>\n                    </li>\n\n                    <!-- Bouton Suivant -->\n                    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n                      <button class=\"page-link\" (click)=\"nextPage()\" [disabled]=\"currentPage === totalPages - 1\">\n                        Suivant\n                      </button>\n                    </li>\n                  </ul>\n                </nav>\n              </div>\n\n              <!-- Informations de pagination -->\n              <div class=\"text-center mt-2\">\n                <small class=\"text-muted\">\n                  Page {{ currentPage + 1 }} sur {{ totalPages }}\n                  ({{ historiques.length }} éléments affichés, 3 par page)\n                </small>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n\n\n"], "mappings": ";;;;;;;IC6EcA,EAAA,CAAAC,cAAA,cAA+D;IAC1DD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAIpCH,EAAA,CAAAC,cAAA,cAAyD;IAInBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEnFH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAHhBH,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,kBAAA,aAAAC,aAAA,CAAAC,EAAA,KAA2B;IAC/BP,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAAS,WAAA,OAAAH,aAAA,CAAAI,IAAA,sBAA+C;IAE3DV,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAAQ,iBAAA,CAAAF,aAAA,CAAAK,WAAA,CAA4B;;;;;;IAiB5CX,EAAA,CAAAC,cAAA,aAE0C;IACdD,EAAA,CAAAY,UAAA,mBAAAC,iEAAA;MAAA,MAAAC,WAAA,GAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,WAAA,CAAAI,SAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,QAAA,CAAAL,OAAA,CAAc;IAAA,EAAC;IAChDjB,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAHPH,EAAA,CAAAuB,WAAA,WAAAN,OAAA,KAAAO,MAAA,CAAAC,WAAA,CAAqC;IAErCzB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAY,OAAA,UACF;;;;;;IAhBRjB,EAAA,CAAAC,cAAA,cAAuE;IAKrCD,EAAA,CAAAY,UAAA,mBAAAc,4DAAA;MAAA1B,EAAA,CAAAe,aAAA,CAAAY,GAAA;MAAA,MAAAC,MAAA,GAAA5B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAO,MAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAChD7B,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAIXH,EAAA,CAAA8B,UAAA,IAAAC,wCAAA,iBAMK;IAGL/B,EAAA,CAAAC,cAAA,aAAwE;IAC5CD,EAAA,CAAAY,UAAA,mBAAAoB,4DAAA;MAAAhC,EAAA,CAAAe,aAAA,CAAAY,GAAA;MAAA,MAAAM,OAAA,GAAAjC,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAY,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IAC5ClC,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAnBWH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAuB,WAAA,aAAAY,MAAA,CAAAV,WAAA,OAAoC;IACLzB,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAoC,UAAA,aAAAD,MAAA,CAAAV,WAAA,OAA8B;IAM9DzB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAoC,UAAA,YAAAD,MAAA,CAAAE,cAAA,GAAmB;IASlBrC,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAuB,WAAA,aAAAY,MAAA,CAAAV,WAAA,KAAAU,MAAA,CAAAG,UAAA,KAAiD;IACtBtC,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAoC,UAAA,aAAAD,MAAA,CAAAV,WAAA,KAAAU,MAAA,CAAAG,UAAA,KAA2C;;;AD3GhH,OAAM,MAAOC,mBAAmB;EAC9BC,YAAoBC,WAAuB;IAAvB,KAAAA,WAAW,GAAXA,WAAW;IAEhC,KAAAC,IAAI,GAAQ,EAAE;IACf,KAAAjB,WAAW,GAAQ,CAAC;IACpB,KAAAa,UAAU,GAAQ,CAAC;IACnB,KAAAK,UAAU,GAAQ,EAAE;IAEpB,KAAAC,WAAW,GAAc,EAAE;EAPsB;EAS/CC,QAAQA,CAAA;IACN,IAAI,CAACC,eAAe,CAAC,CAAC,CAAC;EACzB;EAIAA,eAAeA,CAACC,IAAY;IAC1B,IAAI,CAACtB,WAAW,GAAGsB,IAAI;IACvBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACN,UAAU,CAAC;IAEnD,IAAG,IAAI,CAACA,UAAU,KAAK,EAAE,IAAI,IAAI,CAACA,UAAU,CAACO,IAAI,EAAE,KAAK,EAAE,EAAE;MAC1D,IAAI,CAACT,WAAW,CAACU,cAAc,CAACJ,IAAI,EAAE,IAAI,CAACL,IAAI,CAAC,CAACU,SAAS,CAAC;QACzDC,IAAI,EAAGC,GAAG,IAAI;UACZN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEK,GAAG,CAAC;UACtC,IAAI,CAACV,WAAW,GAAGU,GAAG,CAACC,OAAO;UAC9B,IAAI,CAACjB,UAAU,GAAGgB,GAAG,CAAChB,UAAU;UAChCU,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACL,WAAW,CAACY,MAAM,CAAC;QAChE,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbV,OAAO,CAACS,KAAK,CAAC,4CAA4C,EAAEC,GAAG,CAAC;QAClE;OACD,CAAC;KACH,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAACN,UAAU,CAAC;MACxD,IAAI,CAACF,WAAW,CAACkB,gBAAgB,CAAC,IAAI,CAAChB,UAAU,CAACO,IAAI,EAAE,EAAEH,IAAI,EAAE,IAAI,CAACL,IAAI,CAAC,CAACU,SAAS,CAAC;QACnFC,IAAI,EAAGC,GAAQ,IAAI;UACjBN,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEK,GAAG,CAAC;UACnD,IAAI,CAACV,WAAW,GAAGU,GAAG,CAACC,OAAO;UAC9B,IAAI,CAACjB,UAAU,GAAGgB,GAAG,CAAChB,UAAU;UAChCU,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACL,WAAW,CAACY,MAAM,CAAC;QACxE,CAAC;QACDC,KAAK,EAAGC,GAAG,IAAI;UACbV,OAAO,CAACS,KAAK,CAAC,8CAA8C,EAAEC,GAAG,CAAC;QACpE;OACD,CAAC;;EAEN;EAEAE,QAAQA,CAAA;IACNZ,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAACN,UAAU,CAAC;IAC1D,IAAI,CAACG,eAAe,CAAC,CAAC,CAAC;EACzB;EAEA;EACAxB,QAAQA,CAACyB,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAACT,UAAU,EAAE;MACvC,IAAI,CAACQ,eAAe,CAACC,IAAI,CAAC;;EAE9B;EAEAb,QAAQA,CAAA;IACN,IAAI,IAAI,CAACT,WAAW,GAAG,IAAI,CAACa,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACQ,eAAe,CAAC,IAAI,CAACrB,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEAI,YAAYA,CAAA;IACV,IAAI,IAAI,CAACJ,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACqB,eAAe,CAAC,IAAI,CAACrB,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACAY,cAAcA,CAAA;IACZ,MAAMwB,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACxC,WAAW,GAAGuC,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAC9B,UAAU,GAAG,CAAC,EAAEyB,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;;;uBA3FWtB,mBAAmB,EAAAvC,EAAA,CAAAuE,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAnBlC,mBAAmB;MAAAmC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRhChF,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAkF,SAAA,cAAsB;UAEtBlF,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAkF,SAAA,iBAAyB;UAKrBlF,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAkF,SAAA,iBAES;UAETlF,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oDAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIJH,EAAA,CAAAC,cAAA,eAAyD;UAElCD,EAAA,CAAAY,UAAA,2BAAAuE,6DAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAtC,UAAA,GAAAyC,MAAA;UAAA,EAAwB,mBAAAC,qDAAA;YAAA,OAAUJ,GAAA,CAAArB,QAAA,EAAU;UAAA,EAApB;UAA3C5D,EAAA,CAAAG,YAAA,EAA6G;UAC7GH,EAAA,CAAAkF,SAAA,gBAAiC;UACnClF,EAAA,CAAAG,YAAA,EAAM;UAuBEH,EAAA,CAAAC,cAAA,eAAiB;UAGbD,EAAA,CAAA8B,UAAA,KAAAwD,mCAAA,kBAEM;UAGNtF,EAAA,CAAA8B,UAAA,KAAAyD,mCAAA,mBAUM;UAGNvF,EAAA,CAAA8B,UAAA,KAAA0D,mCAAA,mBA2BM;UAGNxF,EAAA,CAAAC,cAAA,eAA8B;UAE1BD,EAAA,CAAAE,MAAA,IAEF;UAAAF,EAAA,CAAAG,YAAA,EAAQ;;;UAhFDH,EAAA,CAAAI,SAAA,IAAwB;UAAxBJ,EAAA,CAAAoC,UAAA,YAAA6C,GAAA,CAAAtC,UAAA,CAAwB;UA4B3B3C,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAoC,UAAA,SAAA6C,GAAA,CAAArC,WAAA,CAAAY,MAAA,OAA8B;UAKRxD,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAoC,UAAA,YAAA6C,GAAA,CAAArC,WAAA,CAAc;UAapC5C,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoC,UAAA,SAAA6C,GAAA,CAAA3C,UAAA,KAAoB;UAgCtBtC,EAAA,CAAAI,SAAA,GAEF;UAFEJ,EAAA,CAAAyF,kBAAA,WAAAR,GAAA,CAAAxD,WAAA,eAAAwD,GAAA,CAAA3C,UAAA,QAAA2C,GAAA,CAAArC,WAAA,CAAAY,MAAA,oDAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
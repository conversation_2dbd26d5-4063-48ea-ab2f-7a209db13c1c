{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"../dashboard/type.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/common\";\nfunction MarqueComponent_div_41_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de Marque est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_41_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de marque doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, Marque<PERSON>omponent_div_41_div_1_Template, 2, 0, \"div\", 69);\n    i0.ɵɵtemplate(2, MarqueComponent_div_41_div_2_Template, 2, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(40);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.errors == null ? null : _r1.errors[\"minlength\"]);\n  }\n}\nfunction MarqueComponent_option_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r15 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", type_r15);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(type_r15.nomType);\n  }\n}\nfunction MarqueComponent_div_48_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Veuillez s\\u00E9lectionner au moins un type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, MarqueComponent_div_48_div_1_Template, 2, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r3 = i0.ɵɵreference(46);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r3.errors == null ? null : _r3.errors[\"required\"]);\n  }\n}\nfunction MarqueComponent_div_65_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de Marque est requis\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_65_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Le nom de marque doit contenir au moins 2 caract\\u00E8res\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, MarqueComponent_div_65_div_1_Template, 2, 0, \"div\", 69);\n    i0.ɵɵtemplate(2, MarqueComponent_div_65_div_2_Template, 2, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    let tmp_1_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r6.form.get(\"nomMarque\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_1_0 = ctx_r6.form.get(\"nomMarque\")) == null ? null : tmp_1_0.errors == null ? null : tmp_1_0.errors[\"minlength\"]);\n  }\n}\nfunction MarqueComponent_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"img\", 72);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"src\", ctx_r7.imagePreview, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction MarqueComponent_option_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 70);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r19 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngValue\", type_r19);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(type_r19.nomType);\n  }\n}\nfunction MarqueComponent_div_72_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Veuillez s\\u00E9lectionner au moins un type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68);\n    i0.ɵɵtemplate(1, MarqueComponent_div_72_div_1_Template, 2, 0, \"div\", 69);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r9 = i0.ɵɵnextContext();\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", (tmp_0_0 = ctx_r9.form.get(\"types\")) == null ? null : tmp_0_0.errors == null ? null : tmp_0_0.errors[\"required\"]);\n  }\n}\nfunction MarqueComponent_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r10.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r10.notification.message, \"\\n\");\n  }\n}\nfunction MarqueComponent_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"div\")(3, \"p\", 76);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 77);\n    i0.ɵɵtext(6, \"Cr\\u00E9\\u00E9 le 15/01/2024\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"p\", 78);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 79)(10, \"span\", 80);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 81);\n    i0.ɵɵtext(13, \"Modifier\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const type_r21 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(type_r21.nomType);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(type_r21.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", type_r21.marques.length, \" marques\");\n  }\n}\nfunction MarqueComponent_tr_114_ng_container_10_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 94);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r27 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", type_r27.nomType, \" \");\n  }\n}\nfunction MarqueComponent_tr_114_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MarqueComponent_tr_114_ng_container_10_span_1_Template, 2, 1, \"span\", 93);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const marque_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", marque_r22.types);\n  }\n}\nfunction MarqueComponent_tr_114_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 95);\n    i0.ɵɵtext(1, \"Aucun type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MarqueComponent_tr_114_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 82)(2, \"div\", 83);\n    i0.ɵɵelement(3, \"img\", 84);\n    i0.ɵɵelementStart(4, \"div\", 85)(5, \"h6\", 86);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"td\", 87);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"td\", 87);\n    i0.ɵɵtemplate(10, MarqueComponent_tr_114_ng_container_10_Template, 2, 1, \"ng-container\", 88);\n    i0.ɵɵtemplate(11, MarqueComponent_tr_114_ng_template_11_Template, 2, 0, \"ng-template\", null, 89, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 90)(14, \"button\", 91);\n    i0.ɵɵlistener(\"click\", function MarqueComponent_tr_114_Template_button_click_14_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const marque_r22 = restoredCtx.$implicit;\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.openModal1(marque_r22));\n    });\n    i0.ɵɵtext(15, \" \\u270F\\uFE0F \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function MarqueComponent_tr_114_Template_button_click_16_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r30);\n      const marque_r22 = restoredCtx.$implicit;\n      const ctx_r31 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r31.confirmDelete(marque_r22.idMarque));\n    });\n    i0.ɵɵtext(17, \" \\uD83D\\uDDD1\\uFE0F \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const marque_r22 = ctx.$implicit;\n    const _r24 = i0.ɵɵreference(12);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", marque_r22.image || \"assets/images/profile/default.jpg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(marque_r22.nomMarque);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(marque_r22.nomMarque);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", marque_r22.types && marque_r22.types.length > 0)(\"ngIfElse\", _r24);\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"show\": a0\n  };\n};\nexport class MarqueComponent {\n  constructor(http, authservice, fb) {\n    this.http = http;\n    this.authservice = authservice;\n    this.fb = fb;\n    // Notification system\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.Types = [];\n    this.Marques = [];\n    this.isModalOpen = false;\n    this.selectedTypeId = null;\n    this.newMarque = {\n      idMarque: 0,\n      nomMarque: '',\n      image: null,\n      types: [],\n      models: []\n    };\n    this.newMarque1 = {\n      idMarque: 0,\n      nomMarque: '',\n      image: null,\n      types: [],\n      models: []\n    };\n    this.imagePreview = null;\n    this.selectedImage = null;\n    this.signupErrors = {};\n  }\n  deleteMarque(id) {\n    this.authservice.deleteMarque(id).subscribe(() => {\n      this.Marques = this.Marques.filter(marque => marque.idMarque !== id);\n    });\n  }\n  confirmDelete(MarqueId) {\n    console.log(MarqueId);\n    this.showNotification('success', 'Type supprimé avec succès');\n    this.deleteMarque(MarqueId);\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  ngOnInit() {\n    this.GetALLTypes();\n    this.form = this.fb.group({\n      nomMarque: ['', [Validators.required, Validators.minLength(2)]],\n      types: [[], Validators.required],\n      image: [null]\n    });\n    this.GetAllMarques();\n  }\n  openModal1(Marque) {\n    this.resetErrors();\n    this.newMarque1 = {\n      ...Marque\n    };\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = new bootstrap.Modal(modalElement);\n      modal.show();\n    } else {\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\n    }\n  }\n  onUpdateClick(form) {\n    if (form.invalid) {\n      form.form.markAllAsTouched();\n      return;\n    }\n    this.updateData(); // Appelle ta fonction de mise à jour existante\n  }\n\n  closeModal1() {\n    const modalElement = document.getElementById('updateModal');\n    if (modalElement) {\n      const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\n      modal.hide();\n    }\n  }\n  updateData() {\n    // Transformer les types sélectionnés en tableau contenant uniquement les ID\n    const dataToSend = {\n      ...this.newMarque1,\n      types: this.newMarque1.types.map(type => ({\n        idType: type.idType\n      }))\n    };\n    console.log('Données mises à jour:', dataToSend);\n    this.authservice.updateMarque(dataToSend).subscribe(response => {\n      console.log('Update successful:', response);\n      this.showNotification('success', 'Marque modifiée avec succès');\n      this.closeModal1();\n      this.GetAllMarques();\n      window.scrollTo({\n        top: 0,\n        behavior: 'smooth'\n      });\n    }, error => {\n      console.error('Update failed:', error);\n      this.showNotification('error', 'Échec de la modification de la marque');\n    });\n  }\n  onImageSelected(event) {\n    const file = event.target.files?.[0];\n    if (file) {\n      this.form.patchValue({\n        image: file\n      });\n      this.form.get('image')?.updateValueAndValidity();\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.imagePreview = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n  }\n  openModal() {\n    this.isModalOpen = true;\n  }\n  closeModal() {\n    this.isModalOpen = false;\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  closeOnOutsideClick(event) {\n    if (event.target.classList.contains('modal')) {\n      this.closeModal();\n    }\n  }\n  GetALLTypes() {\n    this.authservice.getAllTypes().subscribe(data => {\n      this.Types = data;\n      console.log(\"Marques reçus : \", JSON.stringify(this.Marques, null, 2));\n    });\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllMarques() {\n    this.authservice.getAllMarques().subscribe(data => {\n      this.Marques = data;\n      console.log(\"Types reçus : \", JSON.stringify(this.Types, null, 2));\n    });\n  }\n  validateSignup() {\n    this.resetErrors();\n    let isValid = true;\n    if (!this.newMarque.nomMarque || this.newMarque.nomMarque.trim().length === 0) {\n      this.signupErrors.nomMarque = 'Le nom de Marque est requis';\n      isValid = false;\n    } else if (this.newMarque.nomMarque.length < 2) {\n      this.signupErrors.nomMarque = 'Le nom de marque doit contenir au moins 2 caractères au minimum';\n      isValid = false;\n    }\n    return isValid;\n  }\n  onRegister() {\n    if (this.form.invalid) {\n      this.form.markAllAsTouched(); // Pour forcer l'affichage des erreurs\n      return;\n    }\n    this.authservice.addMarque(this.form.value).subscribe({\n      next: response => {\n        console.log('User registered successfully', response);\n        this.showNotification('success', 'Marque ajoutée avec succès');\n        this.Marques.push(response);\n        this.closeModal();\n      },\n      error: error => {\n        console.error('Registration failed:', error);\n        alert('Échec de l’enregistrement');\n      }\n    });\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MarqueComponent_Factory(t) {\n      return new (t || MarqueComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.TypeService), i0.ɵɵdirectiveInject(i3.FormBuilder));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MarqueComponent,\n      selectors: [[\"app-marque\"]],\n      decls: 123,\n      vars: 16,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\", 3, \"click\"], [1, \"icon\"], [\"id\", \"updateModal\", \"tabindex\", \"-1\", \"aria-labelledby\", \"updateModalLabel\", \"aria-hidden\", \"true\", \"data-bs-backdrop\", \"false\", 1, \"modal\", \"fade\"], [1, \"modal-dialog\", \"modal-dialog-centered\", \"modal-lg\"], [1, \"modal-content\", \"shadow\", \"rounded-4\"], [\"id\", \"updateModalLabel\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", \"aria-label\", \"Fermer\", 1, \"btn-close\", \"btn-close-white\"], [1, \"modal-body\"], [\"updateForm\", \"ngForm\"], [1, \"mb-4\"], [\"for\", \"nomMarque\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"type\", \"text\", \"id\", \"nomMarque\", \"name\", \"nomMarque\", \"required\", \"\", \"minlength\", \"2\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"nomMarque\", \"ngModel\"], [\"style\", \"color:red\", 4, \"ngIf\"], [\"for\", \"typeMarque\", 1, \"form-label\", \"fw-semibold\", \"fs-5\"], [\"id\", \"typeMarque\", \"name\", \"typeMarque\", \"required\", \"\", \"multiple\", \"\", 1, \"form-inputp\", 3, \"ngModel\", \"ngModelChange\"], [\"typeMarque\", \"ngModel\"], [3, \"ngValue\", 4, \"ngFor\", \"ngForOf\"], [1, \"modal-footer\"], [\"type\", \"button\", \"data-bs-dismiss\", \"modal\", 1, \"btn\", \"btn-secondary\"], [\"type\", \"button\", 1, \"btn\", \"btn-success\", \"px-4\", 3, \"click\"], [1, \"modal\", 3, \"ngClass\", \"click\"], [1, \"modal-content\", 3, \"click\"], [1, \"close\", 3, \"click\"], [2, \"font-size\", \"20px\", \"margin-bottom\", \"-10px\"], [\"novalidate\", \"\", 3, \"formGroup\", \"ngSubmit\"], [\"for\", \"nomType\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\", \"margin-bottom\", \"-40px\"], [\"id\", \"nomType\", \"type\", \"text\", \"formControlName\", \"nomMarque\", \"placeholder\", \"Ex: Ordinateur portable\", \"required\", \"\", 1, \"form-inputp\"], [\"style\", \"margin-top: 10px;\", 4, \"ngIf\"], [\"for\", \"type\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"id\", \"type\", \"formControlName\", \"types\", \"multiple\", \"\", 1, \"form-inputp\"], [\"for\", \"image\", 2, \"font-size\", \"14px\", \"font-weight\", \"500\", \"color\", \"#000000\"], [\"type\", \"file\", \"id\", \"image\", \"accept\", \"image/*\", 1, \"form-inputp\", 3, \"change\"], [\"type\", \"submit\"], [1, \"row\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"card1\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\"], [1, \"card\"], [1, \"card-body\"], [1, \"d-md-flex\", \"align-items-center\"], [1, \"card-title\"], [1, \"card-subtitle\"], [1, \"ms-auto\", \"mt-3\", \"mt-md-0\"], [\"aria-label\", \"Default select example\", 1, \"form-select\", \"theme-select\", \"border-0\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [1, \"table-responsive\", \"mt-4\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [2, \"color\", \"red\"], [4, \"ngIf\"], [3, \"ngValue\"], [2, \"margin-top\", \"10px\"], [\"alt\", \"Aper\\u00E7u\", 2, \"width\", \"100px\", \"height\", \"auto\", \"border\", \"1px solid #ccc\", \"border-radius\", \"5px\", 3, \"src\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"card1\"], [1, \"card1-flex\"], [1, \"card1-title\"], [1, \"card1-date\"], [1, \"card1-desc\"], [1, \"card1-footer\"], [1, \"card1-badge\"], [1, \"card1-button\"], [1, \"px-1\"], [1, \"d-flex\", \"align-items-center\"], [\"width\", \"80\", \"alt\", \"Image Marque\", 1, \"rounded-circle\", 2, \"border-radius\", \"0% !important\", 3, \"src\"], [1, \"ms-3\"], [1, \"mb-0\", \"fw-bolder\"], [1, \"px-0\"], [4, \"ngIf\", \"ngIfElse\"], [\"noTypes\", \"\"], [1, \"text-end\"], [\"title\", \"Modifier\", 1, \"btn\", \"btn-sm\", 2, \"color\", \"blue\", \"font-size\", \"18px\", \"border\", \"none\", \"background\", \"none\", 3, \"click\"], [\"title\", \"Supprimer\", 1, \"btn\", \"btn-sm\", 2, \"color\", \"red\", \"font-size\", \"18px\", \"border\", \"none\", \"background\", \"none\", 3, \"click\"], [\"class\", \"badge bg-info me-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"badge\", \"bg-info\", \"me-1\"], [1, \"badge\", \"bg-secondary\"]],\n      template: function MarqueComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r32 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3)(8, \"div\", 4);\n          i0.ɵɵelement(9, \"header\", 5);\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"div\", 8)(13, \"h1\");\n          i0.ɵɵtext(14, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p\");\n          i0.ɵɵtext(16, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"h2\");\n          i0.ɵɵtext(20, \"Marques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\");\n          i0.ɵɵtext(22, \"G\\u00E9rez les marques d'\\u00E9quipements par type \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 11);\n          i0.ɵɵlistener(\"click\", function MarqueComponent_Template_button_click_23_listener() {\n            return ctx.openModal();\n          });\n          i0.ɵɵelementStart(24, \"span\", 12);\n          i0.ɵɵtext(25, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \"Nouveau Marque \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 13)(28, \"div\", 14)(29, \"div\", 15)(30, \"h5\", 16);\n          i0.ɵɵtext(31, \"\\uD83D\\uDCDD Modifier les informations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(32, \"button\", 17);\n          i0.ɵɵelementStart(33, \"div\", 18)(34, \"form\", null, 19)(36, \"div\", 20)(37, \"label\", 21);\n          i0.ɵɵtext(38, \"Nom du type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"input\", 22, 23);\n          i0.ɵɵlistener(\"ngModelChange\", function MarqueComponent_Template_input_ngModelChange_39_listener($event) {\n            return ctx.newMarque1.nomMarque = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(41, MarqueComponent_div_41_Template, 3, 2, \"div\", 24);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 20)(43, \"label\", 25);\n          i0.ɵɵtext(44, \"Types Disponibles\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"select\", 26, 27);\n          i0.ɵɵlistener(\"ngModelChange\", function MarqueComponent_Template_select_ngModelChange_45_listener($event) {\n            return ctx.newMarque1.types = $event;\n          });\n          i0.ɵɵtemplate(47, MarqueComponent_option_47_Template, 2, 2, \"option\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(48, MarqueComponent_div_48_Template, 2, 1, \"div\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(49, \"div\", 29)(50, \"button\", 30);\n          i0.ɵɵtext(51, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"button\", 31);\n          i0.ɵɵlistener(\"click\", function MarqueComponent_Template_button_click_52_listener() {\n            i0.ɵɵrestoreView(_r32);\n            const _r0 = i0.ɵɵreference(35);\n            return i0.ɵɵresetView(ctx.onUpdateClick(_r0));\n          });\n          i0.ɵɵtext(53, \" \\uD83D\\uDCBE Sauvegarder \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(54, \"div\", 32);\n          i0.ɵɵlistener(\"click\", function MarqueComponent_Template_div_click_54_listener($event) {\n            return ctx.closeOnOutsideClick($event);\n          });\n          i0.ɵɵelementStart(55, \"div\", 33);\n          i0.ɵɵlistener(\"click\", function MarqueComponent_Template_div_click_55_listener($event) {\n            return $event.stopPropagation();\n          });\n          i0.ɵɵelementStart(56, \"span\", 34);\n          i0.ɵɵlistener(\"click\", function MarqueComponent_Template_span_click_56_listener() {\n            return ctx.closeModal();\n          });\n          i0.ɵɵtext(57, \"\\u00D7\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"h3\", 35);\n          i0.ɵɵtext(59, \"Ajouter un nouveau\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"form\", 36);\n          i0.ɵɵlistener(\"ngSubmit\", function MarqueComponent_Template_form_ngSubmit_60_listener() {\n            return ctx.onRegister();\n          });\n          i0.ɵɵelement(61, \"br\");\n          i0.ɵɵelementStart(62, \"label\", 37);\n          i0.ɵɵtext(63, \"Nom du marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"input\", 38);\n          i0.ɵɵtemplate(65, MarqueComponent_div_65_Template, 3, 2, \"div\", 24);\n          i0.ɵɵelement(66, \"br\");\n          i0.ɵɵtemplate(67, MarqueComponent_div_67_Template, 2, 1, \"div\", 39);\n          i0.ɵɵelementStart(68, \"label\", 40);\n          i0.ɵɵtext(69, \"Type d'\\u00E9quipement\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"select\", 41);\n          i0.ɵɵtemplate(71, MarqueComponent_option_71_Template, 2, 2, \"option\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(72, MarqueComponent_div_72_Template, 2, 1, \"div\", 24);\n          i0.ɵɵelement(73, \"br\");\n          i0.ɵɵelementStart(74, \"label\", 42);\n          i0.ɵɵtext(75, \"Logo de la marque\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"input\", 43);\n          i0.ɵɵlistener(\"change\", function MarqueComponent_Template_input_change_76_listener($event) {\n            return ctx.onFileSelected($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"button\", 44);\n          i0.ɵɵtext(78, \"Enregistrer\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(79, \"div\", 45);\n          i0.ɵɵtemplate(80, MarqueComponent_div_80_Template, 2, 2, \"div\", 46);\n          i0.ɵɵtemplate(81, MarqueComponent_div_81_Template, 14, 3, \"div\", 47);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(82, \"br\")(83, \"br\");\n          i0.ɵɵelementStart(84, \"div\", 48)(85, \"div\", 49)(86, \"div\", 50)(87, \"div\", 51)(88, \"div\")(89, \"h4\", 52);\n          i0.ɵɵtext(90, \"Liste Des Marques\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"p\", 53);\n          i0.ɵɵtext(92, \" visualiser les marques disponibles \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(93, \"div\", 54)(94, \"select\", 55)(95, \"option\", 56);\n          i0.ɵɵtext(96, \"March 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(97, \"option\", 57);\n          i0.ɵɵtext(98, \"March 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(99, \"option\", 58);\n          i0.ɵɵtext(100, \"March 2025\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(101, \"div\", 59)(102, \"table\", 60)(103, \"thead\")(104, \"tr\")(105, \"th\", 61);\n          i0.ɵɵtext(106, \" Marque Image \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"th\", 61);\n          i0.ɵɵtext(108, \"Marque Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"th\", 61);\n          i0.ɵɵtext(110, \" Types Associes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"th\", 62);\n          i0.ɵɵtext(112, \" Actions \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(113, \"tbody\");\n          i0.ɵɵtemplate(114, MarqueComponent_tr_114_Template, 18, 5, \"tr\", 63);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(115, \"div\", 64)(116, \"p\", 65);\n          i0.ɵɵtext(117, \"Design and Developed by \");\n          i0.ɵɵelementStart(118, \"a\", 66);\n          i0.ɵɵtext(119, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(120, \" Distributed by \");\n          i0.ɵɵelementStart(121, \"a\", 67);\n          i0.ɵɵtext(122, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r1 = i0.ɵɵreference(40);\n          const _r3 = i0.ɵɵreference(46);\n          let tmp_7_0;\n          let tmp_10_0;\n          i0.ɵɵadvance(39);\n          i0.ɵɵproperty(\"ngModel\", ctx.newMarque1.nomMarque);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", _r1.invalid && _r1.touched);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.newMarque1.types);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.Types);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", _r3.invalid && _r3.touched);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c0, ctx.isModalOpen));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.form.get(\"nomMarque\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.form.get(\"nomMarque\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.imagePreview);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.Types);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.form.get(\"types\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.form.get(\"types\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.Types);\n          i0.ɵɵadvance(33);\n          i0.ɵɵproperty(\"ngForOf\", ctx.Marques);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectMultipleControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinLengthValidator, i3.NgModel, i3.NgForm, i3.FormGroupDirective, i3.FormControlName],\n      styles: [\".welcome-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, #2563eb, #1e40af); \\n\\n  color: white;\\n  padding: 30px 40px;\\n  border-radius: 12px;\\n  margin-bottom: 20px;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: bold;\\n  margin: 0 0 8px 0;\\n  color: white;\\n}\\n\\n.welcome-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  border-radius: 15px;\\n  box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);\\n  padding: 20px 30px;\\n  text-align: center;\\n  flex: 1 1 200px;\\n  max-width: 250px;\\n  transition: transform 0.2s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #000000; \\n\\n  margin-bottom: 10px;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: bold;\\n  color: #111827; \\n\\n}\\n.header-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  background-color: #f9fafb; \\n\\n  padding: 20px 30px;\\n  border-radius: 10px;\\n  margin-bottom: 20px;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 28px;\\n  color: #111827; \\n\\n  font-weight: 700;\\n}\\n\\n.header-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 5px 0 0 0;\\n  color: #6b7280; \\n\\n  font-size: 14px;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%] {\\n  background-color: #000000; \\n\\n  color: white;\\n  padding: 10px 16px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  transition: background-color 0.2s ease;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n\\n.add-user-btn[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 16px;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  display: none; \\n\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  overflow: auto;\\n  background-color: rgba(0,0,0,0.4); \\n\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  margin: 10% auto;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  box-shadow: 0 5px 15px rgba(0,0,0,0.3);\\n  position: relative;\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {opacity: 0;}\\n  to {opacity: 1;}\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  color: #aaa;\\n  position: absolute;\\n  top: 12px;\\n  right: 16px;\\n  font-size: 28px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.close[_ngcontent-%COMP%]:hover {\\n  color: #000;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin: 10px 0;\\n  padding: 10px;\\n  border-radius: 6px;\\n  border: 10px solid #000000;\\n\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #111827;\\n  color: white;\\n  border: none;\\n  padding: 10px 15px;\\n  border-radius: 6px;\\n  cursor: pointer;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%]:hover {\\n  background-color: #1f2937;\\n}\\n.modal[_ngcontent-%COMP%] {\\n  display: none;\\n  position: fixed;\\n  z-index: 1000;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-color: rgba(0, 0, 0, 0.4);\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 20px;\\n  border-radius: 10px;\\n  width: 400px;\\n  max-width: 90%;\\n  position: relative;\\n}\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 10px;\\n  right: 15px;\\n  font-size: 24px;\\n  cursor: pointer;\\n}\\n.stats-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-around;\\n  flex-wrap: wrap;\\n  padding: 20px;\\n  gap: 20px;\\n\\n  \\n\\n  margin-top: -30px; \\n\\n}\\nselect[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px; \\n\\n  padding: 10px 15px;\\n  border: 1.5px solid #ccc;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  font-size: 1rem;\\n  color: #333;\\n  appearance: none; \\n\\n  cursor: pointer;\\n  transition: border-color 0.3s ease;\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%] {\\n  background-image: url(\\\"data:image/svg+xml;charset=US-ASCII,%3Csvg width='14' height='8' viewBox='0 0 14 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%23333' d='M7 8L0 0h14L7 8z'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 15px center;\\n  background-size: 14px 8px;\\n  margin-bottom: 20px; \\n\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: #007bff;\\n  box-shadow: 0 0 5px rgba(0,123,255,0.5);\\n}\\n\\n\\n\\nselect[_ngcontent-%COMP%]:hover {\\n  border-color: #888;\\n}\\n\\n\\n.search-wrapper[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  padding: 20px;\\n  border-radius: 12px;\\n  border: 1px solid #eee;\\n  max-width: 1200px;\\n  margin: 0 auto; \\n\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); \\n\\n  margin-top: -20px;\\n}\\n\\n\\n\\n.custom-search[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px 10px 40px;\\n  border: 1px solid #dcdcdc;\\n  border-radius: 8px;\\n  background-color: #fff;\\n  font-size: 14px;\\n  color: #333;\\n  outline: none;\\n  box-sizing: border-box;\\n  transition: border-color 0.2s ease;\\n}\\n\\n.custom-search[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #bbb;\\n}\\n\\n\\n\\n.icon-search[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 12px;\\n  transform: translateY(-50%);\\n  width: 16px;\\n  height: 16px;\\n  background-image: url('data:image/svg+xml;utf8,<svg fill=\\\"%23999\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 512 512\\\"><path d=\\\"M505 442.7L405.3 343c28.4-34.9 45.7-79 45.7-127C451 103.5 349.5 2 225.5 2S0 103.5 0 216.5 103.5 431 216.5 431c48 0 92.1-17.3 127-45.7l99.7 99.7c4.6 4.6 10.6 7 16.7 7s12.1-2.3 16.7-7c9.3-9.3 9.3-24.4 0-33.7zM216.5 366c-82.6 0-150-67.4-150-150s67.4-150 150-150 150 67.4 150 150-67.4 150-150 150z\\\"/></svg>');\\n  background-repeat: no-repeat;\\n  background-size: 16px 16px;\\n  pointer-events: none;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0; left: 0; right: 0; bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.5);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 999;\\n  visibility: hidden;\\n  opacity: 0;\\n  transition: opacity 0.3s ease;\\n}\\n\\n.modal.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  opacity: 1;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 24px;\\n  border-radius: 12px;\\n  width: 500px;\\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);\\n  font-family: 'Segoe UI', sans-serif;\\n  position: relative;\\n}\\n\\n\\n\\n.close[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 16px;\\n  right: 16px;\\n  font-size: 20px;\\n  cursor: pointer;\\n  color: #6b7280;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%], .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px 12px;\\n  margin-top: 10px;\\n  margin-bottom: 6px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal-content[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus, .modal-content[_ngcontent-%COMP%]   select[_ngcontent-%COMP%]:focus {\\n  border-color: #2563eb;\\n  box-shadow: 0 0 0 1px #2563eb;\\n}\\n\\n\\n\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   button[type=\\\"submit\\\"][_ngcontent-%COMP%] {\\n  background-color: #2563eb;\\n  color: white;\\n  padding: 10px 20px;\\n  border: none;\\n  border-radius: 8px;\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin-top: 16px;\\n  cursor: pointer;\\n  float: right;\\n}\\n\\n\\n\\n.modal-content[_ngcontent-%COMP%]   div[style*=\\\"color:red\\\"][_ngcontent-%COMP%] {\\n  font-size: 13px;\\n  margin-top: -4px;\\n  margin-bottom: 8px;\\n}\\ntextarea[_ngcontent-%COMP%] {\\n  \\n  border-radius: 8px;         \\n\\n  border: 1px solid #d1d5db;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  font-family: inherit;\\n  outline: none;\\n  width: 100%;\\n  resize: vertical;           \\n\\n}\\n\\ntextarea[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #2563eb;\\n  border: 3px solid black;\\n}\\n\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%] {\\n  border: 2px solid #000000;\\n  border-radius: 8px;\\n  padding: 10px 12px;\\n  font-size: 14px;\\n  width: 100%;\\n  margin-top: 5px;\\n  margin-bottom: 10px;\\n  outline: none;\\n  font-family: inherit;\\n}\\n\\n.modal[_ngcontent-%COMP%]   .form-inputp[_ngcontent-%COMP%]:focus {\\n  border-color: #000000;\\n  box-shadow: 0 0 0 1px #000000;\\n}\\n\\n\\n\\nselect[multiple][_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 120px;  \\n\\n  padding: 5px;\\n  border-radius: 5px;\\n  border: 1px solid #ccc;\\n  font-size: 14px;\\n  background-color: white;\\n  box-sizing: border-box;\\n  \\n\\n  overflow-y: auto;\\n}\\n\\n\\n\\n.simple-notification[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 12px 20px;\\n  margin-bottom: 15px;\\n  text-align: center;\\n  font-weight: 500;\\n  font-size: 14px;\\n  border-radius: 8px;\\n}\\n\\n.simple-notification.success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #153257;\\n  border: 1px solid #c3e6cb;\\n}\\n\\n.simple-notification.error[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n  border: 1px solid #f5c6cb;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\", \".card1[_ngcontent-%COMP%] {\\n    width: 280px;\\n    height: 190px;\\n    padding: 20px;\\n    background-color: #fff;\\n    border-radius: 12px;\\n    box-shadow: 0 0 0 1px #e5e7eb;\\n    font-family: 'Segoe UI', sans-serif;\\n    font-size: 14px;\\n    display: flex;\\n    flex-direction: column;\\n    gap: 14px;\\n    margin-top: 20px;\\n    margin-left: 20px;\\n  }\\n\\n  .card1-icon[_ngcontent-%COMP%] {\\n    background-color: #e0edff;\\n    color: #2563eb;\\n    padding: 6px;\\n    border-radius: 8px;\\n    width: 43px;\\n    height: 43px;\\n    display: flex;\\n    align-items: center;\\n    justify-content: center;\\n  }\\n\\n  .card1-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 25px;\\n    height: 25px;\\n  }\\n\\n  .card1-title[_ngcontent-%COMP%] {\\n    font-weight: 600;\\n    color: #111827;\\n    font-size: 20px;\\n    margin: 0;\\n  }\\n\\n  .card1-date[_ngcontent-%COMP%] {\\n    font-size: 15px;\\n    color: #9ca3af;\\n    margin: 2px 0 0 0;\\n  }\\n\\n  .card1-desc[_ngcontent-%COMP%] {\\n    color: #4b5563;\\n    margin: 0;\\n    font-size: 16px;\\n  }\\n\\n  .card1-badge[_ngcontent-%COMP%] {\\n    background-color: #e0edff;\\n    color: #0d00ff;\\n    padding: 4px 10px;\\n    border-radius: 990px;\\n    font-size: 12px;\\n  }\\n\\n.card1-button[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  font-size: 14px;\\n  border: 1px solid #d1d5db;\\n  border-radius: 6px;\\n  background-color: #fff;\\n  cursor: pointer;\\n  color: #000000; \\n\\n  font-weight: 500;;      \\n\\n\\n\\n}\\n\\n\\n  .card1-flex[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    gap: 10px;\\n  }\\n\\n  .card1-footer[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: space-between;\\n    align-items: center;\\n    background-color: white;\\n    padding: 10px;\\n    border-radius: 6px;\\n    border: 0px solid #e5e7eb;\\n  }\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "bootstrap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "MarqueComponent_div_41_div_1_Template", "MarqueComponent_div_41_div_2_Template", "ɵɵadvance", "ɵɵproperty", "_r1", "errors", "type_r15", "ɵɵtextInterpolate", "nomType", "MarqueComponent_div_48_div_1_Template", "_r3", "MarqueComponent_div_65_div_1_Template", "MarqueComponent_div_65_div_2_Template", "tmp_0_0", "ctx_r6", "form", "get", "tmp_1_0", "ɵɵelement", "ctx_r7", "imagePreview", "ɵɵsanitizeUrl", "type_r19", "MarqueComponent_div_72_div_1_Template", "ctx_r9", "ctx_r10", "notification", "type", "ɵɵtextInterpolate1", "message", "type_r21", "description", "marques", "length", "type_r27", "ɵɵelementContainerStart", "MarqueComponent_tr_114_ng_container_10_span_1_Template", "ɵɵelementContainerEnd", "marque_r22", "types", "MarqueComponent_tr_114_ng_container_10_Template", "MarqueComponent_tr_114_ng_template_11_Template", "ɵɵtemplateRefExtractor", "ɵɵlistener", "MarqueComponent_tr_114_Template_button_click_14_listener", "restoredCtx", "ɵɵrestoreView", "_r30", "$implicit", "ctx_r29", "ɵɵnextContext", "ɵɵresetView", "openModal1", "MarqueComponent_tr_114_Template_button_click_16_listener", "ctx_r31", "confirmDelete", "idMarque", "image", "nomMarque", "_r24", "MarqueComponent", "constructor", "http", "authservice", "fb", "show", "Types", "Marques", "isModalOpen", "selectedTypeId", "newMarque", "models", "newMarque1", "selectedImage", "signupErrors", "deleteMarque", "id", "subscribe", "filter", "marque", "MarqueId", "console", "log", "showNotification", "window", "scrollTo", "top", "behavior", "ngOnInit", "GetALLTypes", "group", "required", "<PERSON><PERSON><PERSON><PERSON>", "GetAllMarques", "Marque", "resetErrors", "modalElement", "document", "getElementById", "modal", "Modal", "error", "onUpdateClick", "invalid", "mark<PERSON>llAsTouched", "updateData", "closeModal1", "getInstance", "hide", "dataToSend", "map", "idType", "updateMarque", "response", "onImageSelected", "event", "file", "target", "files", "patchValue", "updateValueAndValidity", "reader", "FileReader", "onload", "result", "readAsDataURL", "openModal", "closeModal", "setTimeout", "hideNotification", "closeOnOutsideClick", "classList", "contains", "getAllTypes", "data", "JSON", "stringify", "getAllMarques", "validateSignup", "<PERSON><PERSON><PERSON><PERSON>", "trim", "onRegister", "addMarque", "value", "next", "push", "alert", "onFileSelected", "formData", "FormData", "append", "post", "imageUrl", "fullUrl", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "TypeService", "i3", "FormBuilder", "selectors", "decls", "vars", "consts", "template", "MarqueComponent_Template", "rf", "ctx", "MarqueComponent_Template_button_click_23_listener", "MarqueComponent_Template_input_ngModelChange_39_listener", "$event", "MarqueComponent_div_41_Template", "MarqueComponent_Template_select_ngModelChange_45_listener", "MarqueComponent_option_47_Template", "MarqueComponent_div_48_Template", "MarqueComponent_Template_button_click_52_listener", "_r32", "_r0", "ɵɵreference", "MarqueComponent_Template_div_click_54_listener", "MarqueComponent_Template_div_click_55_listener", "stopPropagation", "MarqueComponent_Template_span_click_56_listener", "MarqueComponent_Template_form_ngSubmit_60_listener", "MarqueComponent_div_65_Template", "MarqueComponent_div_67_Template", "MarqueComponent_option_71_Template", "MarqueComponent_div_72_Template", "MarqueComponent_Template_input_change_76_listener", "MarqueComponent_div_80_Template", "MarqueComponent_div_81_Template", "MarqueComponent_tr_114_Template", "touched", "ɵɵpureFunction1", "_c0", "tmp_7_0", "tmp_10_0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\marque\\marque.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\marque\\marque.component.html"], "sourcesContent": ["import { Component,OnInit, Type } from '@angular/core';\r\nimport { TypeService } from '../dashboard/type.service';\r\nimport { TypeEqui } from '../dashboard/TypeEqui';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { Marque } from './Marque';\r\nimport { FormBuilder, FormGroup, NgForm, Validators } from '@angular/forms';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport * as bootstrap from 'bootstrap';\r\n// or for just Modal:\r\nimport { Modal } from 'bootstrap';\r\n\r\n@Component({\r\n  selector: 'app-marque',\r\n  templateUrl: './marque.component.html',\r\n  styleUrls: ['./marque.component.css']\r\n})\r\nexport class MarqueComponent implements OnInit {\r\n\r\n// Notification system\r\nnotification = {\r\n  show: false,\r\n  type: 'success', // 'success' or 'error'\r\n  message: ''\r\n};\r\n  Types:TypeEqui[]=[];\r\n    Marques:Marque[]=[];\r\n  isModalOpen = false;\r\nselectedTypeId: number | null = null;\r\nform!: FormGroup;\r\n\r\n\r\n\r\nconstructor(private http: HttpClient,private authservice:TypeService,  private fb: FormBuilder){}\r\nnewMarque: Marque = {\r\n  idMarque: 0,\r\n  nomMarque: '',\r\n  image: null,\r\n  types: [] ,\r\n  models: []\r\n};\r\nnewMarque1: Marque = {\r\n  idMarque: 0,\r\n  nomMarque: '',\r\n  image: null,\r\n  types: [] ,\r\n  models: []\r\n};\r\n  deleteMarque(id: number) {\r\n    this.authservice.deleteMarque(id).subscribe(() => {\r\n      this.Marques = this.Marques.filter(marque => marque.idMarque !== id);\r\n    });\r\n  }\r\n  confirmDelete(MarqueId: number): void {\r\n    console.log(MarqueId);\r\n   this.showNotification('success', 'Type supprimé avec succès');\r\n    \r\n      this.deleteMarque(MarqueId);\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  \r\n  }\r\n\r\nngOnInit(): void {\r\n  this.GetALLTypes();\r\nthis.form = this.fb.group({\r\n    nomMarque: ['', [Validators.required, Validators.minLength(2)]],\r\n    types: [[], Validators.required],\r\n    image: [null]\r\n  });\r\n  this.GetAllMarques();\r\n\r\n\r\n}\r\n\r\n\r\n\r\n  openModal1(Marque: Marque) {\r\n    this.resetErrors();\r\n    this.newMarque1 = { ...Marque };\r\n    const modalElement = document.getElementById('updateModal');\r\n    if (modalElement) {\r\n      const modal = new bootstrap.Modal(modalElement);\r\n      modal.show();\r\n    } else {\r\n      console.error('La modale avec l\\'ID \"updateModal\" n\\'a pas été trouvée.');\r\n    }\r\n  }\r\n  onUpdateClick(form: NgForm) {\r\n  if (form.invalid) {\r\n    form.form.markAllAsTouched();\r\n    return;\r\n  }\r\n\r\n  this.updateData(); // Appelle ta fonction de mise à jour existante\r\n}\r\ncloseModal1() {\r\n  const modalElement = document.getElementById('updateModal');\r\n  if (modalElement) {\r\n    const modal = bootstrap.Modal.getInstance(modalElement) || new bootstrap.Modal(modalElement);\r\n    modal.hide();\r\n  }\r\n}\r\n\r\nupdateData() {\r\n\r\n\r\n  // Transformer les types sélectionnés en tableau contenant uniquement les ID\r\n  const dataToSend = {\r\n    ...this.newMarque1,\r\n    types: this.newMarque1.types.map(type => ({ idType: type.idType }))\r\n  };\r\n\r\n  console.log('Données mises à jour:', dataToSend);\r\n  \r\n  this.authservice.updateMarque(dataToSend).subscribe(\r\n    (response) => {\r\n      console.log('Update successful:', response);\r\n      this.showNotification('success', 'Marque modifiée avec succès');\r\n      this.closeModal1();\r\n      this.GetAllMarques();\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n    },\r\n    (error) => {\r\n      console.error('Update failed:', error);\r\n      this.showNotification('error', 'Échec de la modification de la marque');\r\n    }\r\n  );\r\n}\r\n\r\n\r\nimagePreview: string | ArrayBuffer | null = null;\r\nselectedImage: File | null = null;\r\n\r\nonImageSelected(event: Event): void {\r\n  const file = (event.target as HTMLInputElement).files?.[0];\r\n  if (file) {\r\n    this.form.patchValue({ image: file });\r\n    this.form.get('image')?.updateValueAndValidity();\r\n\r\n    const reader = new FileReader();\r\n    reader.onload = () => {\r\n      this.imagePreview = reader.result;\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n}\r\n\r\n\r\n  openModal() {\r\n    this.isModalOpen = true;\r\n  }\r\n\r\n  closeModal() {\r\n    this.isModalOpen = false;\r\n  }\r\n\r\n  // Méthodes pour les notifications\r\n  showNotification(type: 'success' | 'error', message: string) {\r\n    this.notification = {\r\n      show: true,\r\n      type: type,\r\n      message: message\r\n    };\r\n\r\n    // Auto-hide après 2 secondes\r\n    setTimeout(() => {\r\n      this.hideNotification();\r\n    }, 2000);\r\n  }\r\n\r\n  hideNotification() {\r\n    this.notification.show = false;\r\n  }\r\n\r\n  closeOnOutsideClick(event: MouseEvent) {\r\n    if ((event.target as HTMLElement).classList.contains('modal')) {\r\n      this.closeModal();\r\n    }\r\n  }\r\n\r\n\r\n  GetALLTypes(){\r\n  this.authservice.getAllTypes().subscribe(data => {\r\n  this.Types = data;\r\n  console.log(\"Marques reçus : \", JSON.stringify(this.Marques, null, 2));\r\n});\r\n\r\n}\r\n  signupErrors: any = {};\r\n  resetErrors() {\r\n    this.signupErrors = {};\r\n  }\r\nGetAllMarques()\r\n{\r\n  this.authservice.getAllMarques().subscribe(data => {\r\n  this.Marques = data;\r\n  console.log(\"Types reçus : \", JSON.stringify(this.Types, null, 2));\r\n});\r\n\r\n\r\n}\r\n validateSignup(): boolean {\r\n    this.resetErrors();\r\n    let isValid = true;\r\n\r\n\r\n    if (!this.newMarque.nomMarque || this.newMarque.nomMarque.trim().length === 0) {\r\n      this.signupErrors.nomMarque = 'Le nom de Marque est requis';\r\n      isValid = false;\r\n    } else if (this.newMarque.nomMarque.length < 2) {\r\n      this.signupErrors.nomMarque = 'Le nom de marque doit contenir au moins 2 caractères au minimum';\r\n      isValid = false;\r\n    }\r\n\r\n  \r\n\r\n    \r\n\r\n    return isValid;\r\n  }\r\n  onRegister(): void {\r\nif (this.form.invalid) {\r\n    this.form.markAllAsTouched(); // Pour forcer l'affichage des erreurs\r\n    return;\r\n  }\r\n\r\n\r\n\r\n  this.authservice.addMarque(this.form.value).subscribe({\r\n    next: (response) => {\r\n      console.log('User registered successfully', response);\r\n      this.showNotification('success', 'Marque ajoutée avec succès');\r\n      this.Marques.push(response);\r\n      this.closeModal();\r\n    },\r\n    error: (error) => {\r\n      console.error('Registration failed:', error);\r\n      alert('Échec de l’enregistrement');\r\n    }\r\n  });\r\n}\r\n\r\n\r\nonFileSelected(event: any) {\r\n  const file = event.target.files[0];\r\n\r\n  if (file) {\r\n    const formData = new FormData();\r\n    formData.append('file', file);\r\n\r\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\r\n      (response) => {\r\n        if (response && response.imageUrl) {\r\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\r\n          console.log('Image URL saved: ', fullUrl);\r\n\r\n \r\n          this.form.patchValue({\r\n            image: fullUrl\r\n          });\r\n        } else {\r\n          console.error('Invalid response from API');\r\n        }\r\n      },\r\n      (error) => {\r\n        console.error('Error during image upload', error);\r\n      }\r\n    );\r\n  }\r\n}\r\n\r\n\r\n}\r\n", "<!doctype html>\r\n<html lang=\"en\">\r\n\r\n<head>\r\n  <meta charset=\"utf-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\r\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\r\n  \r\n</head>\r\n\r\n<body>\r\n  <!--  Body Wrapper -->\r\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\r\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\r\n\r\n    <!--  App Topstrip -->\r\n    \r\n    <!-- Sidebar Start -->\r\n\r\n\r\n    <!--  Sidebar End -->\r\n    <!--  Main wrapper -->\r\n    <div class=\"body-wrapper\">\r\n      <!--  Header Start -->\r\n      <header class=\"app-header\">\r\n\r\n      </header>\r\n      <!--  Header End -->\r\n      <div class=\"body-wrapper-inner\">\r\n        <div class=\"container-fluid\">\r\n                <div class=\"welcome-header\">\r\n  <h1>Bienvenue dans votre espace</h1>\r\n  <p>Gérez efficacement vos activités académiques depuis ce tableau de bord</p>\r\n\r\n</div>\r\n<!-- Bouton pour ouvrir la modale -->\r\n\r\n<div class=\"header-container\">\r\n  <div class=\"header-text\">\r\n    <h2>Marques</h2>\r\n    <p>Gérez les marques d'équipements par type\r\n\r\n\r\n\r\n</p>\r\n  </div>\r\n<button class=\"add-user-btn\" (click)=\"openModal()\">\r\n  <span class=\"icon\">+</span>Nouveau Marque\r\n\r\n</button>\r\n</div>\r\n\r\n<div class=\"modal fade\" id=\"updateModal\" tabindex=\"-1\" aria-labelledby=\"updateModalLabel\" aria-hidden=\"true\" data-bs-backdrop=\"false\">\r\n  <div class=\"modal-dialog modal-dialog-centered modal-lg\">\r\n    <div class=\"modal-content shadow rounded-4\">\r\n      \r\n      <h5 id=\"updateModalLabel\">📝 Modifier les informations</h5>\r\n      <button type=\"button\" class=\"btn-close btn-close-white\" data-bs-dismiss=\"modal\" aria-label=\"Fermer\"></button>\r\n\r\n      <div class=\"modal-body\">\r\n        <form #updateForm=\"ngForm\">\r\n          <!-- Nom du type -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"nomMarque\" class=\"form-label fw-semibold fs-5\">Nom du type</label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control\"\r\n              id=\"nomMarque\"\r\n              name=\"nomMarque\"\r\n              [(ngModel)]=\"newMarque1.nomMarque\"\r\n              #nomMarque=\"ngModel\"\r\n              required\r\n              minlength=\"2\"\r\n            />\r\n            <div *ngIf=\"nomMarque.invalid && nomMarque.touched\" style=\"color:red\">\r\n              <div *ngIf=\"nomMarque.errors?.['required']\">Le nom de Marque est requis</div>\r\n              <div *ngIf=\"nomMarque.errors?.['minlength']\">Le nom de marque doit contenir au moins 2 caractères</div>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Types disponibles -->\r\n          <div class=\"mb-4\">\r\n            <label for=\"typeMarque\" class=\"form-label fw-semibold fs-5\">Types Disponibles</label>\r\n            <select\r\n              class=\"form-inputp\"\r\n              id=\"typeMarque\"\r\n              name=\"typeMarque\"\r\n              [(ngModel)]=\"newMarque1.types\"\r\n              #typeMarque=\"ngModel\"\r\n              required\r\n              multiple\r\n            >\r\n              <option *ngFor=\"let type of Types\" [ngValue]=\"type\">{{ type.nomType }}</option>\r\n            </select>\r\n            <div *ngIf=\"typeMarque.invalid && typeMarque.touched\" style=\"color:red\">\r\n              <div *ngIf=\"typeMarque.errors?.['required']\">Veuillez sélectionner au moins un type</div>\r\n            </div>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      <div class=\"modal-footer\">\r\n        <button type=\"button\" class=\"btn btn-secondary\" data-bs-dismiss=\"modal\">\r\n          Annuler\r\n        </button>\r\n        <button type=\"button\" class=\"btn btn-success px-4\" (click)=\"onUpdateClick(updateForm)\">\r\n          💾 Sauvegarder\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- MODAL -->\r\n<div class=\"modal\" [ngClass]=\"{'show': isModalOpen}\" (click)=\"closeOnOutsideClick($event)\">\r\n  <div class=\"modal-content\" (click)=\"$event.stopPropagation()\">\r\n    <span class=\"close\" (click)=\"closeModal()\">&times;</span>\r\n    <h3 style=\"font-size: 20px; margin-bottom: -10px;\">Ajouter un nouveau</h3>\r\n\r\n    <form [formGroup]=\"form\" (ngSubmit)=\"onRegister()\" novalidate>\r\n      <br>\r\n\r\n      <label style=\"font-size: 14px; font-weight: 500; color: #000000; margin-bottom:-40px\" for=\"nomType\">Nom du marque</label>\r\n      <input\r\n        class=\"form-inputp\"\r\n        id=\"nomType\"\r\n        type=\"text\"\r\n        formControlName=\"nomMarque\"\r\n        placeholder=\"Ex: Ordinateur portable\"\r\n        required\r\n      />\r\n      <div *ngIf=\"form.get('nomMarque')?.invalid && form.get('nomMarque')?.touched\" style=\"color:red\">\r\n        <div *ngIf=\"form.get('nomMarque')?.errors?.['required']\">Le nom de Marque est requis</div>\r\n        <div *ngIf=\"form.get('nomMarque')?.errors?.['minlength']\">Le nom de marque doit contenir au moins 2 caractères</div>\r\n      </div>\r\n      <br>\r\n\r\n      <div *ngIf=\"imagePreview\" style=\"margin-top: 10px;\">\r\n        <img [src]=\"imagePreview\" alt=\"Aperçu\" style=\"width: 100px; height: auto; border: 1px solid #ccc; border-radius: 5px;\" />\r\n      </div>\r\n\r\n      <label style=\"font-size: 14px; font-weight: 500; color: #000000;\" for=\"type\">Type d'équipement</label>\r\n      <select\r\n        class=\"form-inputp\"\r\n        id=\"type\"\r\n        formControlName=\"types\"\r\n        multiple\r\n      >\r\n        <option *ngFor=\"let type of Types\" [ngValue]=\"type\">{{ type.nomType }}</option>\r\n      </select>\r\n<div *ngIf=\"form.get('types')?.invalid && form.get('types')?.touched\" style=\"color:red\">\r\n  <div *ngIf=\"form.get('types')?.errors?.['required']\">Veuillez sélectionner au moins un type</div>\r\n</div>\r\n\r\n      <br>\r\n\r\n      <label style=\"font-size: 14px; font-weight: 500; color: #000000;\" for=\"image\">Logo de la marque</label>\r\n      <input \r\n        type=\"file\" \r\n        id=\"image\" \r\n        (change)=\"onFileSelected($event)\" \r\n        accept=\"image/*\"\r\n        class=\"form-inputp\"\r\n      />\r\n\r\n      <button type=\"submit\">Enregistrer</button>\r\n    </form>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n\r\n          <!--  Row 1 -->\r\n          <div class=\"row\">\r\n           \r\n    <style>\r\n  .card1 {\r\n    width: 280px;\r\n    height: 190px;\r\n    padding: 20px;\r\n    background-color: #fff;\r\n    border-radius: 12px;\r\n    box-shadow: 0 0 0 1px #e5e7eb;\r\n    font-family: 'Segoe UI', sans-serif;\r\n    font-size: 14px;\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 14px;\r\n    margin-top: 20px;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .card1-icon {\r\n    background-color: #e0edff;\r\n    color: #2563eb;\r\n    padding: 6px;\r\n    border-radius: 8px;\r\n    width: 43px;\r\n    height: 43px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }\r\n\r\n  .card1-icon svg {\r\n    width: 25px;\r\n    height: 25px;\r\n  }\r\n\r\n  .card1-title {\r\n    font-weight: 600;\r\n    color: #111827;\r\n    font-size: 20px;\r\n    margin: 0;\r\n  }\r\n\r\n  .card1-date {\r\n    font-size: 15px;\r\n    color: #9ca3af;\r\n    margin: 2px 0 0 0;\r\n  }\r\n\r\n  .card1-desc {\r\n    color: #4b5563;\r\n    margin: 0;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .card1-badge {\r\n    background-color: #e0edff;\r\n    color: #0d00ff;\r\n    padding: 4px 10px;\r\n    border-radius: 990px;\r\n    font-size: 12px;\r\n  }\r\n\r\n.card1-button {\r\n  padding: 6px 12px;\r\n  font-size: 14px;\r\n  border: 1px solid #d1d5db;\r\n  border-radius: 6px;\r\n  background-color: #fff;\r\n  cursor: pointer;\r\n  color: #000000; /* Darker gray (high opacity) */\r\n  font-weight: 500;;      /* ⬅️ makes text bold */\r\n\r\n\r\n}\r\n\r\n\r\n  .card1-flex {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .card1-footer {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    background-color: white;\r\n    padding: 10px;\r\n    border-radius: 6px;\r\n    border: 0px solid #e5e7eb;\r\n  }\r\n  \r\n</style>\r\n\r\n<!-- Simple Notification Bar -->\r\n<div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\r\n  {{ notification.message }}\r\n</div>\r\n\r\n<div *ngFor=\"let type of Types\" class=\"card1\">\r\n  <div class=\"card1-flex\">\r\n   \r\n\r\n    <div>\r\n      <p class=\"card1-title\">{{ type.nomType }}</p>\r\n      <p class=\"card1-date\">Créé le 15/01/2024</p> <!-- ou {{ type.dateCreation }} si tu veux le rendre dynamique -->\r\n    </div>\r\n  </div>\r\n\r\n  <p class=\"card1-desc\">{{ type.description }}</p>\r\n\r\n  <div class=\"card1-footer\">\r\n   <span class=\"card1-badge\">{{ type.marques.length }} marques</span>\r\n\r\n    <button class=\"card1-button\">Modifier</button>\r\n  </div>\r\n</div>\r\n\r\n\r\n\r\n       \r\n\r\n          </div>\r\n          \r\n<br>\r\n<br>\r\n                <div class=\"col-12\">\r\n              <div class=\"card\">\r\n                <div class=\"card-body\" style=\"\">\r\n                  <div class=\"d-md-flex align-items-center\">\r\n                    <div>\r\n                      <h4 class=\"card-title\">Liste Des Marques</h4>\r\n                      <p class=\"card-subtitle\">\r\n                    visualiser les marques disponibles\r\n                      </p>\r\n                    </div>\r\n                    <div class=\"ms-auto mt-3 mt-md-0\">\r\n                      <select class=\"form-select theme-select border-0\" aria-label=\"Default select example\">\r\n                        <option value=\"1\">March 2025</option>\r\n                        <option value=\"2\">March 2025</option>\r\n                        <option value=\"3\">March 2025</option>\r\n                      </select>\r\n                    </div>\r\n                  </div>\r\n                  <div class=\"table-responsive mt-4\">\r\n                    <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\r\n                      <thead>\r\n                        <tr>\r\n                          <th scope=\"col\" class=\"px-0 text-muted\">\r\n                            Marque Image\r\n                          </th>\r\n                          <th scope=\"col\" class=\"px-0 text-muted\">Marque Name</th>\r\n                          <th scope=\"col\" class=\"px-0 text-muted\">\r\n                            Types Associes\r\n                          </th>\r\n                          <th scope=\"col\" class=\"px-0 text-muted text-end\">\r\n                            Actions\r\n                          </th>\r\n                        </tr>\r\n                      </thead>\r\n               <tbody>\r\n  <tr *ngFor=\"let marque of Marques\">\r\n    <td class=\"px-1\">\r\n      <div class=\"d-flex align-items-center\">\r\n        <img\r\n          [src]=\"marque.image || 'assets/images/profile/default.jpg'\"\r\n          class=\"rounded-circle\"\r\n          width=\"80\"\r\n          style=\"border-radius: 0% !important;\"\r\n          alt=\"Image Marque\"\r\n        />\r\n        <div class=\"ms-3\">\r\n          <h6 class=\"mb-0 fw-bolder\">{{ marque.nomMarque }}</h6>\r\n         \r\n        </div>\r\n      </div>\r\n    </td>\r\n    <td class=\"px-0\">{{ marque.nomMarque }}</td>\r\n   <td class=\"px-0\">\r\n  <ng-container *ngIf=\"marque.types && marque.types.length > 0; else noTypes\">\r\n    <span\r\n      class=\"badge bg-info me-1\"\r\n      *ngFor=\"let type of marque.types\"\r\n    >\r\n      {{ type.nomType }}\r\n    </span>\r\n  </ng-container>\r\n  <ng-template #noTypes>\r\n    <span class=\"badge bg-secondary\">Aucun type</span>\r\n  </ng-template>\r\n</td>\r\n\r\n\r\n  <td class=\"text-end\">\r\n  <button class=\"btn btn-sm\" (click)=\"openModal1(marque)\" title=\"Modifier\" style=\"color:blue; font-size: 18px; border: none; background: none;\">\r\n    ✏️\r\n  </button>\r\n  <button class=\"btn btn-sm\" (click)=\"confirmDelete(marque.idMarque)\" title=\"Supprimer\" style=\"color:red; font-size: 18px; border: none; background: none;\">\r\n    🗑️\r\n  </button>\r\n</td>\r\n\r\n\r\n\r\n\r\n  </tr>\r\n\r\n</tbody>\r\n\r\n                    </table>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          \r\n          <div class=\"py-6 px-6 text-center\">\r\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\r\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\r\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\r\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\r\n  <script src=\"./assets/js/app.min.js\"></script>\r\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\r\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\r\n  <script src=\"./assets/js/dashboard.js\"></script>\r\n  <!-- solar icons -->\r\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\r\n</body>\r\n\r\n</html>"], "mappings": "AAKA,SAAyCA,UAAU,QAAQ,gBAAgB;AAE3E,OAAO,KAAKC,SAAS,MAAM,WAAW;;;;;;;;ICoExBC,EAAA,CAAAC,cAAA,UAA4C;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC7EH,EAAA,CAAAC,cAAA,UAA6C;IAAAD,EAAA,CAAAE,MAAA,gEAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFzGH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAI,UAAA,IAAAC,qCAAA,kBAA6E;IAC7EL,EAAA,CAAAI,UAAA,IAAAE,qCAAA,kBAAuG;IACzGN,EAAA,CAAAG,YAAA,EAAM;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,aAAoC;IACpCV,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAC,MAAA,kBAAAD,GAAA,CAAAC,MAAA,cAAqC;;;;;IAgB3CV,EAAA,CAAAC,cAAA,iBAAoD;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAQ,UAAA,YAAAG,QAAA,CAAgB;IAACX,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,iBAAA,CAAAD,QAAA,CAAAE,OAAA,CAAkB;;;;;IAGtEb,EAAA,CAAAC,cAAA,UAA6C;IAAAD,EAAA,CAAAE,MAAA,kDAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAD3FH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAI,UAAA,IAAAU,qCAAA,kBAAyF;IAC3Fd,EAAA,CAAAG,YAAA,EAAM;;;;;IADEH,EAAA,CAAAO,SAAA,GAAqC;IAArCP,EAAA,CAAAQ,UAAA,SAAAO,GAAA,CAAAL,MAAA,kBAAAK,GAAA,CAAAL,MAAA,aAAqC;;;;;IAqCjDV,EAAA,CAAAC,cAAA,UAAyD;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAC1FH,EAAA,CAAAC,cAAA,UAA0D;IAAAD,EAAA,CAAAE,MAAA,gEAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFtHH,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAI,UAAA,IAAAY,qCAAA,kBAA0F;IAC1FhB,EAAA,CAAAI,UAAA,IAAAa,qCAAA,kBAAoH;IACtHjB,EAAA,CAAAG,YAAA,EAAM;;;;;;IAFEH,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,UAAA,UAAAU,OAAA,GAAAC,MAAA,CAAAC,IAAA,CAAAC,GAAA,gCAAAH,OAAA,CAAAR,MAAA,kBAAAQ,OAAA,CAAAR,MAAA,aAAiD;IACjDV,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAQ,UAAA,UAAAc,OAAA,GAAAH,MAAA,CAAAC,IAAA,CAAAC,GAAA,gCAAAC,OAAA,CAAAZ,MAAA,kBAAAY,OAAA,CAAAZ,MAAA,cAAkD;;;;;IAI1DV,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAuB,SAAA,cAAyH;IAC3HvB,EAAA,CAAAG,YAAA,EAAM;;;;IADCH,EAAA,CAAAO,SAAA,GAAoB;IAApBP,EAAA,CAAAQ,UAAA,QAAAgB,MAAA,CAAAC,YAAA,EAAAzB,EAAA,CAAA0B,aAAA,CAAoB;;;;;IAUzB1B,EAAA,CAAAC,cAAA,iBAAoD;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAA5CH,EAAA,CAAAQ,UAAA,YAAAmB,QAAA,CAAgB;IAAC3B,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,iBAAA,CAAAe,QAAA,CAAAd,OAAA,CAAkB;;;;;IAG5Eb,EAAA,CAAAC,cAAA,UAAqD;IAAAD,EAAA,CAAAE,MAAA,kDAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADnGH,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAI,UAAA,IAAAwB,qCAAA,kBAAiG;IACnG5B,EAAA,CAAAG,YAAA,EAAM;;;;;IADEH,EAAA,CAAAO,SAAA,GAA6C;IAA7CP,EAAA,CAAAQ,UAAA,UAAAU,OAAA,GAAAW,MAAA,CAAAT,IAAA,CAAAC,GAAA,4BAAAH,OAAA,CAAAR,MAAA,kBAAAQ,OAAA,CAAAR,MAAA,aAA6C;;;;;IAuHrDV,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAQ,UAAA,YAAAsB,OAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiC,kBAAA,MAAAH,OAAA,CAAAC,YAAA,CAAAG,OAAA,OACF;;;;;IAEAlC,EAAA,CAAAC,cAAA,cAA8C;IAKjBD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,mCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIhDH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhDH,EAAA,CAAAC,cAAA,cAA0B;IACCD,EAAA,CAAAE,MAAA,IAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEjEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAVrBH,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAY,iBAAA,CAAAuB,QAAA,CAAAtB,OAAA,CAAkB;IAKvBb,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAY,iBAAA,CAAAuB,QAAA,CAAAC,WAAA,CAAsB;IAGjBpC,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAiC,kBAAA,KAAAE,QAAA,CAAAE,OAAA,CAAAC,MAAA,aAAiC;;;;;IAoE1DtC,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAiC,kBAAA,MAAAM,QAAA,CAAA1B,OAAA,MACF;;;;;IANFb,EAAA,CAAAwC,uBAAA,GAA4E;IAC1ExC,EAAA,CAAAI,UAAA,IAAAqC,sDAAA,mBAKO;IACTzC,EAAA,CAAA0C,qBAAA,EAAe;;;;IAJM1C,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,YAAAmC,UAAA,CAAAC,KAAA,CAAe;;;;;IAMlC5C,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IA3BpDH,EAAA,CAAAC,cAAA,SAAmC;IAG7BD,EAAA,CAAAuB,SAAA,cAME;IACFvB,EAAA,CAAAC,cAAA,cAAkB;IACWD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAK5DH,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,aAAiB;IAClBD,EAAA,CAAAI,UAAA,KAAAyC,+CAAA,2BAOe;IACf7C,EAAA,CAAAI,UAAA,KAAA0C,8CAAA,iCAAA9C,EAAA,CAAA+C,sBAAA,CAEc;IAChB/C,EAAA,CAAAG,YAAA,EAAK;IAGHH,EAAA,CAAAC,cAAA,cAAqB;IACMD,EAAA,CAAAgD,UAAA,mBAAAC,yDAAA;MAAA,MAAAC,WAAA,GAAAlD,EAAA,CAAAmD,aAAA,CAAAC,IAAA;MAAA,MAAAT,UAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAtD,EAAA,CAAAuD,aAAA;MAAA,OAASvD,EAAA,CAAAwD,WAAA,CAAAF,OAAA,CAAAG,UAAA,CAAAd,UAAA,CAAkB;IAAA,EAAC;IACrD3C,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA0J;IAA/HD,EAAA,CAAAgD,UAAA,mBAAAU,yDAAA;MAAA,MAAAR,WAAA,GAAAlD,EAAA,CAAAmD,aAAA,CAAAC,IAAA;MAAA,MAAAT,UAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAM,OAAA,GAAA3D,EAAA,CAAAuD,aAAA;MAAA,OAASvD,EAAA,CAAAwD,WAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAjB,UAAA,CAAAkB,QAAA,CAA8B;IAAA,EAAC;IACjE7D,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAlCDH,EAAA,CAAAO,SAAA,GAA2D;IAA3DP,EAAA,CAAAQ,UAAA,QAAAmC,UAAA,CAAAmB,KAAA,yCAAA9D,EAAA,CAAA0B,aAAA,CAA2D;IAOhC1B,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAY,iBAAA,CAAA+B,UAAA,CAAAoB,SAAA,CAAsB;IAKtC/D,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAY,iBAAA,CAAA+B,UAAA,CAAAoB,SAAA,CAAsB;IAE1B/D,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAQ,UAAA,SAAAmC,UAAA,CAAAC,KAAA,IAAAD,UAAA,CAAAC,KAAA,CAAAN,MAAA,KAA+C,aAAA0B,IAAA;;;;;;;;ADlVhE,OAAM,MAAOC,eAAe;EAgB5BC,YAAoBC,IAAgB,EAASC,WAAuB,EAAWC,EAAe;IAA1E,KAAAF,IAAI,GAAJA,IAAI;IAAqB,KAAAC,WAAW,GAAXA,WAAW;IAAuB,KAAAC,EAAE,GAAFA,EAAE;IAdjF;IACA,KAAAtC,YAAY,GAAG;MACbuC,IAAI,EAAE,KAAK;MACXtC,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;IACC,KAAAqC,KAAK,GAAY,EAAE;IACjB,KAAAC,OAAO,GAAU,EAAE;IACrB,KAAAC,WAAW,GAAG,KAAK;IACrB,KAAAC,cAAc,GAAkB,IAAI;IAMpC,KAAAC,SAAS,GAAW;MAClBd,QAAQ,EAAE,CAAC;MACXE,SAAS,EAAE,EAAE;MACbD,KAAK,EAAE,IAAI;MACXlB,KAAK,EAAE,EAAE;MACTgC,MAAM,EAAE;KACT;IACD,KAAAC,UAAU,GAAW;MACnBhB,QAAQ,EAAE,CAAC;MACXE,SAAS,EAAE,EAAE;MACbD,KAAK,EAAE,IAAI;MACXlB,KAAK,EAAE,EAAE;MACTgC,MAAM,EAAE;KACT;IAmFD,KAAAnD,YAAY,GAAgC,IAAI;IAChD,KAAAqD,aAAa,GAAgB,IAAI;IAyD/B,KAAAC,YAAY,GAAQ,EAAE;EA3JwE;EAe9FC,YAAYA,CAACC,EAAU;IACrB,IAAI,CAACb,WAAW,CAACY,YAAY,CAACC,EAAE,CAAC,CAACC,SAAS,CAAC,MAAK;MAC/C,IAAI,CAACV,OAAO,GAAG,IAAI,CAACA,OAAO,CAACW,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACvB,QAAQ,KAAKoB,EAAE,CAAC;IACtE,CAAC,CAAC;EACJ;EACArB,aAAaA,CAACyB,QAAgB;IAC5BC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAAC;IACtB,IAAI,CAACG,gBAAgB,CAAC,SAAS,EAAE,2BAA2B,CAAC;IAE1D,IAAI,CAACR,YAAY,CAACK,QAAQ,CAAC;IAC3BI,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EAEnD;EAEFC,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;IACpB,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAACiD,EAAE,CAAC0B,KAAK,CAAC;MACtBhC,SAAS,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAACkG,QAAQ,EAAElG,UAAU,CAACmG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/DrD,KAAK,EAAE,CAAC,EAAE,EAAE9C,UAAU,CAACkG,QAAQ,CAAC;MAChClC,KAAK,EAAE,CAAC,IAAI;KACb,CAAC;IACF,IAAI,CAACoC,aAAa,EAAE;EAGtB;EAIEzC,UAAUA,CAAC0C,MAAc;IACvB,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACvB,UAAU,GAAG;MAAE,GAAGsB;IAAM,CAAE;IAC/B,MAAME,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAG,IAAIzG,SAAS,CAAC0G,KAAK,CAACJ,YAAY,CAAC;MAC/CG,KAAK,CAAClC,IAAI,EAAE;KACb,MAAM;MACLgB,OAAO,CAACoB,KAAK,CAAC,0DAA0D,CAAC;;EAE7E;EACAC,aAAaA,CAACvF,IAAY;IAC1B,IAAIA,IAAI,CAACwF,OAAO,EAAE;MAChBxF,IAAI,CAACA,IAAI,CAACyF,gBAAgB,EAAE;MAC5B;;IAGF,IAAI,CAACC,UAAU,EAAE,CAAC,CAAC;EACrB;;EACAC,WAAWA,CAAA;IACT,MAAMV,YAAY,GAAGC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;IAC3D,IAAIF,YAAY,EAAE;MAChB,MAAMG,KAAK,GAAGzG,SAAS,CAAC0G,KAAK,CAACO,WAAW,CAACX,YAAY,CAAC,IAAI,IAAItG,SAAS,CAAC0G,KAAK,CAACJ,YAAY,CAAC;MAC5FG,KAAK,CAACS,IAAI,EAAE;;EAEhB;EAEAH,UAAUA,CAAA;IAGR;IACA,MAAMI,UAAU,GAAG;MACjB,GAAG,IAAI,CAACrC,UAAU;MAClBjC,KAAK,EAAE,IAAI,CAACiC,UAAU,CAACjC,KAAK,CAACuE,GAAG,CAACnF,IAAI,KAAK;QAAEoF,MAAM,EAAEpF,IAAI,CAACoF;MAAM,CAAE,CAAC;KACnE;IAED9B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2B,UAAU,CAAC;IAEhD,IAAI,CAAC9C,WAAW,CAACiD,YAAY,CAACH,UAAU,CAAC,CAAChC,SAAS,CAChDoC,QAAQ,IAAI;MACXhC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE+B,QAAQ,CAAC;MAC3C,IAAI,CAAC9B,gBAAgB,CAAC,SAAS,EAAE,6BAA6B,CAAC;MAC/D,IAAI,CAACuB,WAAW,EAAE;MAClB,IAAI,CAACb,aAAa,EAAE;MACpBT,MAAM,CAACC,QAAQ,CAAC;QAAEC,GAAG,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAQ,CAAE,CAAC;IACjD,CAAC,EACAc,KAAK,IAAI;MACRpB,OAAO,CAACoB,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtC,IAAI,CAAClB,gBAAgB,CAAC,OAAO,EAAE,uCAAuC,CAAC;IACzE,CAAC,CACF;EACH;EAMA+B,eAAeA,CAACC,KAAY;IAC1B,MAAMC,IAAI,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK,GAAG,CAAC,CAAC;IAC1D,IAAIF,IAAI,EAAE;MACR,IAAI,CAACrG,IAAI,CAACwG,UAAU,CAAC;QAAE9D,KAAK,EAAE2D;MAAI,CAAE,CAAC;MACrC,IAAI,CAACrG,IAAI,CAACC,GAAG,CAAC,OAAO,CAAC,EAAEwG,sBAAsB,EAAE;MAEhD,MAAMC,MAAM,GAAG,IAAIC,UAAU,EAAE;MAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;QACnB,IAAI,CAACvG,YAAY,GAAGqG,MAAM,CAACG,MAAM;MACnC,CAAC;MACDH,MAAM,CAACI,aAAa,CAACT,IAAI,CAAC;;EAE9B;EAGEU,SAASA,CAAA;IACP,IAAI,CAAC1D,WAAW,GAAG,IAAI;EACzB;EAEA2D,UAAUA,CAAA;IACR,IAAI,CAAC3D,WAAW,GAAG,KAAK;EAC1B;EAEA;EACAe,gBAAgBA,CAACxD,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBuC,IAAI,EAAE,IAAI;MACVtC,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAED;IACAmG,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACvG,YAAY,CAACuC,IAAI,GAAG,KAAK;EAChC;EAEAiE,mBAAmBA,CAACf,KAAiB;IACnC,IAAKA,KAAK,CAACE,MAAsB,CAACc,SAAS,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC7D,IAAI,CAACL,UAAU,EAAE;;EAErB;EAGAtC,WAAWA,CAAA;IACX,IAAI,CAAC1B,WAAW,CAACsE,WAAW,EAAE,CAACxD,SAAS,CAACyD,IAAI,IAAG;MAChD,IAAI,CAACpE,KAAK,GAAGoE,IAAI;MACjBrD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEqD,IAAI,CAACC,SAAS,CAAC,IAAI,CAACrE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACxE,CAAC,CAAC;EAEF;EAEE4B,WAAWA,CAAA;IACT,IAAI,CAACrB,YAAY,GAAG,EAAE;EACxB;EACFmB,aAAaA,CAAA;IAEX,IAAI,CAAC9B,WAAW,CAAC0E,aAAa,EAAE,CAAC5D,SAAS,CAACyD,IAAI,IAAG;MAClD,IAAI,CAACnE,OAAO,GAAGmE,IAAI;MACnBrD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqD,IAAI,CAACC,SAAS,CAAC,IAAI,CAACtE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC,CAAC;EAGF;EACCwE,cAAcA,CAAA;IACX,IAAI,CAAC3C,WAAW,EAAE;IAClB,IAAI4C,OAAO,GAAG,IAAI;IAGlB,IAAI,CAAC,IAAI,CAACrE,SAAS,CAACZ,SAAS,IAAI,IAAI,CAACY,SAAS,CAACZ,SAAS,CAACkF,IAAI,EAAE,CAAC3G,MAAM,KAAK,CAAC,EAAE;MAC7E,IAAI,CAACyC,YAAY,CAAChB,SAAS,GAAG,6BAA6B;MAC3DiF,OAAO,GAAG,KAAK;KAChB,MAAM,IAAI,IAAI,CAACrE,SAAS,CAACZ,SAAS,CAACzB,MAAM,GAAG,CAAC,EAAE;MAC9C,IAAI,CAACyC,YAAY,CAAChB,SAAS,GAAG,iEAAiE;MAC/FiF,OAAO,GAAG,KAAK;;IAOjB,OAAOA,OAAO;EAChB;EACAE,UAAUA,CAAA;IACZ,IAAI,IAAI,CAAC9H,IAAI,CAACwF,OAAO,EAAE;MACnB,IAAI,CAACxF,IAAI,CAACyF,gBAAgB,EAAE,CAAC,CAAC;MAC9B;;IAKF,IAAI,CAACzC,WAAW,CAAC+E,SAAS,CAAC,IAAI,CAAC/H,IAAI,CAACgI,KAAK,CAAC,CAAClE,SAAS,CAAC;MACpDmE,IAAI,EAAG/B,QAAQ,IAAI;QACjBhC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE+B,QAAQ,CAAC;QACrD,IAAI,CAAC9B,gBAAgB,CAAC,SAAS,EAAE,4BAA4B,CAAC;QAC9D,IAAI,CAAChB,OAAO,CAAC8E,IAAI,CAAChC,QAAQ,CAAC;QAC3B,IAAI,CAACc,UAAU,EAAE;MACnB,CAAC;MACD1B,KAAK,EAAGA,KAAK,IAAI;QACfpB,OAAO,CAACoB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C6C,KAAK,CAAC,2BAA2B,CAAC;MACpC;KACD,CAAC;EACJ;EAGAC,cAAcA,CAAChC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMgC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAElC,IAAI,CAAC;MAE7B,IAAI,CAACtD,IAAI,CAACyF,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACvE,SAAS,CACpEoC,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACuC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBxC,QAAQ,CAACuC,QAAQ,EAAE;UAC3DvE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEuE,OAAO,CAAC;UAGzC,IAAI,CAAC1I,IAAI,CAACwG,UAAU,CAAC;YACnB9D,KAAK,EAAEgG;WACR,CAAC;SACH,MAAM;UACLxE,OAAO,CAACoB,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRpB,OAAO,CAACoB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;;;uBA5PazC,eAAe,EAAAjE,EAAA,CAAA+J,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAjK,EAAA,CAAA+J,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnK,EAAA,CAAA+J,iBAAA,CAAAK,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAfpG,eAAe;MAAAqG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCf5B5K,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAuB,SAAA,cAAsB;UAEtBvB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAcAD,EAAA,CAAAuB,SAAA,gBAES;UAETvB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2DAIP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAAmD;UAAtBD,EAAA,CAAAgD,UAAA,mBAAA8H,kDAAA;YAAA,OAASD,GAAA,CAAA1C,SAAA,EAAW;UAAA,EAAC;UAChDnI,EAAA,CAAAC,cAAA,gBAAmB;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,uBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAGTH,EAAA,CAAAC,cAAA,eAAsI;UAItGD,EAAA,CAAAE,MAAA,8CAA4B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3DH,EAAA,CAAAuB,SAAA,kBAA6G;UAE7GvB,EAAA,CAAAC,cAAA,eAAwB;UAIyCD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC9EH,EAAA,CAAAC,cAAA,qBASE;UAJAD,EAAA,CAAAgD,UAAA,2BAAA+H,yDAAAC,MAAA;YAAA,OAAAH,GAAA,CAAAhG,UAAA,CAAAd,SAAA,GAAAiH,MAAA;UAAA,EAAkC;UALpChL,EAAA,CAAAG,YAAA,EASE;UACFH,EAAA,CAAAI,UAAA,KAAA6K,+BAAA,kBAGM;UACRjL,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAAkB;UAC4CD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACrFH,EAAA,CAAAC,cAAA,sBAQC;UAJCD,EAAA,CAAAgD,UAAA,2BAAAkI,0DAAAF,MAAA;YAAA,OAAAH,GAAA,CAAAhG,UAAA,CAAAjC,KAAA,GAAAoI,MAAA;UAAA,EAA8B;UAK9BhL,EAAA,CAAAI,UAAA,KAAA+K,kCAAA,qBAA+E;UACjFnL,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAI,UAAA,KAAAgL,+BAAA,kBAEM;UACRpL,EAAA,CAAAG,YAAA,EAAM;UAIVH,EAAA,CAAAC,cAAA,eAA0B;UAEtBD,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAAuF;UAApCD,EAAA,CAAAgD,UAAA,mBAAAqI,kDAAA;YAAArL,EAAA,CAAAmD,aAAA,CAAAmI,IAAA;YAAA,MAAAC,GAAA,GAAAvL,EAAA,CAAAwL,WAAA;YAAA,OAASxL,EAAA,CAAAwD,WAAA,CAAAqH,GAAA,CAAAlE,aAAA,CAAA4E,GAAA,CAAyB;UAAA,EAAC;UACpFvL,EAAA,CAAAE,MAAA,kCACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAOjBH,EAAA,CAAAC,cAAA,eAA2F;UAAtCD,EAAA,CAAAgD,UAAA,mBAAAyI,+CAAAT,MAAA;YAAA,OAASH,GAAA,CAAAtC,mBAAA,CAAAyC,MAAA,CAA2B;UAAA,EAAC;UACxFhL,EAAA,CAAAC,cAAA,eAA8D;UAAnCD,EAAA,CAAAgD,UAAA,mBAAA0I,+CAAAV,MAAA;YAAA,OAASA,MAAA,CAAAW,eAAA,EAAwB;UAAA,EAAC;UAC3D3L,EAAA,CAAAC,cAAA,gBAA2C;UAAvBD,EAAA,CAAAgD,UAAA,mBAAA4I,gDAAA;YAAA,OAASf,GAAA,CAAAzC,UAAA,EAAY;UAAA,EAAC;UAACpI,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzDH,EAAA,CAAAC,cAAA,cAAmD;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE1EH,EAAA,CAAAC,cAAA,gBAA8D;UAArCD,EAAA,CAAAgD,UAAA,sBAAA6I,mDAAA;YAAA,OAAYhB,GAAA,CAAA3B,UAAA,EAAY;UAAA,EAAC;UAChDlJ,EAAA,CAAAuB,SAAA,UAAI;UAEJvB,EAAA,CAAAC,cAAA,iBAAoG;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACzHH,EAAA,CAAAuB,SAAA,iBAOE;UACFvB,EAAA,CAAAI,UAAA,KAAA0L,+BAAA,kBAGM;UACN9L,EAAA,CAAAuB,SAAA,UAAI;UAEJvB,EAAA,CAAAI,UAAA,KAAA2L,+BAAA,kBAEM;UAEN/L,EAAA,CAAAC,cAAA,iBAA6E;UAAAD,EAAA,CAAAE,MAAA,8BAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtGH,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAI,UAAA,KAAA4L,kCAAA,qBAA+E;UACjFhM,EAAA,CAAAG,YAAA,EAAS;UACfH,EAAA,CAAAI,UAAA,KAAA6L,+BAAA,kBAEM;UAEAjM,EAAA,CAAAuB,SAAA,UAAI;UAEJvB,EAAA,CAAAC,cAAA,iBAA8E;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACvGH,EAAA,CAAAC,cAAA,iBAME;UAHAD,EAAA,CAAAgD,UAAA,oBAAAkJ,kDAAAlB,MAAA;YAAA,OAAUH,GAAA,CAAArB,cAAA,CAAAwB,MAAA,CAAsB;UAAA,EAAC;UAHnChL,EAAA,CAAAG,YAAA,EAME;UAEFH,EAAA,CAAAC,cAAA,kBAAsB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAStCH,EAAA,CAAAC,cAAA,eAAiB;UAgG3BD,EAAA,CAAAI,UAAA,KAAA+L,+BAAA,kBAEM;UAENnM,EAAA,CAAAI,UAAA,KAAAgM,+BAAA,mBAiBM;UAMIpM,EAAA,CAAAG,YAAA,EAAM;UAEhBH,EAAA,CAAAuB,SAAA,UAAI;UAEYvB,EAAA,CAAAC,cAAA,eAAoB;UAKSD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC7CH,EAAA,CAAAC,cAAA,aAAyB;UAC3BD,EAAA,CAAAE,MAAA,4CACE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAENH,EAAA,CAAAC,cAAA,eAAkC;UAEZD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAkB;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,kBAAkB;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAI3CH,EAAA,CAAAC,cAAA,gBAAmC;UAKzBD,EAAA,CAAAE,MAAA,uBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAwC;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,eAAwC;UACtCD,EAAA,CAAAE,MAAA,yBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,eAAiD;UAC/CD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGhBH,EAAA,CAAAC,cAAA,cAAO;UACpBD,EAAA,CAAAI,UAAA,MAAAiM,+BAAA,kBA4CK;UAEPrM,EAAA,CAAAG,YAAA,EAAQ;UAQEH,EAAA,CAAAC,cAAA,gBAAmC;UACZD,EAAA,CAAAE,MAAA,iCAAwB;UAAAF,EAAA,CAAAC,cAAA,cACW;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,yBAAe;UAAAF,EAAA,CAAAC,cAAA,cAAkD;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;;UAnUvJH,EAAA,CAAAO,SAAA,IAAkC;UAAlCP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAAhG,UAAA,CAAAd,SAAA,CAAkC;UAK9B/D,EAAA,CAAAO,SAAA,GAA4C;UAA5CP,EAAA,CAAAQ,UAAA,SAAAC,GAAA,CAAAmG,OAAA,IAAAnG,GAAA,CAAA6L,OAAA,CAA4C;UAahDtM,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAAhG,UAAA,CAAAjC,KAAA,CAA8B;UAKL5C,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAAtG,KAAA,CAAQ;UAE7BvE,EAAA,CAAAO,SAAA,GAA8C;UAA9CP,EAAA,CAAAQ,UAAA,SAAAO,GAAA,CAAA6F,OAAA,IAAA7F,GAAA,CAAAuL,OAAA,CAA8C;UAoB7CtM,EAAA,CAAAO,SAAA,GAAiC;UAAjCP,EAAA,CAAAQ,UAAA,YAAAR,EAAA,CAAAuM,eAAA,KAAAC,GAAA,EAAA3B,GAAA,CAAApG,WAAA,EAAiC;UAK1CzE,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAQ,UAAA,cAAAqK,GAAA,CAAAzJ,IAAA,CAAkB;UAYhBpB,EAAA,CAAAO,SAAA,GAAsE;UAAtEP,EAAA,CAAAQ,UAAA,WAAAiM,OAAA,GAAA5B,GAAA,CAAAzJ,IAAA,CAAAC,GAAA,gCAAAoL,OAAA,CAAA7F,OAAA,OAAA6F,OAAA,GAAA5B,GAAA,CAAAzJ,IAAA,CAAAC,GAAA,gCAAAoL,OAAA,CAAAH,OAAA,EAAsE;UAMtEtM,EAAA,CAAAO,SAAA,GAAkB;UAAlBP,EAAA,CAAAQ,UAAA,SAAAqK,GAAA,CAAApJ,YAAA,CAAkB;UAWGzB,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAAtG,KAAA,CAAQ;UAEnCvE,EAAA,CAAAO,SAAA,GAA8D;UAA9DP,EAAA,CAAAQ,UAAA,WAAAkM,QAAA,GAAA7B,GAAA,CAAAzJ,IAAA,CAAAC,GAAA,4BAAAqL,QAAA,CAAA9F,OAAA,OAAA8F,QAAA,GAAA7B,GAAA,CAAAzJ,IAAA,CAAAC,GAAA,4BAAAqL,QAAA,CAAAJ,OAAA,EAA8D;UAwH9DtM,EAAA,CAAAO,SAAA,GAAuB;UAAvBP,EAAA,CAAAQ,UAAA,SAAAqK,GAAA,CAAA9I,YAAA,CAAAuC,IAAA,CAAuB;UAIPtE,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAAtG,KAAA,CAAQ;UA8DLvE,EAAA,CAAAO,SAAA,IAAU;UAAVP,EAAA,CAAAQ,UAAA,YAAAqK,GAAA,CAAArG,OAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
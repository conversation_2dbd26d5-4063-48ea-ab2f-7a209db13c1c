{"ast": null, "code": "import { Utilisateur } from './utilisateur';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UtilisateurService {\n  constructor(httpClient) {\n    this.httpClient = httpClient;\n    this.baseURL = \"http://localhost:8085/auth\";\n    this.baseURL1 = \"http://localhost:8085/api/users\";\n    this.user = new Utilisateur();\n  }\n  login(loginRequest) {\n    return this.httpClient.post(`${this.baseURL}/login`, loginRequest).pipe(tap(user => {\n      if (user && user.token) {\n        // Store user data in session storage\n        sessionStorage.setItem('token', user.token);\n        sessionStorage.setItem('username', user.username);\n        sessionStorage.setItem('id', user.id.toString());\n        sessionStorage.setItem('role', user.role);\n        sessionStorage.setItem('email', user.email);\n        // Update current user subject\n        this.currentUserSubject.next(user);\n      }\n    }));\n  }\n  register(agent) {\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  getUtilisateur() {\n    return this.httpClient.get(`${this.baseURL}/AllUsers`);\n  }\n  getAgents() {\n    return this.httpClient.get(`${this.baseURL1}/agents`);\n  }\n  forgotPassword(email) {\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, {\n      email\n    }, {\n      responseType: 'text'\n    });\n  }\n  resetPassword(token, newPassword) {\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, {\n      password: newPassword\n    }, {\n      responseType: 'text'\n    });\n  }\n  checkEmailAvailability(email) {\n    return this.httpClient.get(`${this.baseURL}/check-email?email=${email}`);\n  }\n  redirectToDashboard() {\n    const role = this.getUserRole();\n    if (role === 'enseignant') {\n      this.router.navigate(['/dashboard-enseignant']);\n    } else if (role === 'chef departement') {\n      this.router.navigate(['/dashboard-chef']);\n    } else if (role === 'rapporteur') {\n      this.router.navigate(['/dashboard-rapporteur']);\n    } else if (role === 'admin') {\n      this.router.navigate(['/dashboard-admin']);\n    } else if (role === 'president') {\n      this.router.navigate(['/president']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  getUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  static {\n    this.ɵfac = function UtilisateurService_Factory(t) {\n      return new (t || UtilisateurService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilisateurService,\n      factory: UtilisateurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Utilisa<PERSON>ur", "UtilisateurService", "constructor", "httpClient", "baseURL", "baseURL1", "user", "login", "loginRequest", "post", "pipe", "tap", "token", "sessionStorage", "setItem", "username", "id", "toString", "role", "email", "currentUserSubject", "next", "register", "agent", "headers", "getUtilisateur", "get", "getAgents", "forgotPassword", "responseType", "resetPassword", "newPassword", "password", "checkEmailAvailability", "redirectToDashboard", "getUserRole", "router", "navigate", "getCurrentUser", "value", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient,HttpClientModule } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport {  Agent, Utilisateur } from './utilisateur';\r\nexport interface LoginRequest {\r\n  registartionNumber: string;\r\n  password: string;\r\n}\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilisateurService {\r\n   private baseURL=\"http://localhost:8085/auth\";\r\n  private baseURL1=\"http://localhost:8085/api/users\";\r\n  constructor(private httpClient:HttpClient) { }\r\n\r\nuser: Utilisateur = new Utilisateur();\r\n  \r\n  login(loginRequest: LoginRequest): Observable<any> {\r\n    return this.httpClient.post<any>(`${this.baseURL}/login`, loginRequest)\r\n       .pipe(\r\n        tap(user => {\r\n          if (user && user.token) {\r\n            // Store user data in session storage\r\n            sessionStorage.setItem('token', user.token);\r\n            sessionStorage.setItem('username', user.username);\r\n            sessionStorage.setItem('id', user.id.toString());\r\n            sessionStorage.setItem('role', user.role);\r\n            sessionStorage.setItem('email', user.email);\r\n            \r\n            // Update current user subject\r\n            this.currentUserSubject.next(user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n  \r\n\r\n  register(agent: Utilisateur): Observable<any> {\r\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n    getUtilisateur(): Observable<Utilisateur[]> {\r\n    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);\r\n  }\r\n    getAgents(): Observable<Agent[]> {\r\n    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);\r\n  }\r\n  forgotPassword(email: string) {\r\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });\r\n  }\r\n  resetPassword(token: string, newPassword: string) {\r\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });\r\n  }\r\n  \r\n  checkEmailAvailability(email: string): Observable<any> {\r\n    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);\r\n  }\r\n\r\n  redirectToDashboard(): void {\r\n    const role = this.getUserRole();\r\n    if (role === 'enseignant') {\r\n      this.router.navigate(['/dashboard-enseignant']);\r\n    } else if (role === 'chef departement') {\r\n      this.router.navigate(['/dashboard-chef']);\r\n    } else if (role === 'rapporteur') {\r\n      this.router.navigate(['/dashboard-rapporteur']);\r\n    } else if (role === 'admin') {\r\n      this.router.navigate(['/dashboard-admin']);\r\n    } else if (role === 'president') {\r\n      this.router.navigate(['/president']);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n    getUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  getCurrentUser(): Utilisateur | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": "AAGA,SAAiBA,WAAW,QAAQ,eAAe;;;AAQnD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,UAAqB;IAArB,KAAAA,UAAU,GAAVA,UAAU;IAFrB,KAAAC,OAAO,GAAC,4BAA4B;IACrC,KAAAC,QAAQ,GAAC,iCAAiC;IAGpD,KAAAC,IAAI,GAAgB,IAAIN,WAAW,EAAE;EAFU;EAI7CO,KAAKA,CAACC,YAA0B;IAC9B,OAAO,IAAI,CAACL,UAAU,CAACM,IAAI,CAAM,GAAG,IAAI,CAACL,OAAO,QAAQ,EAAEI,YAAY,CAAC,CACnEE,IAAI,CACJC,GAAG,CAACL,IAAI,IAAG;MACT,IAAIA,IAAI,IAAIA,IAAI,CAACM,KAAK,EAAE;QACtB;QACAC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAER,IAAI,CAACM,KAAK,CAAC;QAC3CC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAER,IAAI,CAACS,QAAQ,CAAC;QACjDF,cAAc,CAACC,OAAO,CAAC,IAAI,EAAER,IAAI,CAACU,EAAE,CAACC,QAAQ,EAAE,CAAC;QAChDJ,cAAc,CAACC,OAAO,CAAC,MAAM,EAAER,IAAI,CAACY,IAAI,CAAC;QACzCL,cAAc,CAACC,OAAO,CAAC,OAAO,EAAER,IAAI,CAACa,KAAK,CAAC;QAE3C;QACA,IAAI,CAACC,kBAAkB,CAACC,IAAI,CAACf,IAAI,CAAC;;IAEtC,CAAC,CAAC,CACH;EACL;EAGAgB,QAAQA,CAACC,KAAkB;IACzB,OAAO,IAAI,CAACpB,UAAU,CAACM,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,WAAW,EAAEmB,KAAK,EAAE;MAC7DC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAC9C,CAAC;EACJ;EACEC,cAAcA,CAAA;IACd,OAAO,IAAI,CAACtB,UAAU,CAACuB,GAAG,CAAgB,GAAG,IAAI,CAACtB,OAAO,WAAW,CAAC;EACvE;EACEuB,SAASA,CAAA;IACT,OAAO,IAAI,CAACxB,UAAU,CAACuB,GAAG,CAAU,GAAG,IAAI,CAACrB,QAAQ,SAAS,CAAC;EAChE;EACAuB,cAAcA,CAACT,KAAa;IAC1B,OAAO,IAAI,CAAChB,UAAU,CAACM,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,kBAAkB,EAAE;MAAEe;IAAK,CAAE,EAAE;MAAEU,YAAY,EAAE;IAAM,CAAE,CAAC;EACrG;EACAC,aAAaA,CAAClB,KAAa,EAAEmB,WAAmB;IAC9C,OAAO,IAAI,CAAC5B,UAAU,CAACM,IAAI,CAAC,GAAG,IAAI,CAACL,OAAO,yBAAyBQ,KAAK,EAAE,EAAE;MAAEoB,QAAQ,EAAED;IAAW,CAAE,EAAE;MAAEF,YAAY,EAAE;IAAM,CAAE,CAAC;EACnI;EAEAI,sBAAsBA,CAACd,KAAa;IAClC,OAAO,IAAI,CAAChB,UAAU,CAACuB,GAAG,CAAM,GAAG,IAAI,CAACtB,OAAO,sBAAsBe,KAAK,EAAE,CAAC;EAC/E;EAEAe,mBAAmBA,CAAA;IACjB,MAAMhB,IAAI,GAAG,IAAI,CAACiB,WAAW,EAAE;IAC/B,IAAIjB,IAAI,KAAK,YAAY,EAAE;MACzB,IAAI,CAACkB,MAAM,CAACC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;KAChD,MAAM,IAAInB,IAAI,KAAK,kBAAkB,EAAE;MACtC,IAAI,CAACkB,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;KAC1C,MAAM,IAAInB,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAACkB,MAAM,CAACC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;KAChD,MAAM,IAAInB,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACkB,MAAM,CAACC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;KAC3C,MAAM,IAAInB,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,CAACkB,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;KACrC,MAAM;MACL,IAAI,CAACD,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEEF,WAAWA,CAAA;IACX,MAAM7B,IAAI,GAAG,IAAI,CAACgC,cAAc,EAAE;IAClC,OAAOhC,IAAI,GAAGA,IAAI,CAACY,IAAI,GAAG,IAAI;EAChC;EAEAoB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClB,kBAAkB,CAACmB,KAAK;EACtC;;;uBAzEWtC,kBAAkB,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB1C,kBAAkB;MAAA2C,OAAA,EAAlB3C,kBAAkB,CAAA4C,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { Utilisateur } from './utilisateur';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class UtilisateurService {\n  constructor(httpClient, router) {\n    this.httpClient = httpClient;\n    this.router = router;\n    this.baseURL = \"http://localhost:8085/auth\";\n    this.baseURL1 = \"http://localhost:8085/api/users\";\n    this.isLoggingOut = false;\n    this.user = new Utilisateur();\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.navigationItems = [{\n      id: 'dashboard-chef',\n      title: '<PERSON>au de Bord',\n      icon: 'ti ti-layout-dashboard',\n      route: '/',\n      roles: ['ADMIN', 'USER']\n    }, {\n      id: 'Types',\n      title: 'Type',\n      icon: 'ti ti-layout-dashboard',\n      route: '/dashboard',\n      roles: ['ADMIN']\n    }, {\n      id: 'Mar<PERSON>',\n      title: 'Mar<PERSON>',\n      icon: 'ti ti-layout-dashboard',\n      route: '/marque',\n      roles: ['ADMIN']\n    }, {\n      id: 'Models',\n      title: 'Models',\n      icon: 'ti ti-layout-dashboard',\n      route: '/Model',\n      roles: ['ADMIN']\n    }, {\n      id: 'Equipement_Admin',\n      title: 'Equipements',\n      icon: 'ti ti-layout-dashboard',\n      route: '/equipement',\n      roles: ['ADMIN']\n    }, {\n      id: 'Fournisseur',\n      title: 'Fournisseurs',\n      icon: 'ti ti-layout-dashboard',\n      route: '/fournisseur',\n      roles: ['ADMIN']\n    }, {\n      id: 'Historique',\n      title: 'Historique',\n      icon: 'ti ti-edit-circle',\n      route: '/historique',\n      roles: ['ADMIN', 'USER']\n    }];\n    this.loadUserFromStorage();\n  }\n  login(loginRequest) {\n    return this.httpClient.post(`${this.baseURL}/login`, loginRequest).pipe(tap(user => {\n      if (user && user.token) {\n        // Store user data in session storage\n        sessionStorage.setItem('token', user.token);\n        sessionStorage.setItem('username', user.username);\n        sessionStorage.setItem('registrationNumber', user.registrationNumber);\n        sessionStorage.setItem('role', user.role);\n        sessionStorage.setItem('email', user.email);\n        // Update current user subject\n        this.currentUserSubject.next(user);\n      }\n    }));\n  }\n  register(agent) {\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  getUtilisateur() {\n    return this.httpClient.get(`${this.baseURL}/AllUsers`);\n  }\n  getAgents() {\n    return this.httpClient.get(`${this.baseURL1}/agents`);\n  }\n  forgotPassword(email) {\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, {\n      email\n    }, {\n      responseType: 'text'\n    });\n  }\n  resetPassword(token, newPassword) {\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, {\n      password: newPassword\n    }, {\n      responseType: 'text'\n    });\n  }\n  checkEmailAvailability(email) {\n    return this.httpClient.get(`${this.baseURL}/check-email?email=${email}`);\n  }\n  loadUserFromStorage() {\n    const token = sessionStorage.getItem('token');\n    const username = sessionStorage.getItem('username');\n    const registrationNumber = sessionStorage.getItem('registrationNumber');\n    const role = sessionStorage.getItem('role');\n    const email = sessionStorage.getItem('email');\n  }\n  redirectToDashboard() {\n    const role = this.getUserRole();\n    if (role === 'USER') {\n      this.router.navigate(['/equipementDSI']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  getUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  logout() {\n    if (this.isLoggingOut) return; // ← 🔁 évite double appel\n    this.isLoggingOut = true;\n    try {\n      // Clear session storage\n      sessionStorage.clear();\n      this.currentUserSubject.next(null);\n      // Naviguer vers login\n      this.router.navigate(['/utilisateur'], {\n        replaceUrl: true\n      }).finally(() => {\n        this.isLoggingOut = false;\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n      this.isLoggingOut = false;\n    }\n  }\n  getNavigationItems(userRole) {\n    return this.navigationItems.filter(item => item.roles.includes(userRole));\n  }\n  static {\n    this.ɵfac = function UtilisateurService_Factory(t) {\n      return new (t || UtilisateurService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilisateurService,\n      factory: UtilisateurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "Utilisa<PERSON>ur", "UtilisateurService", "constructor", "httpClient", "router", "baseURL", "baseURL1", "isLoggingOut", "user", "currentUserSubject", "currentUser$", "asObservable", "navigationItems", "id", "title", "icon", "route", "roles", "loadUserFromStorage", "login", "loginRequest", "post", "pipe", "token", "sessionStorage", "setItem", "username", "registrationNumber", "role", "email", "next", "register", "agent", "headers", "getUtilisateur", "get", "getAgents", "forgotPassword", "responseType", "resetPassword", "newPassword", "password", "checkEmailAvailability", "getItem", "redirectToDashboard", "getUserRole", "navigate", "getCurrentUser", "value", "logout", "clear", "replaceUrl", "finally", "error", "console", "getNavigationItems", "userRole", "filter", "item", "includes", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient,HttpClientModule } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\r\nimport {  Agent, Utilisateur } from './utilisateur';\r\nimport { Router } from '@angular/router';\r\nexport interface LoginRequest {\r\n  registrationNumber: string;\r\n  password: string;\r\n}\r\nexport interface NavigationItem {\r\n  id: string;\r\n  title: string;\r\n  icon: string;\r\n  route?: string;\r\n  action?: () => void;\r\n  roles: string[];\r\n  children?: NavigationItem[];\r\n}\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilisateurService {\r\n   private baseURL=\"http://localhost:8085/auth\";\r\n  private baseURL1=\"http://localhost:8085/api/users\";\r\n  constructor(private httpClient:HttpClient,private router: Router) { \r\n\r\nthis.loadUserFromStorage();\r\n\r\n  }\r\n\r\n\r\n  private isLoggingOut: boolean = false;\r\n\r\nuser: Utilisateur = new Utilisateur();\r\n  private currentUserSubject = new BehaviorSubject<Utilisateur | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  \r\n  login(loginRequest: LoginRequest): Observable<any> {\r\n    return this.httpClient.post<any>(`${this.baseURL}/login`, loginRequest)\r\n       .pipe(\r\n        tap(user => {\r\n          if (user && user.token) {\r\n            // Store user data in session storage\r\n            sessionStorage.setItem('token', user.token);\r\n            sessionStorage.setItem('username', user.username);\r\n            sessionStorage.setItem('registrationNumber', user.registrationNumber);\r\n            sessionStorage.setItem('role', user.role);\r\n            sessionStorage.setItem('email', user.email);\r\n            \r\n            // Update current user subject\r\n            this.currentUserSubject.next(user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n  \r\n\r\n  register(agent: Utilisateur): Observable<any> {\r\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n    getUtilisateur(): Observable<Utilisateur[]> {\r\n    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);\r\n  }\r\n    getAgents(): Observable<Agent[]> {\r\n    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);\r\n  }\r\n  forgotPassword(email: string) {\r\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });\r\n  }\r\n  resetPassword(token: string, newPassword: string) {\r\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });\r\n  }\r\n  \r\n  checkEmailAvailability(email: string): Observable<any> {\r\n    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);\r\n  }\r\n\r\n\r\n  private loadUserFromStorage(): void {\r\n    const token = sessionStorage.getItem('token');\r\n    const username = sessionStorage.getItem('username');\r\n    const registrationNumber = sessionStorage.getItem('registrationNumber');\r\n    const role = sessionStorage.getItem('role');\r\n    const email = sessionStorage.getItem('email');\r\n\r\n\r\n  }\r\n\r\n\r\n\r\n\r\n  redirectToDashboard(): void {\r\n    const role = this.getUserRole();\r\n    if (role === 'USER') {\r\n      this.router.navigate(['/equipementDSI']);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n    getUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  getCurrentUser(): Utilisateur | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n\r\n\r\n\r\nlogout(): void {\r\n  if (this.isLoggingOut) return; // ← 🔁 évite double appel\r\n  this.isLoggingOut = true;\r\n\r\n  try {\r\n    // Clear session storage\r\n    sessionStorage.clear();\r\n    this.currentUserSubject.next(null);\r\n\r\n    // Naviguer vers login\r\n    this.router.navigate(['/utilisateur'], { replaceUrl: true }).finally(() => {\r\n      this.isLoggingOut = false;\r\n    });\r\n\r\n  } catch (error) {\r\n    console.error('Logout error:', error);\r\n    this.isLoggingOut = false;\r\n  }\r\n}\r\n\r\n private navigationItems: NavigationItem[] = [\r\n    {\r\n      id: 'dashboard-chef',\r\n      title: 'Tableau de Bord',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/',\r\n      roles: ['ADMIN','USER']\r\n    },\r\n       {\r\n      id: 'Types',\r\n      title: 'Type',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/dashboard',\r\n      roles: ['ADMIN']\r\n    },\r\n    {\r\n      id: 'Marques',\r\n      title: 'Marques',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/marque',\r\n      roles: ['ADMIN']\r\n    },\r\n    {\r\n      id: 'Models',\r\n      title: 'Models',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/Model',\r\n      roles: ['ADMIN']\r\n    },\r\n    {\r\n      id: 'Equipement_Admin',\r\n      title: 'Equipements',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/equipement',\r\n      roles: ['ADMIN']\r\n    },\r\n        {\r\n      id: 'Fournisseur',\r\n      title: 'Fournisseurs',\r\n      icon: 'ti ti-layout-dashboard',\r\n      route: '/fournisseur',\r\n      roles: ['ADMIN']\r\n    },\r\n    {\r\n      id: 'Historique',\r\n      title: 'Historique',\r\n      icon: 'ti ti-edit-circle',\r\n      route: '/historique',\r\n      roles: ['ADMIN','USER']\r\n    \r\n    }\r\n  ];\r\n\r\n\r\n\r\n\r\n  getNavigationItems(userRole: string): NavigationItem[] {\r\n    return this.navigationItems.filter(item => \r\n      item.roles.includes(userRole)\r\n    );\r\n  }\r\n\r\n\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AACvD,SAAiBC,WAAW,QAAQ,eAAe;;;;AAkBnD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,UAAqB,EAASC,MAAc;IAA5C,KAAAD,UAAU,GAAVA,UAAU;IAAoB,KAAAC,MAAM,GAANA,MAAM;IAF/C,KAAAC,OAAO,GAAC,4BAA4B;IACrC,KAAAC,QAAQ,GAAC,iCAAiC;IAQ1C,KAAAC,YAAY,GAAY,KAAK;IAEvC,KAAAC,IAAI,GAAgB,IAAIR,WAAW,EAAE;IAC3B,KAAAS,kBAAkB,GAAG,IAAIX,eAAe,CAAqB,IAAI,CAAC;IACnE,KAAAY,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAmGrD,KAAAC,eAAe,GAAqB,CACzC;MACEC,EAAE,EAAE,gBAAgB;MACpBC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,CAAC,OAAO,EAAC,MAAM;KACvB,EACE;MACDJ,EAAE,EAAE,OAAO;MACXC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,YAAY;MACnBC,KAAK,EAAE,CAAC,OAAO;KAChB,EACD;MACEJ,EAAE,EAAE,SAAS;MACbC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,CAAC,OAAO;KAChB,EACD;MACEJ,EAAE,EAAE,QAAQ;MACZC,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,QAAQ;MACfC,KAAK,EAAE,CAAC,OAAO;KAChB,EACD;MACEJ,EAAE,EAAE,kBAAkB;MACtBC,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,CAAC,OAAO;KAChB,EACG;MACFJ,EAAE,EAAE,aAAa;MACjBC,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE,wBAAwB;MAC9BC,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,CAAC,OAAO;KAChB,EACD;MACEJ,EAAE,EAAE,YAAY;MAChBC,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE,mBAAmB;MACzBC,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,CAAC,OAAO,EAAC,MAAM;KAEvB,CACF;IA/JH,IAAI,CAACC,mBAAmB,EAAE;EAExB;EASAC,KAAKA,CAACC,YAA0B;IAC9B,OAAO,IAAI,CAACjB,UAAU,CAACkB,IAAI,CAAM,GAAG,IAAI,CAAChB,OAAO,QAAQ,EAAEe,YAAY,CAAC,CACnEE,IAAI,CACJvB,GAAG,CAACS,IAAI,IAAG;MACT,IAAIA,IAAI,IAAIA,IAAI,CAACe,KAAK,EAAE;QACtB;QACAC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEjB,IAAI,CAACe,KAAK,CAAC;QAC3CC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEjB,IAAI,CAACkB,QAAQ,CAAC;QACjDF,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAEjB,IAAI,CAACmB,kBAAkB,CAAC;QACrEH,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEjB,IAAI,CAACoB,IAAI,CAAC;QACzCJ,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEjB,IAAI,CAACqB,KAAK,CAAC;QAE3C;QACA,IAAI,CAACpB,kBAAkB,CAACqB,IAAI,CAACtB,IAAI,CAAC;;IAEtC,CAAC,CAAC,CACH;EACL;EAGAuB,QAAQA,CAACC,KAAkB;IACzB,OAAO,IAAI,CAAC7B,UAAU,CAACkB,IAAI,CAAC,GAAG,IAAI,CAAChB,OAAO,WAAW,EAAE2B,KAAK,EAAE;MAC7DC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAC9C,CAAC;EACJ;EACEC,cAAcA,CAAA;IACd,OAAO,IAAI,CAAC/B,UAAU,CAACgC,GAAG,CAAgB,GAAG,IAAI,CAAC9B,OAAO,WAAW,CAAC;EACvE;EACE+B,SAASA,CAAA;IACT,OAAO,IAAI,CAACjC,UAAU,CAACgC,GAAG,CAAU,GAAG,IAAI,CAAC7B,QAAQ,SAAS,CAAC;EAChE;EACA+B,cAAcA,CAACR,KAAa;IAC1B,OAAO,IAAI,CAAC1B,UAAU,CAACkB,IAAI,CAAC,GAAG,IAAI,CAAChB,OAAO,kBAAkB,EAAE;MAAEwB;IAAK,CAAE,EAAE;MAAES,YAAY,EAAE;IAAM,CAAE,CAAC;EACrG;EACAC,aAAaA,CAAChB,KAAa,EAAEiB,WAAmB;IAC9C,OAAO,IAAI,CAACrC,UAAU,CAACkB,IAAI,CAAC,GAAG,IAAI,CAAChB,OAAO,yBAAyBkB,KAAK,EAAE,EAAE;MAAEkB,QAAQ,EAAED;IAAW,CAAE,EAAE;MAAEF,YAAY,EAAE;IAAM,CAAE,CAAC;EACnI;EAEAI,sBAAsBA,CAACb,KAAa;IAClC,OAAO,IAAI,CAAC1B,UAAU,CAACgC,GAAG,CAAM,GAAG,IAAI,CAAC9B,OAAO,sBAAsBwB,KAAK,EAAE,CAAC;EAC/E;EAGQX,mBAAmBA,CAAA;IACzB,MAAMK,KAAK,GAAGC,cAAc,CAACmB,OAAO,CAAC,OAAO,CAAC;IAC7C,MAAMjB,QAAQ,GAAGF,cAAc,CAACmB,OAAO,CAAC,UAAU,CAAC;IACnD,MAAMhB,kBAAkB,GAAGH,cAAc,CAACmB,OAAO,CAAC,oBAAoB,CAAC;IACvE,MAAMf,IAAI,GAAGJ,cAAc,CAACmB,OAAO,CAAC,MAAM,CAAC;IAC3C,MAAMd,KAAK,GAAGL,cAAc,CAACmB,OAAO,CAAC,OAAO,CAAC;EAG/C;EAKAC,mBAAmBA,CAAA;IACjB,MAAMhB,IAAI,GAAG,IAAI,CAACiB,WAAW,EAAE;IAC/B,IAAIjB,IAAI,KAAK,MAAM,EAAE;MACnB,IAAI,CAACxB,MAAM,CAAC0C,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;KACzC,MAAM;MACL,IAAI,CAAC1C,MAAM,CAAC0C,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEED,WAAWA,CAAA;IACX,MAAMrC,IAAI,GAAG,IAAI,CAACuC,cAAc,EAAE;IAClC,OAAOvC,IAAI,GAAGA,IAAI,CAACoB,IAAI,GAAG,IAAI;EAChC;EAEAmB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACtC,kBAAkB,CAACuC,KAAK;EACtC;EAKFC,MAAMA,CAAA;IACJ,IAAI,IAAI,CAAC1C,YAAY,EAAE,OAAO,CAAC;IAC/B,IAAI,CAACA,YAAY,GAAG,IAAI;IAExB,IAAI;MACF;MACAiB,cAAc,CAAC0B,KAAK,EAAE;MACtB,IAAI,CAACzC,kBAAkB,CAACqB,IAAI,CAAC,IAAI,CAAC;MAElC;MACA,IAAI,CAAC1B,MAAM,CAAC0C,QAAQ,CAAC,CAAC,cAAc,CAAC,EAAE;QAAEK,UAAU,EAAE;MAAI,CAAE,CAAC,CAACC,OAAO,CAAC,MAAK;QACxE,IAAI,CAAC7C,YAAY,GAAG,KAAK;MAC3B,CAAC,CAAC;KAEH,CAAC,OAAO8C,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,IAAI,CAAC9C,YAAY,GAAG,KAAK;;EAE7B;EA0DEgD,kBAAkBA,CAACC,QAAgB;IACjC,OAAO,IAAI,CAAC5C,eAAe,CAAC6C,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACzC,KAAK,CAAC0C,QAAQ,CAACH,QAAQ,CAAC,CAC9B;EACH;;;uBA7KWvD,kBAAkB,EAAA2D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAlBhE,kBAAkB;MAAAiE,OAAA,EAAlBjE,kBAAkB,CAAAkE,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
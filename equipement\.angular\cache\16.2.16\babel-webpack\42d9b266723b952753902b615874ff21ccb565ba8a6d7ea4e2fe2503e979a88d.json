{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { Utilisateur } from './utilisateur';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class UtilisateurService {\n  constructor(httpClient, router) {\n    this.httpClient = httpClient;\n    this.router = router;\n    this.baseURL = \"http://localhost:8085/auth\";\n    this.baseURL1 = \"http://localhost:8085/api/users\";\n    this.user = new Utilisateur();\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n  }\n  login(loginRequest) {\n    return this.httpClient.post(`${this.baseURL}/login`, loginRequest).pipe(tap(user => {\n      if (user && user.token) {\n        // Store user data in session storage\n        sessionStorage.setItem('token', user.token);\n        sessionStorage.setItem('username', user.username);\n        sessionStorage.setItem('registrationNumber', user.registrationNumber);\n        sessionStorage.setItem('role', user.role);\n        sessionStorage.setItem('email', user.email);\n        // Update current user subject\n        this.currentUserSubject.next(user);\n      }\n    }));\n  }\n  register(agent) {\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  getUtilisateur() {\n    return this.httpClient.get(`${this.baseURL}/AllUsers`);\n  }\n  getAgents() {\n    return this.httpClient.get(`${this.baseURL1}/agents`);\n  }\n  forgotPassword(email) {\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, {\n      email\n    }, {\n      responseType: 'text'\n    });\n  }\n  resetPassword(token, newPassword) {\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, {\n      password: newPassword\n    }, {\n      responseType: 'text'\n    });\n  }\n  checkEmailAvailability(email) {\n    return this.httpClient.get(`${this.baseURL}/check-email?email=${email}`);\n  }\n  loadUserFromStorage() {\n    const token = sessionStorage.getItem('token');\n    const username = sessionStorage.getItem('username');\n    const id = sessionStorage.getItem('re');\n    const role = sessionStorage.getItem('role');\n    const email = sessionStorage.getItem('email');\n  }\n  redirectToDashboard() {\n    const role = this.getUserRole();\n    if (role === 'USER') {\n      this.router.navigate(['/equipementDSI']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  getUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  static {\n    this.ɵfac = function UtilisateurService_Factory(t) {\n      return new (t || UtilisateurService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilisateurService,\n      factory: UtilisateurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "Utilisa<PERSON>ur", "UtilisateurService", "constructor", "httpClient", "router", "baseURL", "baseURL1", "user", "currentUserSubject", "currentUser$", "asObservable", "login", "loginRequest", "post", "pipe", "token", "sessionStorage", "setItem", "username", "registrationNumber", "role", "email", "next", "register", "agent", "headers", "getUtilisateur", "get", "getAgents", "forgotPassword", "responseType", "resetPassword", "newPassword", "password", "checkEmailAvailability", "loadUserFromStorage", "getItem", "id", "redirectToDashboard", "getUserRole", "navigate", "getCurrentUser", "value", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient,HttpClientModule } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\r\nimport {  Agent, Utilisateur } from './utilisateur';\r\nimport { Router } from '@angular/router';\r\nexport interface LoginRequest {\r\n  registrationNumber: string;\r\n  password: string;\r\n}\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilisateurService {\r\n   private baseURL=\"http://localhost:8085/auth\";\r\n  private baseURL1=\"http://localhost:8085/api/users\";\r\n  constructor(private httpClient:HttpClient,private router: Router) { }\r\n\r\nuser: Utilisateur = new Utilisateur();\r\n  private currentUserSubject = new BehaviorSubject<Utilisateur | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  \r\n  login(loginRequest: LoginRequest): Observable<any> {\r\n    return this.httpClient.post<any>(`${this.baseURL}/login`, loginRequest)\r\n       .pipe(\r\n        tap(user => {\r\n          if (user && user.token) {\r\n            // Store user data in session storage\r\n            sessionStorage.setItem('token', user.token);\r\n            sessionStorage.setItem('username', user.username);\r\n            sessionStorage.setItem('registrationNumber', user.registrationNumber);\r\n            sessionStorage.setItem('role', user.role);\r\n            sessionStorage.setItem('email', user.email);\r\n            \r\n            // Update current user subject\r\n            this.currentUserSubject.next(user);\r\n          }\r\n        })\r\n      );\r\n  }\r\n  \r\n\r\n  register(agent: Utilisateur): Observable<any> {\r\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n    getUtilisateur(): Observable<Utilisateur[]> {\r\n    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);\r\n  }\r\n    getAgents(): Observable<Agent[]> {\r\n    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);\r\n  }\r\n  forgotPassword(email: string) {\r\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });\r\n  }\r\n  resetPassword(token: string, newPassword: string) {\r\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });\r\n  }\r\n  \r\n  checkEmailAvailability(email: string): Observable<any> {\r\n    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);\r\n  }\r\n\r\n\r\n  private loadUserFromStorage(): void {\r\n    const token = sessionStorage.getItem('token');\r\n    const username = sessionStorage.getItem('username');\r\n    const id = sessionStorage.getItem('re');\r\n    const role = sessionStorage.getItem('role');\r\n    const email = sessionStorage.getItem('email');\r\n\r\n\r\n  }\r\n\r\n\r\n\r\n\r\n  redirectToDashboard(): void {\r\n    const role = this.getUserRole();\r\n    if (role === 'USER') {\r\n      this.router.navigate(['/equipementDSI']);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n    getUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n  getCurrentUser(): Utilisateur | null {\r\n    return this.currentUserSubject.value;\r\n  }\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AACvD,SAAiBC,WAAW,QAAQ,eAAe;;;;AASnD,OAAM,MAAOC,kBAAkB;EAG7BC,YAAoBC,UAAqB,EAASC,MAAc;IAA5C,KAAAD,UAAU,GAAVA,UAAU;IAAoB,KAAAC,MAAM,GAANA,MAAM;IAF/C,KAAAC,OAAO,GAAC,4BAA4B;IACrC,KAAAC,QAAQ,GAAC,iCAAiC;IAGpD,KAAAC,IAAI,GAAgB,IAAIP,WAAW,EAAE;IAC3B,KAAAQ,kBAAkB,GAAG,IAAIV,eAAe,CAAqB,IAAI,CAAC;IACnE,KAAAW,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;EAJQ;EAMpEC,KAAKA,CAACC,YAA0B;IAC9B,OAAO,IAAI,CAACT,UAAU,CAACU,IAAI,CAAM,GAAG,IAAI,CAACR,OAAO,QAAQ,EAAEO,YAAY,CAAC,CACnEE,IAAI,CACJf,GAAG,CAACQ,IAAI,IAAG;MACT,IAAIA,IAAI,IAAIA,IAAI,CAACQ,KAAK,EAAE;QACtB;QACAC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEV,IAAI,CAACQ,KAAK,CAAC;QAC3CC,cAAc,CAACC,OAAO,CAAC,UAAU,EAAEV,IAAI,CAACW,QAAQ,CAAC;QACjDF,cAAc,CAACC,OAAO,CAAC,oBAAoB,EAAEV,IAAI,CAACY,kBAAkB,CAAC;QACrEH,cAAc,CAACC,OAAO,CAAC,MAAM,EAAEV,IAAI,CAACa,IAAI,CAAC;QACzCJ,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEV,IAAI,CAACc,KAAK,CAAC;QAE3C;QACA,IAAI,CAACb,kBAAkB,CAACc,IAAI,CAACf,IAAI,CAAC;;IAEtC,CAAC,CAAC,CACH;EACL;EAGAgB,QAAQA,CAACC,KAAkB;IACzB,OAAO,IAAI,CAACrB,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,WAAW,EAAEmB,KAAK,EAAE;MAC7DC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAC9C,CAAC;EACJ;EACEC,cAAcA,CAAA;IACd,OAAO,IAAI,CAACvB,UAAU,CAACwB,GAAG,CAAgB,GAAG,IAAI,CAACtB,OAAO,WAAW,CAAC;EACvE;EACEuB,SAASA,CAAA;IACT,OAAO,IAAI,CAACzB,UAAU,CAACwB,GAAG,CAAU,GAAG,IAAI,CAACrB,QAAQ,SAAS,CAAC;EAChE;EACAuB,cAAcA,CAACR,KAAa;IAC1B,OAAO,IAAI,CAAClB,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,kBAAkB,EAAE;MAAEgB;IAAK,CAAE,EAAE;MAAES,YAAY,EAAE;IAAM,CAAE,CAAC;EACrG;EACAC,aAAaA,CAAChB,KAAa,EAAEiB,WAAmB;IAC9C,OAAO,IAAI,CAAC7B,UAAU,CAACU,IAAI,CAAC,GAAG,IAAI,CAACR,OAAO,yBAAyBU,KAAK,EAAE,EAAE;MAAEkB,QAAQ,EAAED;IAAW,CAAE,EAAE;MAAEF,YAAY,EAAE;IAAM,CAAE,CAAC;EACnI;EAEAI,sBAAsBA,CAACb,KAAa;IAClC,OAAO,IAAI,CAAClB,UAAU,CAACwB,GAAG,CAAM,GAAG,IAAI,CAACtB,OAAO,sBAAsBgB,KAAK,EAAE,CAAC;EAC/E;EAGQc,mBAAmBA,CAAA;IACzB,MAAMpB,KAAK,GAAGC,cAAc,CAACoB,OAAO,CAAC,OAAO,CAAC;IAC7C,MAAMlB,QAAQ,GAAGF,cAAc,CAACoB,OAAO,CAAC,UAAU,CAAC;IACnD,MAAMC,EAAE,GAAGrB,cAAc,CAACoB,OAAO,CAAC,IAAI,CAAC;IACvC,MAAMhB,IAAI,GAAGJ,cAAc,CAACoB,OAAO,CAAC,MAAM,CAAC;IAC3C,MAAMf,KAAK,GAAGL,cAAc,CAACoB,OAAO,CAAC,OAAO,CAAC;EAG/C;EAKAE,mBAAmBA,CAAA;IACjB,MAAMlB,IAAI,GAAG,IAAI,CAACmB,WAAW,EAAE;IAC/B,IAAInB,IAAI,KAAK,MAAM,EAAE;MACnB,IAAI,CAAChB,MAAM,CAACoC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;KACzC,MAAM;MACL,IAAI,CAACpC,MAAM,CAACoC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEED,WAAWA,CAAA;IACX,MAAMhC,IAAI,GAAG,IAAI,CAACkC,cAAc,EAAE;IAClC,OAAOlC,IAAI,GAAGA,IAAI,CAACa,IAAI,GAAG,IAAI;EAChC;EAEAqB,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACjC,kBAAkB,CAACkC,KAAK;EACtC;;;uBAjFWzC,kBAAkB,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAlB/C,kBAAkB;MAAAgD,OAAA,EAAlBhD,kBAAkB,CAAAiD,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { DashboardComponent } from './dashboard/dashboard.component';\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\nimport { MotpasseComponent } from './motpasse/motpasse.component';\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\nimport { MarqueComponent } from './marque/marque.component';\nimport { ModelComponent } from './model/model.component';\nimport { EquipementComponent } from './equipement/equipement.component';\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\nimport { AffectaComponent } from './affecta/affecta.component';\nimport { AgentComponent } from './agent/agent.component';\nimport { HistoriqueComponent } from './historique/historique.component';\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'dashboard',\n  component: DashboardComponent\n}, {\n  path: 'marque',\n  component: MarqueComponent\n}, {\n  path: 'model',\n  component: ModelComponent\n}, {\n  path: 'utilisateur',\n  component: UtilisateurComponent\n}, {\n  path: 'equipement',\n  component: EquipementComponent\n}, {\n  path: 'fournisseur',\n  component: FournisseurComponent\n}, {\n  path: 'utilisateur-equipement',\n  component: UtilisateurEquipementComponent\n}, {\n  path: 'motpasseoublie',\n  component: MotpasseComponent\n}, {\n  path: 'affecta',\n  component: AffectaComponent\n}, {\n  path: 'agent',\n  component: AgentComponent\n}, {\n  path: 'historique',\n  component: HistoriqueComponent\n}, {\n  path: 'user-registration',\n  component: UserRegistrationComponent\n}, {\n  path: 'equipementDSI',\n  component: EquipementsComponent\n}, {\n  path: '',\n  redirectTo: 'utilisateur',\n  pathMatch: 'full'\n}, {\n  path: 'reset-password',\n  component: ResetPasswordComponent\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "DashboardComponent", "UtilisateurComponent", "MotpasseComponent", "ResetPasswordComponent", "MarqueComponent", "ModelComponent", "EquipementComponent", "FournisseurComponent", "UtilisateurEquipementComponent", "AffectaComponent", "AgentComponent", "HistoriqueComponent", "UserRegistrationComponent", "EquipementsComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { DashboardComponent } from './dashboard/dashboard.component';\r\nimport { UtilisateurComponent } from './utilisateur/utilisateur.component';\r\nimport { MotpasseComponent } from './motpasse/motpasse.component';\r\nimport { ResetPasswordComponent } from './reset-password/reset-password.component';\r\nimport { MarqueComponent } from './marque/marque.component';\r\nimport { ModelComponent } from './model/model.component';\r\nimport { EquipementComponent } from './equipement/equipement.component';\r\nimport { FournisseurComponent } from './fournisseur/fournisseur.component';\r\nimport { UtilisateurEquipementComponent } from './utilisateur-equipement/utilisateur-equipement.component';\r\nimport { AffectaComponent } from './affecta/affecta.component';\r\nimport { AgentComponent } from './agent/agent.component';\r\nimport { HistoriqueComponent } from './historique/historique.component';\r\nimport { UserRegistrationComponent } from './user-registration/user-registration.component';\r\nimport { EquipementsComponent } from './DSI/equipements/equipements.component';\r\nimport { AuthGuard } from './guards/auth.guard';\r\n\r\nconst routes: Routes = [\r\n\r\n{path:'dashboard',component:DashboardComponent},\r\n{path:'marque',component:MarqueComponent},\r\n{path:'model',component:ModelComponent},\r\n{path:'utilisateur',component:UtilisateurComponent},\r\n{path:'equipement',component:EquipementComponent},\r\n{path:'fournisseur',component:FournisseurComponent},\r\n{path:'utilisateur-equipement',component:UtilisateurEquipementComponent},\r\n{path:'motpasseoublie',component:MotpasseComponent},\r\n{path:'affecta',component:AffectaComponent},\r\n\r\n{path:'agent',component:AgentComponent},\r\n{path:'historique',component:HistoriqueComponent},\r\n{path:'user-registration',component:UserRegistrationComponent},\r\n{path:'equipementDSI',component:EquipementsComponent},\r\n\r\n{path:'',redirectTo:'utilisateur',pathMatch:'full'} ,\r\n{ path: 'reset-password', component:ResetPasswordComponent },\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class AppRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,oBAAoB,QAAQ,qCAAqC;AAC1E,SAASC,8BAA8B,QAAQ,2DAA2D;AAC1G,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,oBAAoB,QAAQ,yCAAyC;;;AAG9E,MAAMC,MAAM,GAAW,CAEvB;EAACC,IAAI,EAAC,WAAW;EAACC,SAAS,EAAChB;AAAkB,CAAC,EAC/C;EAACe,IAAI,EAAC,QAAQ;EAACC,SAAS,EAACZ;AAAe,CAAC,EACzC;EAACW,IAAI,EAAC,OAAO;EAACC,SAAS,EAACX;AAAc,CAAC,EACvC;EAACU,IAAI,EAAC,aAAa;EAACC,SAAS,EAACf;AAAoB,CAAC,EACnD;EAACc,IAAI,EAAC,YAAY;EAACC,SAAS,EAACV;AAAmB,CAAC,EACjD;EAACS,IAAI,EAAC,aAAa;EAACC,SAAS,EAACT;AAAoB,CAAC,EACnD;EAACQ,IAAI,EAAC,wBAAwB;EAACC,SAAS,EAACR;AAA8B,CAAC,EACxE;EAACO,IAAI,EAAC,gBAAgB;EAACC,SAAS,EAACd;AAAiB,CAAC,EACnD;EAACa,IAAI,EAAC,SAAS;EAACC,SAAS,EAACP;AAAgB,CAAC,EAE3C;EAACM,IAAI,EAAC,OAAO;EAACC,SAAS,EAACN;AAAc,CAAC,EACvC;EAACK,IAAI,EAAC,YAAY;EAACC,SAAS,EAACL;AAAmB,CAAC,EACjD;EAACI,IAAI,EAAC,mBAAmB;EAACC,SAAS,EAACJ;AAAyB,CAAC,EAC9D;EAACG,IAAI,EAAC,eAAe;EAACC,SAAS,EAACH;AAAoB,CAAC,EAErD;EAACE,IAAI,EAAC,EAAE;EAACE,UAAU,EAAC,aAAa;EAACC,SAAS,EAAC;AAAM,CAAC,EACnD;EAAEH,IAAI,EAAE,gBAAgB;EAAEC,SAAS,EAACb;AAAsB,CAAE,CAE3D;AAMD,OAAM,MAAOgB,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBpB,YAAY,CAACqB,OAAO,CAACN,MAAM,CAAC,EAC5Bf,YAAY;IAAA;EAAA;;;2EAEXoB,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAFjBxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
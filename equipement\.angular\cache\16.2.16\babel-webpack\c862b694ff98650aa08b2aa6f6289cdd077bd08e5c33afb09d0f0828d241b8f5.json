{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../utilisateur/utilisateur.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nconst _c0 = function (a0) {\n  return [a0];\n};\nfunction NavebarComponent_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 10)(1, \"a\", 11);\n    i0.ɵɵelement(2, \"i\");\n    i0.ɵɵelementStart(3, \"span\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(5, _c0, item_r1.route));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(item_r1.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.title);\n  }\n}\nexport class NavebarComponent {\n  constructor(utilisateurService) {\n    this.utilisateurService = utilisateurService;\n    this.navigationItems = [];\n  }\n  ngOnInit() {\n    this.utilisateurService.currentUser$.subscribe(user => {\n      if (user) {\n        this.navigationItems = this.utilisateurService.getNavigationItems(user.role);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NavebarComponent_Factory(t) {\n      return new (t || NavebarComponent)(i0.ɵɵdirectiveInject(i1.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavebarComponent,\n      selectors: [[\"app-navebar\"]],\n      decls: 13,\n      vars: 1,\n      consts: [[1, \"left-sidebar\"], [\"href\", \"./index.html\"], [\"src\", \"assets/images/logos/ommp.png\", \"alt\", \"\", 2, \"width\", \"180px\", \"height\", \"auto\", \"display\", \"block\", \"margin-left\", \"40px\"], [\"id\", \"sidebarCollapse\", 1, \"close-btn\", \"d-xl-none\", \"d-block\", \"sidebartoggler\", \"cursor-pointer\"], [1, \"ti\", \"ti-x\", \"fs-6\"], [\"data-simplebar\", \"\", 1, \"sidebar-nav\", \"scroll-sidebar\"], [\"id\", \"sidebarnav\"], [1, \"nav-small-cap\"], [1, \"hide-menu\"], [\"class\", \"sidebar-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"sidebar-item\"], [\"aria-expanded\", \"false\", 1, \"sidebar-link\", 3, \"routerLink\"]],\n      template: function NavebarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"aside\", 0)(1, \"div\")(2, \"div\")(3, \"a\", 1);\n          i0.ɵɵelement(4, \"img\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 3);\n          i0.ɵɵelement(6, \"i\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"nav\", 5)(8, \"ul\", 6)(9, \"li\", 7)(10, \"span\", 8);\n          i0.ɵɵtext(11, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(12, NavebarComponent_li_12_Template, 5, 7, \"li\", 9);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.navigationItems);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.RouterLink],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "item_r1", "route", "ɵɵclassMap", "icon", "ɵɵtextInterpolate", "title", "NavebarComponent", "constructor", "utilisateurService", "navigationItems", "ngOnInit", "currentUser$", "subscribe", "user", "getNavigationItems", "role", "ɵɵdirectiveInject", "i1", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "NavebarComponent_Template", "rf", "ctx", "ɵɵtemplate", "NavebarComponent_li_12_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\navebar\\navebar.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\navebar\\navebar.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { NavigationItem, UtilisateurService } from '../utilisateur/utilisateur.service';\r\n\r\n@Component({\r\n  selector: 'app-navebar',\r\n  templateUrl: './navebar.component.html',\r\n  styleUrls: ['./navebar.component.css']\r\n})\r\nexport class NavebarComponent implements OnInit {\r\n  constructor(private utilisateurService: UtilisateurService) { }\r\n\r\n  navigationItems: NavigationItem[] = [];\r\n\r\n\r\n  ngOnInit(): void {\r\n  this.utilisateurService.currentUser$.subscribe(user => {\r\n      if (user) {\r\n        this.navigationItems = this.utilisateurService.getNavigationItems(user.role);\r\n      }\r\n    });\r\n}\r\n\r\n}\r\n", "  <aside class=\"left-sidebar\">\r\n  <!-- Sidebar scroll-->\r\n  <div>\r\n    <div>\r\n      <a href=\"./index.html\">\r\n      <img src=\"assets/images/logos/ommp.png\" alt=\"\" style=\"width: 180px; height: auto; display: block; margin-left: 40px;\" />\r\n\r\n      </a>\r\n      <div class=\"close-btn d-xl-none d-block sidebartoggler cursor-pointer\" id=\"sidebarCollapse\">\r\n        <i class=\"ti ti-x fs-6\"></i>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Sidebar navigation-->\r\n    <nav class=\"sidebar-nav scroll-sidebar\" data-simplebar=\"\">\r\n   <ul id=\"sidebarnav\">\r\n  <li class=\"nav-small-cap\">\r\n    <span class=\"hide-menu\">Home</span>\r\n  </li>\r\n\r\n  <li class=\"sidebar-item\" *ngFor=\"let item of navigationItems\">\r\n    <a class=\"sidebar-link\" [routerLink]=\"[item.route]\" aria-expanded=\"false\">\r\n      <i class=\"{{ item.icon }}\"></i>\r\n      <span class=\"hide-menu\">{{ item.title }}</span>\r\n    </a>\r\n  </li>\r\n</ul>\r\n\r\n    </nav>\r\n    <!-- End Sidebar navigation -->\r\n\r\n  </div>\r\n  <!-- End Sidebar scroll-->\r\n</aside>"], "mappings": ";;;;;;;;;ICoBEA,EAAA,CAAAC,cAAA,aAA8D;IAE1DD,EAAA,CAAAE,SAAA,QAA+B;IAC/BF,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAFzBJ,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAAC,KAAA,EAA2B;IAC9CV,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAW,UAAA,CAAAF,OAAA,CAAAG,IAAA,CAAuB;IACFZ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAa,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;;;ADf9C,OAAM,MAAOC,gBAAgB;EAC3BC,YAAoBC,kBAAsC;IAAtC,KAAAA,kBAAkB,GAAlBA,kBAAkB;IAEtC,KAAAC,eAAe,GAAqB,EAAE;EAFwB;EAK9DC,QAAQA,CAAA;IACR,IAAI,CAACF,kBAAkB,CAACG,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAClD,IAAIA,IAAI,EAAE;QACR,IAAI,CAACJ,eAAe,GAAG,IAAI,CAACD,kBAAkB,CAACM,kBAAkB,CAACD,IAAI,CAACE,IAAI,CAAC;;IAEhF,CAAC,CAAC;EACN;;;uBAZaT,gBAAgB,EAAAf,EAAA,CAAAyB,iBAAA,CAAAC,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAAhBZ,gBAAgB;MAAAa,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR3BlC,EAAA,CAAAC,cAAA,eAA4B;UAKxBD,EAAA,CAAAE,SAAA,aAAwH;UAExHF,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,aAA4F;UAC1FD,EAAA,CAAAE,SAAA,WAA4B;UAC9BF,EAAA,CAAAI,YAAA,EAAM;UAIRJ,EAAA,CAAAC,cAAA,aAA0D;UAGlCD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAGrCJ,EAAA,CAAAoC,UAAA,KAAAC,+BAAA,gBAKK;UACPrC,EAAA,CAAAI,YAAA,EAAK;;;UANuCJ,EAAA,CAAAK,SAAA,IAAkB;UAAlBL,EAAA,CAAAM,UAAA,YAAA6B,GAAA,CAAAjB,eAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
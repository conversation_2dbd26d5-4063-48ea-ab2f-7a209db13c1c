{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class UtilisateurService {\n  constructor(httpClient) {\n    this.httpClient = httpClient;\n    this.baseURL = \"http://localhost:8085/auth\";\n    this.baseURL1 = \"http://localhost:8085/api/users\";\n  }\n  login(user) {\n    return this.httpClient.post(`${this.baseURL}/login`, user);\n  }\n  register(agent) {\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    });\n  }\n  getUtilisateur() {\n    return this.httpClient.get(`${this.baseURL}/AllUsers`);\n  }\n  getAgents() {\n    return this.httpClient.get(`${this.baseURL1}/agents`);\n  }\n  forgotPassword(email) {\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, {\n      email\n    }, {\n      responseType: 'text'\n    });\n  }\n  resetPassword(token, newPassword) {\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, {\n      password: newPassword\n    }, {\n      responseType: 'text'\n    });\n  }\n  checkEmailAvailability(email) {\n    return this.httpClient.get(`${this.baseURL}/check-email?email=${email}`);\n  }\n  redirectToDashboard() {\n    const role = this.getUserRole();\n    if (role === 'enseignant') {\n      this.router.navigate(['/dashboard-enseignant']);\n    } else if (role === 'chef departement') {\n      this.router.navigate(['/dashboard-chef']);\n    } else if (role === 'rapporteur') {\n      this.router.navigate(['/dashboard-rapporteur']);\n    } else if (role === 'admin') {\n      this.router.navigate(['/dashboard-admin']);\n    } else if (role === 'president') {\n      this.router.navigate(['/president']);\n    } else {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  getUserRole() {\n    const user = this.getCurrentUser();\n    return user ? user.role : null;\n  }\n  static {\n    this.ɵfac = function UtilisateurService_Factory(t) {\n      return new (t || UtilisateurService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UtilisateurService,\n      factory: UtilisateurService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["UtilisateurService", "constructor", "httpClient", "baseURL", "baseURL1", "login", "user", "post", "register", "agent", "headers", "getUtilisateur", "get", "getAgents", "forgotPassword", "email", "responseType", "resetPassword", "token", "newPassword", "password", "checkEmailAvailability", "redirectToDashboard", "role", "getUserRole", "router", "navigate", "getCurrentUser", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\utilisateur\\utilisateur.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient,HttpClientModule } from '@angular/common/http';\r\nimport { Observable } from 'rxjs';\r\nimport {  Agent, Utilisateur } from './utilisateur';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class UtilisateurService {\r\n   private baseURL=\"http://localhost:8085/auth\";\r\n  private baseURL1=\"http://localhost:8085/api/users\";\r\n  constructor(private httpClient:HttpClient) { }\r\n  login(user: { registrationNumber: string; password: string }): Observable<any> {\r\n    return this.httpClient.post<any>(`${this.baseURL}/login`, user);\r\n  }\r\n  \r\n\r\n  register(agent: Utilisateur): Observable<any> {\r\n    return this.httpClient.post(`${this.baseURL}/register`, agent, {\r\n      headers: { 'Content-Type': 'application/json' }\r\n    });\r\n  }\r\n    getUtilisateur(): Observable<Utilisateur[]> {\r\n    return this.httpClient.get<Utilisateur[]>(`${this.baseURL}/AllUsers`);\r\n  }\r\n    getAgents(): Observable<Agent[]> {\r\n    return this.httpClient.get<Agent[]>(`${this.baseURL1}/agents`);\r\n  }\r\n  forgotPassword(email: string) {\r\n    return this.httpClient.post(`${this.baseURL}/forgot-password`, { email }, { responseType: 'text' });\r\n  }\r\n  resetPassword(token: string, newPassword: string) {\r\n    return this.httpClient.post(`${this.baseURL}/reset-password?token=${token}`, { password: newPassword }, { responseType: 'text' });\r\n  }\r\n  \r\n  checkEmailAvailability(email: string): Observable<any> {\r\n    return this.httpClient.get<any>(`${this.baseURL}/check-email?email=${email}`);\r\n  }\r\n\r\n  redirectToDashboard(): void {\r\n    const role = this.getUserRole();\r\n    if (role === 'enseignant') {\r\n      this.router.navigate(['/dashboard-enseignant']);\r\n    } else if (role === 'chef departement') {\r\n      this.router.navigate(['/dashboard-chef']);\r\n    } else if (role === 'rapporteur') {\r\n      this.router.navigate(['/dashboard-rapporteur']);\r\n    } else if (role === 'admin') {\r\n      this.router.navigate(['/dashboard-admin']);\r\n    } else if (role === 'president') {\r\n      this.router.navigate(['/president']);\r\n    } else {\r\n      this.router.navigate(['/dashboard']);\r\n    }\r\n  }\r\n\r\n    getUserRole(): string | null {\r\n    const user = this.getCurrentUser();\r\n    return user ? user.role : null;\r\n  }\r\n\r\n\r\n\r\n\r\n\r\n\r\n}\r\n"], "mappings": ";;AAQA,OAAM,MAAOA,kBAAkB;EAG7BC,YAAoBC,UAAqB;IAArB,KAAAA,UAAU,GAAVA,UAAU;IAFrB,KAAAC,OAAO,GAAC,4BAA4B;IACrC,KAAAC,QAAQ,GAAC,iCAAiC;EACL;EAC7CC,KAAKA,CAACC,IAAsD;IAC1D,OAAO,IAAI,CAACJ,UAAU,CAACK,IAAI,CAAM,GAAG,IAAI,CAACJ,OAAO,QAAQ,EAAEG,IAAI,CAAC;EACjE;EAGAE,QAAQA,CAACC,KAAkB;IACzB,OAAO,IAAI,CAACP,UAAU,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,WAAW,EAAEM,KAAK,EAAE;MAC7DC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAC9C,CAAC;EACJ;EACEC,cAAcA,CAAA;IACd,OAAO,IAAI,CAACT,UAAU,CAACU,GAAG,CAAgB,GAAG,IAAI,CAACT,OAAO,WAAW,CAAC;EACvE;EACEU,SAASA,CAAA;IACT,OAAO,IAAI,CAACX,UAAU,CAACU,GAAG,CAAU,GAAG,IAAI,CAACR,QAAQ,SAAS,CAAC;EAChE;EACAU,cAAcA,CAACC,KAAa;IAC1B,OAAO,IAAI,CAACb,UAAU,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,kBAAkB,EAAE;MAAEY;IAAK,CAAE,EAAE;MAAEC,YAAY,EAAE;IAAM,CAAE,CAAC;EACrG;EACAC,aAAaA,CAACC,KAAa,EAAEC,WAAmB;IAC9C,OAAO,IAAI,CAACjB,UAAU,CAACK,IAAI,CAAC,GAAG,IAAI,CAACJ,OAAO,yBAAyBe,KAAK,EAAE,EAAE;MAAEE,QAAQ,EAAED;IAAW,CAAE,EAAE;MAAEH,YAAY,EAAE;IAAM,CAAE,CAAC;EACnI;EAEAK,sBAAsBA,CAACN,KAAa;IAClC,OAAO,IAAI,CAACb,UAAU,CAACU,GAAG,CAAM,GAAG,IAAI,CAACT,OAAO,sBAAsBY,KAAK,EAAE,CAAC;EAC/E;EAEAO,mBAAmBA,CAAA;IACjB,MAAMC,IAAI,GAAG,IAAI,CAACC,WAAW,EAAE;IAC/B,IAAID,IAAI,KAAK,YAAY,EAAE;MACzB,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;KAChD,MAAM,IAAIH,IAAI,KAAK,kBAAkB,EAAE;MACtC,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,CAAC,iBAAiB,CAAC,CAAC;KAC1C,MAAM,IAAIH,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,CAAC,uBAAuB,CAAC,CAAC;KAChD,MAAM,IAAIH,IAAI,KAAK,OAAO,EAAE;MAC3B,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,CAAC,kBAAkB,CAAC,CAAC;KAC3C,MAAM,IAAIH,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAI,CAACE,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;KACrC,MAAM;MACL,IAAI,CAACD,MAAM,CAACC,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEEF,WAAWA,CAAA;IACX,MAAMlB,IAAI,GAAG,IAAI,CAACqB,cAAc,EAAE;IAClC,OAAOrB,IAAI,GAAGA,IAAI,CAACiB,IAAI,GAAG,IAAI;EAChC;;;uBAnDWvB,kBAAkB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAlB/B,kBAAkB;MAAAgC,OAAA,EAAlBhC,kBAAkB,CAAAiC,IAAA;MAAAC,UAAA,EAFjB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
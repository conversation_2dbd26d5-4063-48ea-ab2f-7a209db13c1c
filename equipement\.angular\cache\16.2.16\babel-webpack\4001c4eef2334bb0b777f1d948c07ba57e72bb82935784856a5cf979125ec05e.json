{"ast": null, "code": "import { Equip } from 'src/app/equipement/equip';\nimport { FormControl, Validators } from '@angular/forms';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Panne } from './Panne';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/dashboard/type.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"src/app/utilisateur/utilisateur.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/autocomplete\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"../../Shared/layout/layout.component\";\nfunction EquipementsComponent_mat_option_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 43);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", user_r5);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate3(\" \", user_r5.firstName, \" \", user_r5.lastName, \" - \", user_r5.email, \" \");\n  }\n}\nfunction EquipementsComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.notification.type);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.notification.message, \" \");\n  }\n}\nfunction EquipementsComponent_tr_74_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"uppercase\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const equip_r6 = i0.ɵɵnextContext().$implicit;\n    const ctx_r7 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" Affect\\u00E9 \\u00E0 \", i0.ɵɵpipeBind1(2, 1, ctx_r7.NameUtilisateur[equip_r6.idEqui]), \" \");\n  }\n}\nfunction EquipementsComponent_tr_74_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵelement(2, \"img\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"td\", 45)(4, \"div\", 47)(5, \"h6\", 48);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 49);\n    i0.ɵɵtext(8, \"Mod\\u00E8le\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"td\", 45)(10, \"span\", 50);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"td\", 45)(13, \"span\", 50);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"td\", 45)(17, \"span\", 51);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, EquipementsComponent_tr_74_div_19_Template, 3, 3, \"div\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\", 45)(21, \"span\", 53);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"td\", 45)(24, \"span\", 50);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"td\", 54)(27, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_tr_74_Template_button_click_27_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r10);\n      const equip_r6 = restoredCtx.$implicit;\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.openPanneModal(equip_r6));\n    });\n    i0.ɵɵelement(28, \"i\", 56);\n    i0.ɵɵtext(29, \" Panne \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const equip_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", equip_r6.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(equip_r6.model == null ? null : equip_r6.model.nomModel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(equip_r6.numSerie);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 13, equip_r6.dateAffectation, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", equip_r6.statut === \"DISPONIBLE\" ? \"#28a745\" : equip_r6.statut === \"AFFECTE\" ? \"#dc3545\" : \"#6c757d\")(\"color\", \"white\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r6.statut, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", equip_r6.statut.toLowerCase() === \"affecte\" || equip_r6.statut.toLowerCase() === \"affect\\u00E9\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"title\", equip_r6.description);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", equip_r6.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((equip_r6.fournisseur == null ? null : equip_r6.fournisseur.nomFournisseur) || \"Aucun fournisseur\");\n  }\n}\nfunction EquipementsComponent_nav_75_li_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 60)(1, \"a\", 61);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_li_5_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r15);\n      const i_r13 = restoredCtx.index;\n      const ctx_r14 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r14.loadEquipements(i_r13));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r13 = ctx.index;\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"active\", i_r13 === ctx_r11.currentPage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r13 + 1);\n  }\n}\nconst _c0 = function () {\n  return [];\n};\nfunction EquipementsComponent_nav_75_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nav\", 58)(1, \"ul\", 59)(2, \"li\", 60)(3, \"a\", 61);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_Template_a_click_3_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.loadEquipements(ctx_r16.currentPage - 1));\n    });\n    i0.ɵɵtext(4, \"Pr\\u00E9c\\u00E9dent\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, EquipementsComponent_nav_75_li_5_Template, 3, 3, \"li\", 62);\n    i0.ɵɵelementStart(6, \"li\", 60)(7, \"a\", 61);\n    i0.ɵɵlistener(\"click\", function EquipementsComponent_nav_75_Template_a_click_7_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r18 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r18.loadEquipements(ctx_r18.currentPage + 1));\n    });\n    i0.ɵɵtext(8, \"Suivant\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(5, _c0).constructor(ctx_r4.totalPages));\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"disabled\", ctx_r4.currentPage === ctx_r4.totalPages - 1);\n  }\n}\nexport class EquipementsComponent {\n  constructor(authservice, http, fb, utilisateurService) {\n    this.authservice = authservice;\n    this.http = http;\n    this.fb = fb;\n    this.utilisateurService = utilisateurService;\n    this.models = [];\n    this.equiements = [];\n    this.utilisateurs = [];\n    this.filteredUtilisateurs = [];\n    this.filteredUtilisateursSearch = [];\n    this.modelet = [];\n    this.NomEqui = null;\n    this.NomUser = null;\n    this.notification = {\n      show: false,\n      type: 'success',\n      message: ''\n    };\n    this.currentPage = 0;\n    this.pageSize = 4;\n    this.searchTerm = '';\n    this.affectationFormSubmitted = false;\n    this.editAffectationFormSubmitted = false;\n    this.newEquipement = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.newEquipement1 = {\n      idEqui: 0,\n      numSerie: \"\",\n      statut: \"\",\n      image: \"\",\n      model: null,\n      dateAffectation: new Date(),\n      description: \"\",\n      fournisseur: null\n    };\n    this.EditedAffectation = {\n      id: 0,\n      commentaire: \"\",\n      dateAffectation: new Date(),\n      user: new Utilisateur(),\n      equipement: new Equip(),\n      verrou: \"\"\n    };\n    this.NameUtilisateur = [];\n    this.idsEqui = [];\n    this.tableAffectation = {};\n    // Propriétés pour les pannes\n    this.pannes = [];\n    this.selectedPanne = new Panne();\n    this.showPanneModal = false;\n    this.submitted = false;\n    this.fournisseurs = [];\n    this.utilisateurCtrl = new FormControl();\n    this.utilisateurSearchCtrl = new FormControl();\n    this.modelCtrl = new FormControl();\n    this.selectedStatut = ''; // ou initialisée via formulaire dropdown\n    this.signupErrors = {};\n    // Initialisation du formulaire de panne\n    this.panneForm = this.fb.group({\n      titre: ['', [Validators.required, Validators.minLength(3)]],\n      description: ['', [Validators.required, Validators.minLength(10)]],\n      priorite: ['MOYENNE', [Validators.required]]\n    });\n  }\n  ngOnInit() {\n    this.currentPage = 0;\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n    // Autocomplete pour le modal de modification\n    this.modelCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(models => {\n      this.modelet = models;\n    });\n    // Autocomplete pour le formulaire d'ajout\n    // Autocomplete pour la recherche\n    this.utilisateurSearchCtrl.valueChanges.pipe(debounceTime(300), distinctUntilChanged(), tap(value => {\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, {\n          emitEvent: false\n        });\n        this.loadEquipements(0);\n      }\n    }), switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          this.authservice.searchEquipements(this.searchTerm, this.utilisateurSearchCtrl.value, 0, this.pageSize).subscribe({\n            next: res => {\n              this.equiements = res.content;\n              this.totalPages = res.totalPages;\n              this.fetchUtilisateurs(this.equiements);\n            },\n            error: err => console.error(err)\n          });\n          this.loadEquipements(0);\n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })).subscribe(users => {\n      this.filteredUtilisateursSearch = users;\n    });\n  }\n  displayUtilisateur(user) {\n    return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n  }\n  displayModel(model) {\n    return model ? `${model.nomModel}` : '';\n  }\n  getFournisseur() {\n    this.authservice.getallFournisseur().subscribe(data => {\n      this.fournisseurs = data;\n    });\n  }\n  onUserSearchSelected(user) {\n    this.loadEquipements(0);\n  }\n  loadEquipements(page) {\n    this.currentPage = page;\n    const keyword = this.searchTerm.trim();\n    const statut = this.selectedStatut.trim();\n    let username = '';\n    const userVal = this.utilisateurSearchCtrl.value;\n    if (typeof userVal === 'string') {\n      username = userVal.trim();\n    } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n      username = userVal.username.trim();\n    }\n    // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n    if (username !== '') {\n      this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 2 : Statut seul (sans username)\n    if (keyword === '' && statut !== '') {\n      this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 3 : Keyword seul (sans username ni statut)\n    if (keyword !== '' && statut === '') {\n      this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 4 : Keyword + Statut (sans username)\n    if (keyword !== '' && statut !== '') {\n      this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n        next: res => {\n          this.equiements = res.content;\n          this.totalPages = res.totalPages;\n          this.fetchUtilisateurs(this.equiements);\n        },\n        error: err => console.error(err)\n      });\n      return;\n    }\n    // Cas 5 : Aucun filtre → tout afficher\n    this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n      next: res => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: err => console.error(err)\n    });\n  }\n  fetchUtilisateurs(equiements) {\n    console.log(equiements);\n    // Construire correctement le tableau d'IDs\n    this.idsEqui = equiements.map(eq => eq.idEqui);\n    console.log('IDs des équipements:', this.idsEqui);\n    this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      });\n    });\n  }\n  onSearchChange() {\n    this.loadEquipements(0);\n  }\n  onFileSelected(event) {\n    const file = event.target.files[0];\n    if (file) {\n      const formData = new FormData();\n      formData.append('file', file);\n      this.http.post('http://localhost:8085/images', formData).subscribe(response => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      }, error => {\n        console.error('Error during image upload', error);\n      });\n    }\n  }\n  resetErrors() {\n    this.signupErrors = {};\n  }\n  GetAllModels() {\n    this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n    });\n  }\n  // Méthodes pour les notifications\n  showNotification(type, message) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n  hideNotification() {\n    this.notification.show = false;\n  }\n  // Méthode pour réinitialiser le formulaire=\n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date) {\n    if (!date) return null;\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n  // Méthodes pour la pagination\n  goToPage(page) {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n  nextPage() {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n  // Méthode pour générer les numéros de pages\n  getPageNumbers() {\n    const pages = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n    return pages;\n  }\n  // ==================== GESTION DES PANNES ====================\n  /**\n   * Ouvrir le modal pour déclarer une panne\n   */\n  openPanneModal(equipement) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n  /**\n   * Soumettre une déclaration de panne\n   */\n  onSubmitPanne() {\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement\n      };\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: response => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          // Optionnel: recharger la liste des équipements ou afficher un message de succès\n          this.loadEquipements(this.currentPage);\n        },\n        error: error => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n          // Afficher un message d'erreur à l'utilisateur\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.panneForm.controls).forEach(key => {\n        this.panneForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n  /**\n   * Vérifier si un champ du formulaire a une erreur\n   */\n  hasError(fieldName, errorType) {\n    const field = this.panneForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && field.touched);\n  }\n  /**\n   * Obtenir le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName) {\n    const field = this.panneForm.get(fieldName);\n    if (field && field.touched && field.errors) {\n      if (field.errors['required']) {\n        return `Le ${fieldName} est requis`;\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `Le ${fieldName} doit contenir au moins ${requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function EquipementsComponent_Factory(t) {\n      return new (t || EquipementsComponent)(i0.ɵɵdirectiveInject(i1.TypeService), i0.ɵɵdirectiveInject(i2.HttpClient), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.UtilisateurService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EquipementsComponent,\n      selectors: [[\"app-equipements\"]],\n      decls: 85,\n      vars: 8,\n      consts: [[\"lang\", \"en\"], [\"charset\", \"utf-8\"], [\"name\", \"viewport\", \"content\", \"width=device-width, initial-scale=1\"], [\"id\", \"main-wrapper\", \"data-layout\", \"vertical\", \"data-navbarbg\", \"skin6\", \"data-sidebartype\", \"full\", \"data-sidebar-position\", \"fixed\", \"data-header-position\", \"fixed\", 1, \"page-wrapper\"], [1, \"body-wrapper\"], [1, \"app-header\"], [1, \"body-wrapper-inner\"], [1, \"container-fluid\"], [1, \"welcome-header\"], [1, \"header-container\"], [1, \"header-text\"], [1, \"add-user-btn\"], [1, \"icon\"], [1, \"card\", \"mt-3\", \"mb-4\"], [1, \"card-body\"], [1, \"card-title\", \"mb-3\"], [1, \"row\", \"g-3\"], [1, \"col-md-6\"], [\"appearance\", \"outline\", 2, \"width\", \"100%\"], [\"type\", \"text\", \"matInput\", \"\", \"placeholder\", \"Rechercher un utilisateur...\", 3, \"formControl\", \"matAutocomplete\"], [3, \"displayWith\", \"optionSelected\"], [\"autoUserSearch\", \"matAutocomplete\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"search-wrapper\"], [1, \"custom-search\"], [\"type\", \"text\", \"placeholder\", \"Rechercher un equipement...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [1, \"icon-search\"], [1, \"bg-light\"], [1, \"container\", \"my-2\"], [\"class\", \"simple-notification\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-12\"], [1, \"card\"], [1, \"table-responsive\", \"mt-1\"], [1, \"table\", \"mb-0\", \"text-nowrap\", \"varient-table\", \"align-middle\", \"fs-3\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\"], [\"scope\", \"col\", 1, \"px-0\", \"text-muted\", \"text-end\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"mt-4 d-flex justify-content-center\", 4, \"ngIf\"], [1, \"py-6\", \"px-6\", \"text-center\"], [1, \"mb-0\", \"fs-4\"], [\"href\", \"#\", 1, \"pe-1\", \"text-primary\", \"text-decoration-underline\"], [\"href\", \"https://themewagon.com\", \"target\", \"_blank\"], [3, \"value\"], [1, \"simple-notification\", 3, \"ngClass\"], [1, \"px-1\"], [\"alt\", \"\\u00C9quipement\", \"width\", \"40\", \"height\", \"40\", 1, \"rounded-circle\", \"img-fluid\", 3, \"src\"], [1, \"ms-3\"], [1, \"fw-semibold\", \"mb-0\", \"fs-4\"], [1, \"fw-normal\", \"text-muted\"], [1, \"fw-normal\"], [1, \"badge\", \"rounded-pill\"], [\"class\", \"text-muted small mt-1\", 4, \"ngIf\"], [1, \"fw-normal\", \"text-truncate\", 2, \"max-width\", \"150px\", \"display\", \"inline-block\", 3, \"title\"], [1, \"px-1\", \"text-end\"], [\"title\", \"D\\u00E9clarer une panne\", 1, \"btn\", \"btn-danger\", \"btn-sm\", 3, \"click\"], [1, \"ti\", \"ti-alert-triangle\"], [1, \"text-muted\", \"small\", \"mt-1\"], [1, \"mt-4\", \"d-flex\", \"justify-content-center\"], [1, \"pagination\"], [1, \"page-item\"], [1, \"page-link\", 3, \"click\"], [\"class\", \"page-item\", 3, \"active\", 4, \"ngFor\", \"ngForOf\"]],\n      template: function EquipementsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"html\", 0)(1, \"head\");\n          i0.ɵɵelement(2, \"meta\", 1)(3, \"meta\", 2);\n          i0.ɵɵelementStart(4, \"title\");\n          i0.ɵɵtext(5, \"Flexy Free Bootstrap Admin Template by WrapPixel\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(6, \"body\")(7, \"div\", 3);\n          i0.ɵɵelement(8, \"app-layout\");\n          i0.ɵɵelementStart(9, \"div\", 4);\n          i0.ɵɵelement(10, \"header\", 5);\n          i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"h1\");\n          i0.ɵɵtext(15, \"Bienvenue dans votre espace\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\");\n          i0.ɵɵtext(17, \"G\\u00E9rez efficacement vos activit\\u00E9s acad\\u00E9miques depuis ce tableau de bord\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"div\", 10)(20, \"h2\");\n          i0.ɵɵtext(21, \"\\u00C9quipements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"G\\u00E9rez les diff\\u00E9rents types d'\\u00E9quipements informatiques \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"button\", 11)(25, \"span\", 12);\n          i0.ɵɵtext(26, \"+\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(27, \"Nouvel Panne \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 13)(29, \"div\", 14)(30, \"h5\", 15);\n          i0.ɵɵtext(31, \"Recherche par utilisateur et statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 16)(33, \"div\", 17)(34, \"mat-form-field\", 18)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Utilisateur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 19);\n          i0.ɵɵelementStart(38, \"mat-autocomplete\", 20, 21);\n          i0.ɵɵlistener(\"optionSelected\", function EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener($event) {\n            return ctx.onUserSearchSelected($event.option.value);\n          });\n          i0.ɵɵtemplate(40, EquipementsComponent_mat_option_40_Template, 2, 4, \"mat-option\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 24)(43, \"input\", 25);\n          i0.ɵɵlistener(\"ngModelChange\", function EquipementsComponent_Template_input_ngModelChange_43_listener($event) {\n            return ctx.searchTerm = $event;\n          })(\"input\", function EquipementsComponent_Template_input_input_43_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"span\", 26);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"body\", 27)(46, \"div\", 28);\n          i0.ɵɵtemplate(47, EquipementsComponent_div_47_Template, 2, 2, \"div\", 29);\n          i0.ɵɵelementStart(48, \"div\", 7)(49, \"div\", 30)(50, \"div\", 31)(51, \"div\", 32)(52, \"div\", 14)(53, \"div\", 33)(54, \"table\", 34)(55, \"thead\")(56, \"tr\")(57, \"th\", 35);\n          i0.ɵɵtext(58, \"Image\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"th\", 35);\n          i0.ɵɵtext(60, \"Mod\\u00E8le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"th\", 35);\n          i0.ɵɵtext(62, \"N\\u00B0 S\\u00E9rie\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"th\", 35);\n          i0.ɵɵtext(64, \"Date d'acquisition\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"th\", 35);\n          i0.ɵɵtext(66, \"Statut\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"th\", 35);\n          i0.ɵɵtext(68, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"th\", 35);\n          i0.ɵɵtext(70, \"Fournisseur\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"th\", 36);\n          i0.ɵɵtext(72, \"Actions\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(73, \"tbody\");\n          i0.ɵɵtemplate(74, EquipementsComponent_tr_74_Template, 30, 16, \"tr\", 37);\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵtemplate(75, EquipementsComponent_nav_75_Template, 9, 6, \"nav\", 38);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(76, \"div\", 30)(77, \"div\", 39)(78, \"p\", 40);\n          i0.ɵɵtext(79, \"Design and Developed by \");\n          i0.ɵɵelementStart(80, \"a\", 41);\n          i0.ɵɵtext(81, \"Wrappixel.com\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(82, \" Distributed by \");\n          i0.ɵɵelementStart(83, \"a\", 42);\n          i0.ɵɵtext(84, \"ThemeWagon\");\n          i0.ɵɵelementEnd()()()()()()()()();\n        }\n        if (rf & 2) {\n          const _r0 = i0.ɵɵreference(39);\n          i0.ɵɵadvance(37);\n          i0.ɵɵproperty(\"formControl\", ctx.utilisateurSearchCtrl)(\"matAutocomplete\", _r0);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"displayWith\", ctx.displayUtilisateur);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredUtilisateursSearch);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.notification.show);\n          i0.ɵɵadvance(27);\n          i0.ɵɵproperty(\"ngForOf\", ctx.equiements);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.totalPages > 1);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i3.FormControlDirective, i6.MatFormField, i6.MatLabel, i7.MatInput, i8.MatAutocomplete, i9.MatOption, i8.MatAutocompleteTrigger, i10.LayoutComponent, i5.UpperCasePipe, i5.DatePipe],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\", \".card-custom[_ngcontent-%COMP%] {\\n      border-radius: 12px;\\n      padding: 20px;\\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n    }\\n\\n    .btn-outline-lightblue[_ngcontent-%COMP%] {\\n      border: 1px solid #cfe2ff;\\n      color: #0d6efd;\\n      background-color: #e7f1ff;\\n    }\\n\\n    .tag[_ngcontent-%COMP%] {\\n      background-color: #e7f1ff;\\n      color: #0d6efd;\\n      padding: 3px 10px;\\n      font-size: 0.8rem;\\n      border-radius: 15px;\\n      position: absolute;\\n      right: 20px;\\n      top: 20px;\\n    }\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 48px; \\n\\n  width: 100px;     \\n\\n  height: 100px;    \\n\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n border-radius: 0% !important;\\n}\\n\\n    .btn-icon[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      justify-content: center;\\n    }\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\\n  background-color: #fff;\\n  color: #212529; \\n\\n  font-size: 0.95rem; \\n\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  color: #1a1a1a;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #000;\\n}\\n\\n.card-custom[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], .text-muted[_ngcontent-%COMP%] {\\n  color: #495057 !important; \\n\\n}\\n\\n.icon-box[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #0d6efd;\\n  margin-right: 10px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  background-color: #e7f1ff;\\n  color: #0d6efd;\\n  padding: 3px 10px;\\n  font-size: 0.8rem;\\n  border-radius: 15px;\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Equip", "FormControl", "Validators", "Utilisa<PERSON>ur", "debounceTime", "distinctUntilChanged", "of", "switchMap", "tap", "<PERSON><PERSON>", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "user_r5", "ɵɵadvance", "ɵɵtextInterpolate3", "firstName", "lastName", "email", "ctx_r2", "notification", "type", "ɵɵtextInterpolate1", "message", "ɵɵpipeBind1", "ctx_r7", "NameUtilisateur", "equip_r6", "idEqui", "ɵɵelement", "ɵɵtemplate", "EquipementsComponent_tr_74_div_19_Template", "ɵɵlistener", "EquipementsComponent_tr_74_Template_button_click_27_listener", "restoredCtx", "ɵɵrestoreView", "_r10", "$implicit", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "openPanneModal", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "model", "nomModel", "numSerie", "ɵɵpipeBind2", "dateAffectation", "ɵɵstyleProp", "statut", "toLowerCase", "description", "<PERSON><PERSON><PERSON><PERSON>", "nomFournisseur", "EquipementsComponent_nav_75_li_5_Template_a_click_1_listener", "_r15", "i_r13", "index", "ctx_r14", "loadEquipements", "ɵɵclassProp", "ctx_r11", "currentPage", "EquipementsComponent_nav_75_Template_a_click_3_listener", "_r17", "ctx_r16", "EquipementsComponent_nav_75_li_5_Template", "EquipementsComponent_nav_75_Template_a_click_7_listener", "ctx_r18", "ctx_r4", "ɵɵpureFunction0", "_c0", "constructor", "totalPages", "EquipementsComponent", "authservice", "http", "fb", "utilisateurService", "models", "equiements", "utilisateurs", "filteredUtilisateurs", "filteredUtilisateursSearch", "modelet", "NomEqui", "NomUser", "show", "pageSize", "searchTerm", "affectationFormSubmitted", "editAffectationFormSubmitted", "newEquipement", "Date", "newEquipement1", "EditedAffectation", "id", "commentaire", "user", "equipement", "verrou", "idsEqui", "tableAffectation", "pannes", "<PERSON><PERSON><PERSON>", "showPanneModal", "submitted", "fournisseurs", "utilisateurCtrl", "utilisateurSearchCtrl", "modelCtrl", "selectedStatut", "signupErrors", "panneForm", "group", "titre", "required", "<PERSON><PERSON><PERSON><PERSON>", "priorite", "ngOnInit", "GetAllModels", "getFournisseur", "valueChanges", "pipe", "value", "trim", "length", "searchModels", "subscribe", "setValue", "emitEvent", "searchEquipements", "next", "res", "content", "fetchUtilisateurs", "error", "err", "console", "searchUsers", "users", "displayUtilisateur", "displayModel", "getallFournisseur", "data", "onUserSearchSelected", "page", "keyword", "username", "userVal", "searchEquipements1", "getDSIEquipements", "log", "map", "eq", "getAffectationsByIds", "for<PERSON>ach", "affectation", "onSearchChange", "onFileSelected", "event", "file", "target", "files", "formData", "FormData", "append", "post", "response", "imageUrl", "fullUrl", "form", "patchValue", "resetErrors", "getAllModel", "showNotification", "setTimeout", "hideNotification", "formatDateForInput", "date", "date<PERSON><PERSON>j", "isNaN", "getTime", "toISOString", "split", "goToPage", "nextPage", "previousPage", "getPageNumbers", "pages", "maxPagesToShow", "startPage", "Math", "max", "floor", "endPage", "min", "i", "push", "reset", "closePanneModal", "onSubmitPanne", "valid", "panneData", "get", "declarer<PERSON><PERSON>", "Object", "keys", "controls", "key", "<PERSON><PERSON><PERSON><PERSON>ched", "<PERSON><PERSON><PERSON><PERSON>", "fieldName", "errorType", "field", "touched", "getErrorMessage", "errors", "<PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "TypeService", "i2", "HttpClient", "i3", "FormBuilder", "i4", "UtilisateurService", "selectors", "decls", "vars", "consts", "template", "EquipementsComponent_Template", "rf", "ctx", "EquipementsComponent_Template_mat_autocomplete_optionSelected_38_listener", "$event", "option", "EquipementsComponent_mat_option_40_Template", "EquipementsComponent_Template_input_ngModelChange_43_listener", "EquipementsComponent_Template_input_input_43_listener", "EquipementsComponent_div_47_Template", "EquipementsComponent_tr_74_Template", "EquipementsComponent_nav_75_Template", "_r0"], "sources": ["C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.ts", "C:\\Users\\<USER>\\Desktop\\pe\\equipement\\src\\app\\DSI\\equipements\\equipements.component.html"], "sourcesContent": ["\nimport { Component, OnInit } from '@angular/core';\nimport { Equip } from 'src/app/equipement/equip';\nimport { Model } from 'src/app/model/Model';\nimport { TypeService } from 'src/app/dashboard/type.service'; \nimport { HttpClient } from '@angular/common/http';\nimport { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';\nimport * as bootstrap from 'bootstrap';\n// or for just Modal:\nimport { Modal } from 'bootstrap';\nimport { Fournisseur } from 'src/app/fournisseur/Fournisseur';\nimport { Utilisateur } from 'src/app/utilisateur/utilisateur';\nimport { UtilisateurService } from 'src/app/utilisateur/utilisateur.service';\n\nimport { debounceTime, distinctUntilChanged, of, switchMap, tap } from 'rxjs';\nimport { Affectation } from 'src/app/affecta/Affectation';\nimport { Historique } from 'src/app/equipement/Historique';\nimport { Panne } from './Panne';\n@Component({\n  selector: 'app-equipements',\n  templateUrl: './equipements.component.html',\n  styleUrls: ['./equipements.component.css']\n})\n\n\n\nexport class EquipementsComponent implements OnInit {\n\n\n\nmodels:Model[]=[];\nequiements:Equip[]=[];\nutilisateurs: Utilisateur[] = [];\n\nfilteredUtilisateurs: Utilisateur[] = [];\nfilteredUtilisateursSearch: Utilisateur[] = [];\nmodelet: Model[] = [];\n  NomEqui:String|null=null;\n    NomUser:String|null=null;\nnotification = {\n  show: false,\n  type: 'success', // 'success' or 'error'\n  message: ''\n};\ncurrentPage = 0;\npageSize = 4;\nsearchTerm: string = '';\n\n// Affectation form\naffectationForm!: FormGroup;\nselectedEquipement!: Equip \naffectationFormSubmitted = false;\neditAffectationFormSubmitted = false;\n\nnewEquipement:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nnewEquipement1:Equip={\nidEqui:0,\nnumSerie:\"\",\nstatut:\"\",\nimage:\"\",\nmodel:null,\ndateAffectation:new Date,\ndescription:\"\",\nfournisseur:null,\n\n};\nform!: FormGroup;\neditForm!: FormGroup;\n\nEditedAffectation:Affectation={\n  id:0,\n  commentaire:\"\",\n  dateAffectation:new Date(),\n  user:new Utilisateur(),\n  equipement:new Equip(),\n  verrou:\"\"\n\n}\nNameUtilisateur:string[]=[];\nidsEqui:number[]=[];\ntableAffectation: any = {};\n\n// Propriétés pour les pannes\npannes: Panne[] = [];\nselectedPanne: Panne = new Panne();\nshowPanneModal: boolean = false;\npanneForm: FormGroup;\n\nsubmitted = false;\nfournisseurs:Fournisseur[]=[];\n  totalPages: any;\n  utilisateurCtrl = new FormControl();\n  utilisateurSearchCtrl = new FormControl();\n  modelCtrl = new FormControl();\nconstructor(\n  private authservice:TypeService,\n  private http:HttpClient,\n  private fb: FormBuilder,\n  private utilisateurService: UtilisateurService\n) {\n  // Initialisation du formulaire de panne\n  this.panneForm = this.fb.group({\n    titre: ['', [Validators.required, Validators.minLength(3)]],\n    description: ['', [Validators.required, Validators.minLength(10)]],\n    priorite: ['MOYENNE', [Validators.required]]\n  });\n}\n  ngOnInit(): void {\n      this.currentPage = 0;\n\n    this.GetAllModels();\n    this.loadEquipements(this.currentPage);\n    this.getFournisseur();\n \n\n\n\n\n\n\n\n\n// Autocomplete pour le modal de modification\nthis.modelCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n          return this.authservice.searchModels(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(models => {\n    this.modelet = models;\n  });\n\n// Autocomplete pour le formulaire d'ajout\n\n\n\n// Autocomplete pour la recherche\nthis.utilisateurSearchCtrl.valueChanges\n  .pipe(\n    debounceTime(300),\n    distinctUntilChanged(),\n    tap((value: any) => {\n\n      if (typeof value === 'string' && value.trim() === '') {\n        this.utilisateurSearchCtrl.setValue(null, { emitEvent: false });\n        this.loadEquipements(0);\n      }\n    }),\n    switchMap(value => {\n      if (typeof value === 'string') {\n        if (value.trim().length > 0) {\n\nthis.authservice.searchEquipements(this.searchTerm,this.utilisateurSearchCtrl.value,0,this.pageSize).subscribe({\n  next: (res) => {\n    this.equiements = res.content;\n    this.totalPages = res.totalPages;\n    this.fetchUtilisateurs(this.equiements);\n  },\n  error: (err) => console.error(err)\n});\nthis.loadEquipements(0);\n          \n          return this.authservice.searchUsers(value.trim());\n        } else {\n          return of([]);\n        }\n      }\n      return of([]);\n    })\n  )\n  .subscribe(users => {\n    this.filteredUtilisateursSearch = users;\n  });\n\n\n\n}\n\n\n\n\n  \n\ndisplayUtilisateur(user: any): string {\n  return user ? `${user.firstName} ${user.lastName} - ${user.email}` : '';\n}\n\n\ndisplayModel(model: any): string {\n  return  model? `${model.nomModel}` : '';\n}\n\n\n\n\n\n getFournisseur()\n  {\n\n  this.authservice.getallFournisseur().subscribe(data => {\n  this.fournisseurs = data;\n\n});\n\n\n  }\n\n\n\n\n\n\n\n\n\n \n\n  onUserSearchSelected(user: Utilisateur) {\n    this.loadEquipements(0);\n \n  }\n\n\n\n\n\nselectedStatut: string = ''; // ou initialisée via formulaire dropdown\n\nloadEquipements(page: number): void {\n  this.currentPage = page;\n\n  const keyword = this.searchTerm.trim();\n  const statut = this.selectedStatut.trim();\n\n  let username = '';\n  const userVal = this.utilisateurSearchCtrl.value;\n\n  if (typeof userVal === 'string') {\n    username = userVal.trim();\n  } else if (userVal && typeof userVal === 'object' && 'username' in userVal) {\n    username = userVal.username.trim();\n  }\n\n  // 🔥 PRIORITÉ 1 : Username présent → toujours utiliser le filtre par username\n  if (username !== '') {\n    this.authservice.searchEquipements(keyword || '', username, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 2 : Statut seul (sans username)\n  if (keyword === '' && statut !== '') {\n    this.authservice.searchEquipements1('', statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 3 : Keyword seul (sans username ni statut)\n  if (keyword !== '' && statut === '') {\n    this.authservice.searchEquipements(keyword, '', page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 4 : Keyword + Statut (sans username)\n  if (keyword !== '' && statut !== '') {\n    this.authservice.searchEquipements1(keyword, statut, page, this.pageSize).subscribe({\n      next: (res) => {\n        this.equiements = res.content;\n        this.totalPages = res.totalPages;\n        this.fetchUtilisateurs(this.equiements);\n      },\n      error: (err) => console.error(err)\n    });\n    return;\n  }\n\n  // Cas 5 : Aucun filtre → tout afficher\n  this.authservice.getDSIEquipements(page, this.pageSize).subscribe({\n    next: (res) => {\n      this.equiements = res.content;\n      this.totalPages = res.totalPages;\n      this.fetchUtilisateurs(this.equiements);\n    },\n    error: (err) => console.error(err)\n  });\n}\n\n\nprivate fetchUtilisateurs(equiements: any[]): void {\n  console.log(equiements);\n\n  // Construire correctement le tableau d'IDs\n  this.idsEqui = equiements.map(eq => eq.idEqui);\n  console.log('IDs des équipements:', this.idsEqui);\n\n  this.authservice.getAffectationsByIds(this.idsEqui).subscribe(data => {\n\n      data.forEach(affectation => {\n        this.tableAffectation[affectation.equipement.idEqui] = affectation;\n        this.NameUtilisateur[affectation.equipement.idEqui] = affectation.user.firstName + \" \" + affectation.user.lastName;\n      \n      });\n    });\n\n\n\n\n}\n\n\n  onSearchChange(): void {\n\n    this.loadEquipements(0);\n  }\n\n  \n  \n\n\n\n\n\n   \n\n\n\n\n\n\n\n    \n\n\n\n\n  \nonFileSelected(event: any) {\n  const file = event.target.files[0];\n\n  if (file) {\n    const formData = new FormData();\n    formData.append('file', file);\n\n    this.http.post<any>('http://localhost:8085/images', formData).subscribe(\n      (response) => {\n        if (response && response.imageUrl) {\n          const fullUrl = `http://localhost:8085${response.imageUrl}`;\n          console.log('Image URL saved: ', fullUrl);\n\n         \n          this.form.patchValue({\n            image: fullUrl\n          });\n        } else {\n          console.error('Invalid response from API');\n        }\n      },\n      (error) => {\n        console.error('Error during image upload', error);\n      }\n    );\n  }\n}\n\n\n\nsignupErrors: any = {};\n    \n  resetErrors() {\n    this.signupErrors = {};\n  }\n\n      GetAllModels()\n    {\n      this.authservice.getAllModel().subscribe(data => {\n      this.models = data;\n   \n    });\n    }\n\n\n\n\n \n  // Méthodes pour les notifications\n  showNotification(type: 'success' | 'error', message: string) {\n    this.notification = {\n      show: true,\n      type: type,\n      message: message\n    };\n\n    // Auto-hide après 2 secondes\n    setTimeout(() => {\n      this.hideNotification();\n    }, 2000);\n  }\n\n  hideNotification() {\n    this.notification.show = false;\n  }\n\n  // Méthode pour réinitialiser le formulaire=\n\n\n\n\n\n\n\n\n  \n  // Méthode pour formater la date pour les inputs HTML\n  formatDateForInput(date: any): string | null {\n    if (!date) return null;\n\n    try {\n      const dateObj = new Date(date);\n      if (isNaN(dateObj.getTime())) return null;\n\n      // Format YYYY-MM-DD pour les inputs de type date\n      return dateObj.toISOString().split('T')[0];\n    } catch (error) {\n      console.error('Erreur lors du formatage de la date:', error);\n      return null;\n    }\n  }\n\n  // Méthodes pour la pagination\n  goToPage(page: number): void {\n    if (page >= 0 && page < this.totalPages) {\n      this.loadEquipements(page);\n    }\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.totalPages - 1) {\n      this.loadEquipements(this.currentPage + 1);\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.loadEquipements(this.currentPage - 1);\n    }\n  }\n\n  // Méthode pour générer les numéros de pages\n  getPageNumbers(): number[] {\n    const pages: number[] = [];\n    const maxPagesToShow = 5; // Afficher maximum 5 numéros de pages\n\n    let startPage = Math.max(0, this.currentPage - Math.floor(maxPagesToShow / 2));\n    let endPage = Math.min(this.totalPages - 1, startPage + maxPagesToShow - 1);\n\n    // Ajuster startPage si on est près de la fin\n    if (endPage - startPage < maxPagesToShow - 1) {\n      startPage = Math.max(0, endPage - maxPagesToShow + 1);\n    }\n\n    for (let i = startPage; i <= endPage; i++) {\n      pages.push(i);\n    }\n\n    return pages;\n  }\n\n  // ==================== GESTION DES PANNES ====================\n\n  /**\n   * Ouvrir le modal pour déclarer une panne\n   */\n  openPanneModal(equipement: Equip) {\n    this.selectedPanne = new Panne();\n    this.selectedPanne.equipement = equipement;\n    this.panneForm.reset();\n    this.panneForm.patchValue({\n      priorite: 'MOYENNE'\n    });\n    this.showPanneModal = true;\n  }\n\n\n  closePanneModal() {\n    this.showPanneModal = false;\n    this.selectedPanne = new Panne();\n    this.panneForm.reset();\n  }\n\n  /**\n   * Soumettre une déclaration de panne\n   */\n  onSubmitPanne() {\n    if (this.panneForm.valid) {\n      const panneData = {\n        titre: this.panneForm.get('titre')?.value,\n        description: this.panneForm.get('description')?.value,\n        priorite: this.panneForm.get('priorite')?.value,\n        equipement: this.selectedPanne.equipement\n      };\n\n      this.authservice.declarerPanne(panneData).subscribe({\n        next: (response) => {\n          console.log('Panne déclarée avec succès:', response);\n          this.closePanneModal();\n          // Optionnel: recharger la liste des équipements ou afficher un message de succès\n          this.loadEquipements(this.currentPage);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la déclaration de panne:', error);\n          // Afficher un message d'erreur à l'utilisateur\n        }\n      });\n    } else {\n      // Marquer tous les champs comme touchés pour afficher les erreurs\n      Object.keys(this.panneForm.controls).forEach(key => {\n        this.panneForm.get(key)?.markAsTouched();\n      });\n    }\n  }\n\n  /**\n   * Vérifier si un champ du formulaire a une erreur\n   */\n  hasError(fieldName: string, errorType: string): boolean {\n    const field = this.panneForm.get(fieldName);\n    return !!(field && field.hasError(errorType) && field.touched);\n  }\n\n  /**\n   * Obtenir le message d'erreur pour un champ\n   */\n  getErrorMessage(fieldName: string): string {\n    const field = this.panneForm.get(fieldName);\n    if (field && field.touched && field.errors) {\n      if (field.errors['required']) {\n        return `Le ${fieldName} est requis`;\n      }\n      if (field.errors['minlength']) {\n        const requiredLength = field.errors['minlength'].requiredLength;\n        return `Le ${fieldName} doit contenir au moins ${requiredLength} caractères`;\n      }\n    }\n    return '';\n  }\n\n}\n\n  \n\n\n\n", "<!doctype html>\n<html lang=\"en\">\n\n<head>\n  <meta charset=\"utf-8\">\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1\">\n  <title>Flexy Free Bootstrap Admin Template by WrapPixel</title>\n  \n</head>\n\n<body>\n  <!--  Body Wrapper -->\n  <div class=\"page-wrapper\" id=\"main-wrapper\" data-layout=\"vertical\" data-navbarbg=\"skin6\" data-sidebartype=\"full\"\n    data-sidebar-position=\"fixed\" data-header-position=\"fixed\">\n\n    <!--  App Topstrip -->\n    \n    <!-- Sidebar Start -->\n<app-layout></app-layout>\n\n    <!--  Sidebar End -->\n    <!--  Main wrapper -->\n    <div class=\"body-wrapper\">\n      <!--  Header Start -->\n      <header class=\"app-header\">\n\n      </header>\n      <!--  Header End -->\n      <div class=\"body-wrapper-inner\">\n        <div class=\"container-fluid\">\n                <div class=\"welcome-header\">\n  <h1>Bienvenue dans votre espace</h1>\n  <p>G<PERSON>rez efficacement vos activités académiques depuis ce tableau de bord</p>\n\n</div>\n<!-- Bouton pour ouvrir la modale -->\n\n<div class=\"header-container\">\n  <div class=\"header-text\">\n    <h2>Équipements</h2>\n    <p>Gérez les différents types d'équipements informatiques\n\n</p>\n  </div>\n<button class=\"add-user-btn\" >\n  <span class=\"icon\">+</span>Nouvel Panne \n\n</button>\n</div>\n\n<!-- Formulaire de recherche simple -->\n<div class=\"card mt-3 mb-4\">\n  <div class=\"card-body\">\n    <h5 class=\"card-title mb-3\">Recherche par utilisateur et statut</h5>\n\n    <div class=\"row g-3\">\n      <!-- Recherche par utilisateur -->\n      <div class=\"col-md-6\">\n        <mat-form-field appearance=\"outline\" style=\"width: 100%;\">\n          <mat-label>Utilisateur</mat-label>\n          <input\n            type=\"text\"\n            matInput\n            [formControl]=\"utilisateurSearchCtrl\"\n            [matAutocomplete]=\"autoUserSearch\"\n            placeholder=\"Rechercher un utilisateur...\">\n\n          <mat-autocomplete\n            #autoUserSearch=\"matAutocomplete\"\n            [displayWith]=\"displayUtilisateur\"\n            (optionSelected)=\"onUserSearchSelected($event.option.value)\">\n            <mat-option *ngFor=\"let user of filteredUtilisateursSearch\" [value]=\"user\">\n              {{ user.firstName }} {{ user.lastName }} - {{ user.email }}\n            </mat-option>\n          </mat-autocomplete>\n        </mat-form-field>\n      </div>\n\n      <!-- Recherche par statut -->\n\n</div>\n  </div>\n</div>\n\n<div class=\"search-wrapper\">\n  <div class=\"custom-search\">\n    <input\n      type=\"text\"\n      placeholder=\"Rechercher un equipement...\"\n      [(ngModel)]=\"searchTerm\"\n      (input)=\"onSearchChange()\"\n      class=\"form-control\"\n    />\n    <span class=\"icon-search\"></span>\n  </div>\n</div>\n\n</div>\n<!-- Modal -->\n\n<!-- Modal de modification -->\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n<style>\n    .card-custom {\n      border-radius: 12px;\n      padding: 20px;\n      box-shadow: 0 0 10px rgba(0,0,0,0.1);\n    }\n\n    .btn-outline-lightblue {\n      border: 1px solid #cfe2ff;\n      color: #0d6efd;\n      background-color: #e7f1ff;\n    }\n\n    .tag {\n      background-color: #e7f1ff;\n      color: #0d6efd;\n      padding: 3px 10px;\n      font-size: 0.8rem;\n      border-radius: 15px;\n      position: absolute;\n      right: 20px;\n      top: 20px;\n    }\n\n.icon-box {\n  font-size: 48px; /* optional - for icon size */\n  width: 100px;     /* increase width */\n  height: 100px;    /* set height */\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #0d6efd;\n  margin-right: 10px;\n border-radius: 0% !important;\n}\n\n    .btn-icon {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n    }\n\n.card-custom {\n  border-radius: 12px;\n  box-shadow: 0 0 10px rgba(0,0,0,0.1);\n  background-color: #fff;\n  color: #212529; /* Darker text */\n  font-size: 0.95rem; /* Slightly larger base font */\n}\n\n.card-custom strong {\n  font-weight: 600; /* Heavier for labels */\n  color: #1a1a1a;\n}\n\n.card-custom h5 {\n  font-weight: 600;\n  color: #000;\n}\n\n.card-custom small,\n.text-muted {\n  color: #495057 !important; /* Less faded gray */\n}\n\n.icon-box {\n  font-size: 32px;\n  color: #0d6efd;\n  margin-right: 10px;\n}\n\n.tag {\n  background-color: #e7f1ff;\n  color: #0d6efd;\n  padding: 3px 10px;\n  font-size: 0.8rem;\n  border-radius: 15px;\n  position: absolute;\n  right: 20px;\n  top: 20px;\n}\n\n\n\n  </style>\n\n<body class=\"bg-light\">\n  <div class=\"container my-2\">\n\n    <!-- Simple Notification Bar -->\n    <div *ngIf=\"notification.show\" class=\"simple-notification\" [ngClass]=\"notification.type\">\n      {{ notification.message }}\n    </div>\n\n    <!-- Tableau des équipements -->\n    <div class=\"container-fluid\">\n      <div class=\"row\">\n        <div class=\"col-12\">\n          <div class=\"card\">\n            <div class=\"card-body\">\n              <div class=\"table-responsive mt-1\">\n                <table class=\"table mb-0 text-nowrap varient-table align-middle fs-3\">\n                  <thead>\n                    <tr>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Image</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Modèle</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">N° Série</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Date d'acquisition</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Statut</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Description</th>\n                      <th scope=\"col\" class=\"px-0 text-muted\">Fournisseur</th>\n                      <th scope=\"col\" class=\"px-0 text-muted text-end\">Actions</th>\n                    </tr>\n                  </thead>\n                  <tbody>\n                    <tr *ngFor=\"let equip of equiements\">\n                      <!-- Image -->\n                      <td class=\"px-1\">\n                        <img [src]=\"equip.image\"\n                             alt=\"Équipement\"\n                             class=\"rounded-circle img-fluid\"\n                             width=\"40\" height=\"40\" />\n                      </td>\n\n                      <!-- Modèle -->\n                      <td class=\"px-1\">\n                        <div class=\"ms-3\">\n                          <h6 class=\"fw-semibold mb-0 fs-4\">{{ equip.model?.nomModel }}</h6>\n                          <span class=\"fw-normal text-muted\">Modèle</span>\n                        </div>\n                      </td>\n\n                      <!-- Numéro de série -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.numSerie }}</span>\n                      </td>\n\n                      <!-- Date d'acquisition -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.dateAffectation | date: 'dd/MM/yyyy' }}</span>\n                      </td>\n\n                      <!-- Statut -->\n                      <td class=\"px-1\">\n                        <span class=\"badge rounded-pill\"\n                              [style.background-color]=\"equip.statut === 'DISPONIBLE' ? '#28a745' : (equip.statut === 'AFFECTE' ? '#dc3545' : '#6c757d')\"\n                              [style.color]=\"'white'\">\n                          {{ equip.statut }}\n                        </span>\n                        <div *ngIf=\"equip.statut.toLowerCase() === 'affecte' || equip.statut.toLowerCase() === 'affecté'\"\n                             class=\"text-muted small mt-1\">\n                          Affecté à {{ NameUtilisateur[equip.idEqui] | uppercase }}\n                        </div>\n                      </td>\n\n                      <!-- Description -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal text-truncate\" style=\"max-width: 150px; display: inline-block;\"\n                              [title]=\"equip.description\">\n                          {{ equip.description }}\n                        </span>\n                      </td>\n\n                      <!-- Fournisseur -->\n                      <td class=\"px-1\">\n                        <span class=\"fw-normal\">{{ equip.fournisseur?.nomFournisseur || 'Aucun fournisseur' }}</span>\n                      </td>\n\n                      <!-- Actions -->\n                      <td class=\"px-1 text-end\">\n                        <button class=\"btn btn-danger btn-sm\"\n                                (click)=\"openPanneModal(equip)\"\n                                title=\"Déclarer une panne\">\n                          <i class=\"ti ti-alert-triangle\"></i> Panne\n                        </button>\n                      </td>\n\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n    <!-- Pagination Bootstrap -->\n<nav class=\"mt-4 d-flex justify-content-center\" *ngIf=\"totalPages > 1\">\n  <ul class=\"pagination\">\n    <li class=\"page-item\" [class.disabled]=\"currentPage === 0\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage - 1)\">Précédent</a>\n    </li>\n\n    <li class=\"page-item\"\n        *ngFor=\"let page of [].constructor(totalPages); let i = index\"\n        [class.active]=\"i === currentPage\">\n      <a class=\"page-link\" (click)=\"loadEquipements(i)\">{{ i + 1 }}</a>\n    </li>\n\n    <li class=\"page-item\" [class.disabled]=\"currentPage === totalPages - 1\">\n      <a class=\"page-link\" (click)=\"loadEquipements(currentPage + 1)\">Suivant</a>\n    </li>\n  </ul>\n</nav>\n\n  </div>\n</body>\n\n\n\n          <!--  Row 1 -->\n          <div class=\"row\">\n            \n          <div class=\"py-6 px-6 text-center\">\n            <p class=\"mb-0 fs-4\">Design and Developed by <a href=\"#\"\n                class=\"pe-1 text-primary text-decoration-underline\">Wrappixel.com</a> Distributed by <a href=\"https://themewagon.com\" target=\"_blank\" >ThemeWagon</a></p>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  <script src=\"./assets/libs/jquery/dist/jquery.min.js\"></script>\n  <script src=\"./assets/libs/bootstrap/dist/js/bootstrap.bundle.min.js\"></script>\n  <script src=\"./assets/js/sidebarmenu.js\"></script>\n  <script src=\"./assets/js/app.min.js\"></script>\n  <script src=\"./assets/libs/apexcharts/dist/apexcharts.min.js\"></script>\n  <script src=\"./assets/libs/simplebar/dist/simplebar.js\"></script>\n  <script src=\"./assets/js/dashboard.js\"></script>\n  <!-- solar icons -->\n  <script src=\"https://cdn.jsdelivr.net/npm/iconify-icon@1.0.8/dist/iconify-icon.min.js\"></script>\n</body>\n\n</html>"], "mappings": "AAEA,SAASA,KAAK,QAAQ,0BAA0B;AAIhD,SAAsBC,WAAW,EAAaC,UAAU,QAAQ,gBAAgB;AAKhF,SAASC,WAAW,QAAQ,iCAAiC;AAG7D,SAASC,YAAY,EAAEC,oBAAoB,EAAEC,EAAE,EAAEC,SAAS,EAAEC,GAAG,QAAQ,MAAM;AAG7E,SAASC,KAAK,QAAQ,SAAS;;;;;;;;;;;;;;ICsDnBC,EAAA,CAAAC,cAAA,qBAA2E;IACzED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF+CH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACxEL,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAO,kBAAA,MAAAF,OAAA,CAAAG,SAAA,OAAAH,OAAA,CAAAI,QAAA,SAAAJ,OAAA,CAAAK,KAAA,MACF;;;;;IA2JRV,EAAA,CAAAC,cAAA,cAAyF;IACvFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFqDH,EAAA,CAAAI,UAAA,YAAAO,MAAA,CAAAC,YAAA,CAAAC,IAAA,CAA6B;IACtFb,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAH,MAAA,CAAAC,YAAA,CAAAG,OAAA,MACF;;;;;IAyDoBf,EAAA,CAAAC,cAAA,cACmC;IACjCD,EAAA,CAAAE,MAAA,GACF;;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADJH,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,0BAAAd,EAAA,CAAAgB,WAAA,OAAAC,MAAA,CAAAC,eAAA,CAAAC,QAAA,CAAAC,MAAA,QACF;;;;;;IArCJpB,EAAA,CAAAC,cAAA,SAAqC;IAGjCD,EAAA,CAAAqB,SAAA,cAG8B;IAChCrB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,aAAiB;IAEqBD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClEH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,kBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAKpDH,EAAA,CAAAC,cAAA,aAAiB;IACSD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIrDH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAAgD;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAIjFH,EAAA,CAAAC,cAAA,cAAiB;IAIbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAsB,UAAA,KAAAC,0CAAA,kBAGM;IACRvB,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,cAAiB;IAGbD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAITH,EAAA,CAAAC,cAAA,cAAiB;IACSD,EAAA,CAAAE,MAAA,IAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAI/FH,EAAA,CAAAC,cAAA,cAA0B;IAEhBD,EAAA,CAAAwB,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,IAAA;MAAA,MAAAT,QAAA,GAAAO,WAAA,CAAAG,SAAA;MAAA,MAAAC,MAAA,GAAA9B,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAF,MAAA,CAAAG,cAAA,CAAAd,QAAA,CAAqB;IAAA,EAAC;IAErCnB,EAAA,CAAAqB,SAAA,aAAoC;IAACrB,EAAA,CAAAE,MAAA,eACvC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxDJH,EAAA,CAAAM,SAAA,GAAmB;IAAnBN,EAAA,CAAAI,UAAA,QAAAe,QAAA,CAAAe,KAAA,EAAAlC,EAAA,CAAAmC,aAAA,CAAmB;IASYnC,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAoC,iBAAA,CAAAjB,QAAA,CAAAkB,KAAA,kBAAAlB,QAAA,CAAAkB,KAAA,CAAAC,QAAA,CAA2B;IAOvCtC,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAoC,iBAAA,CAAAjB,QAAA,CAAAoB,QAAA,CAAoB;IAKpBvC,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAoC,iBAAA,CAAApC,EAAA,CAAAwC,WAAA,SAAArB,QAAA,CAAAsB,eAAA,gBAAgD;IAMlEzC,EAAA,CAAAM,SAAA,GAA2H;IAA3HN,EAAA,CAAA0C,WAAA,qBAAAvB,QAAA,CAAAwB,MAAA,gCAAAxB,QAAA,CAAAwB,MAAA,uCAA2H;IAE/H3C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAK,QAAA,CAAAwB,MAAA,MACF;IACM3C,EAAA,CAAAM,SAAA,GAA0F;IAA1FN,EAAA,CAAAI,UAAA,SAAAe,QAAA,CAAAwB,MAAA,CAAAC,WAAA,oBAAAzB,QAAA,CAAAwB,MAAA,CAAAC,WAAA,sBAA0F;IAS1F5C,EAAA,CAAAM,SAAA,GAA2B;IAA3BN,EAAA,CAAAI,UAAA,UAAAe,QAAA,CAAA0B,WAAA,CAA2B;IAC/B7C,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAAc,kBAAA,MAAAK,QAAA,CAAA0B,WAAA,MACF;IAKwB7C,EAAA,CAAAM,SAAA,GAA8D;IAA9DN,EAAA,CAAAoC,iBAAA,EAAAjB,QAAA,CAAA2B,WAAA,kBAAA3B,QAAA,CAAA2B,WAAA,CAAAC,cAAA,yBAA8D;;;;;;IA8B1G/C,EAAA,CAAAC,cAAA,aAEuC;IAChBD,EAAA,CAAAwB,UAAA,mBAAAwB,6DAAA;MAAA,MAAAtB,WAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAsB,IAAA;MAAA,MAAAC,KAAA,GAAAxB,WAAA,CAAAyB,KAAA;MAAA,MAAAC,OAAA,GAAApD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAAoB,OAAA,CAAAC,eAAA,CAAAH,KAAA,CAAkB;IAAA,EAAC;IAAClD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAD/DH,EAAA,CAAAsD,WAAA,WAAAJ,KAAA,KAAAK,OAAA,CAAAC,WAAA,CAAkC;IACcxD,EAAA,CAAAM,SAAA,GAAW;IAAXN,EAAA,CAAAoC,iBAAA,CAAAc,KAAA,KAAW;;;;;;;;;IATnElD,EAAA,CAAAC,cAAA,cAAuE;IAG5CD,EAAA,CAAAwB,UAAA,mBAAAiC,wDAAA;MAAAzD,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAC,OAAA,GAAA3D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA2B,OAAA,CAAAN,eAAA,CAAAM,OAAA,CAAAH,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,0BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/EH,EAAA,CAAAsB,UAAA,IAAAsC,yCAAA,iBAIK;IAEL5D,EAAA,CAAAC,cAAA,aAAwE;IACjDD,EAAA,CAAAwB,UAAA,mBAAAqC,wDAAA;MAAA7D,EAAA,CAAA2B,aAAA,CAAA+B,IAAA;MAAA,MAAAI,OAAA,GAAA9D,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAgC,WAAA,CAAA8B,OAAA,CAAAT,eAAA,CAAAS,OAAA,CAAAN,WAAA,GAA8B,CAAC,CAAC;IAAA,EAAC;IAACxD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAXvDH,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,OAAoC;IAKrCxD,EAAA,CAAAM,SAAA,GAA+B;IAA/BN,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAC,WAAA,CAAAH,MAAA,CAAAI,UAAA,EAA+B;IAK9BnE,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAsD,WAAA,aAAAS,MAAA,CAAAP,WAAA,KAAAO,MAAA,CAAAI,UAAA,KAAiD;;;ADzT3E,OAAM,MAAOC,oBAAoB;EA8EjCF,YACUG,WAAuB,EACvBC,IAAe,EACfC,EAAe,EACfC,kBAAsC;IAHtC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,kBAAkB,GAAlBA,kBAAkB;IA9E5B,KAAAC,MAAM,GAAS,EAAE;IACjB,KAAAC,UAAU,GAAS,EAAE;IACrB,KAAAC,YAAY,GAAkB,EAAE;IAEhC,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,0BAA0B,GAAkB,EAAE;IAC9C,KAAAC,OAAO,GAAY,EAAE;IACnB,KAAAC,OAAO,GAAa,IAAI;IACtB,KAAAC,OAAO,GAAa,IAAI;IAC5B,KAAApE,YAAY,GAAG;MACbqE,IAAI,EAAE,KAAK;MACXpE,IAAI,EAAE,SAAS;MACfE,OAAO,EAAE;KACV;IACD,KAAAyC,WAAW,GAAG,CAAC;IACf,KAAA0B,QAAQ,GAAG,CAAC;IACZ,KAAAC,UAAU,GAAW,EAAE;IAKvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,4BAA4B,GAAG,KAAK;IAEpC,KAAAC,aAAa,GAAO;MACpBlE,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxB1C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IACD,KAAA0C,cAAc,GAAO;MACrBpE,MAAM,EAAC,CAAC;MACRmB,QAAQ,EAAC,EAAE;MACXI,MAAM,EAAC,EAAE;MACTT,KAAK,EAAC,EAAE;MACRG,KAAK,EAAC,IAAI;MACVI,eAAe,EAAC,IAAI8C,IAAI,CAAJ,CAAI;MACxB1C,WAAW,EAAC,EAAE;MACdC,WAAW,EAAC;KAEX;IAID,KAAA2C,iBAAiB,GAAa;MAC5BC,EAAE,EAAC,CAAC;MACJC,WAAW,EAAC,EAAE;MACdlD,eAAe,EAAC,IAAI8C,IAAI,EAAE;MAC1BK,IAAI,EAAC,IAAInG,WAAW,EAAE;MACtBoG,UAAU,EAAC,IAAIvG,KAAK,EAAE;MACtBwG,MAAM,EAAC;KAER;IACD,KAAA5E,eAAe,GAAU,EAAE;IAC3B,KAAA6E,OAAO,GAAU,EAAE;IACnB,KAAAC,gBAAgB,GAAQ,EAAE;IAE1B;IACA,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,aAAa,GAAU,IAAInG,KAAK,EAAE;IAClC,KAAAoG,cAAc,GAAY,KAAK;IAG/B,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAe,EAAE;IAE3B,KAAAC,eAAe,GAAG,IAAI/G,WAAW,EAAE;IACnC,KAAAgH,qBAAqB,GAAG,IAAIhH,WAAW,EAAE;IACzC,KAAAiH,SAAS,GAAG,IAAIjH,WAAW,EAAE;IA+I/B,KAAAkH,cAAc,GAAW,EAAE,CAAC,CAAC;IA+J7B,KAAAC,YAAY,GAAQ,EAAE;IAvSpB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACpC,EAAE,CAACqC,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrH,UAAU,CAACsH,QAAQ,EAAEtH,UAAU,CAACuH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3DlE,WAAW,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAACsH,QAAQ,EAAEtH,UAAU,CAACuH,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClEC,QAAQ,EAAE,CAAC,SAAS,EAAE,CAACxH,UAAU,CAACsH,QAAQ,CAAC;KAC5C,CAAC;EACJ;EACEG,QAAQA,CAAA;IACJ,IAAI,CAACzD,WAAW,GAAG,CAAC;IAEtB,IAAI,CAAC0D,YAAY,EAAE;IACnB,IAAI,CAAC7D,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;IACtC,IAAI,CAAC2D,cAAc,EAAE;IAUzB;IACA,IAAI,CAACX,SAAS,CAACY,YAAY,CACxBC,IAAI,CACH3H,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBE,SAAS,CAACyH,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAC3B,OAAO,IAAI,CAACnD,WAAW,CAACoD,YAAY,CAACH,KAAK,CAACC,IAAI,EAAE,CAAC;SACnD,MAAM;UACL,OAAO3H,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8H,SAAS,CAACjD,MAAM,IAAG;MAClB,IAAI,CAACK,OAAO,GAAGL,MAAM;IACvB,CAAC,CAAC;IAEJ;IAIA;IACA,IAAI,CAAC8B,qBAAqB,CAACa,YAAY,CACpCC,IAAI,CACH3H,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,EACtBG,GAAG,CAAEwH,KAAU,IAAI;MAEjB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,IAAI,CAAChB,qBAAqB,CAACoB,QAAQ,CAAC,IAAI,EAAE;UAAEC,SAAS,EAAE;QAAK,CAAE,CAAC;QAC/D,IAAI,CAACvE,eAAe,CAAC,CAAC,CAAC;;IAE3B,CAAC,CAAC,EACFxD,SAAS,CAACyH,KAAK,IAAG;MAChB,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAIA,KAAK,CAACC,IAAI,EAAE,CAACC,MAAM,GAAG,CAAC,EAAE;UAErC,IAAI,CAACnD,WAAW,CAACwD,iBAAiB,CAAC,IAAI,CAAC1C,UAAU,EAAC,IAAI,CAACoB,qBAAqB,CAACe,KAAK,EAAC,CAAC,EAAC,IAAI,CAACpC,QAAQ,CAAC,CAACwC,SAAS,CAAC;YAC7GI,IAAI,EAAGC,GAAG,IAAI;cACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;cAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;cAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;YACzC,CAAC;YACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;WAClC,CAAC;UACF,IAAI,CAAC9E,eAAe,CAAC,CAAC,CAAC;UAEb,OAAO,IAAI,CAACgB,WAAW,CAACgE,WAAW,CAACf,KAAK,CAACC,IAAI,EAAE,CAAC;SAClD,MAAM;UACL,OAAO3H,EAAE,CAAC,EAAE,CAAC;;;MAGjB,OAAOA,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,CACH,CACA8H,SAAS,CAACY,KAAK,IAAG;MACjB,IAAI,CAACzD,0BAA0B,GAAGyD,KAAK;IACzC,CAAC,CAAC;EAIJ;EAOAC,kBAAkBA,CAAC3C,IAAS;IAC1B,OAAOA,IAAI,GAAG,GAAGA,IAAI,CAACpF,SAAS,IAAIoF,IAAI,CAACnF,QAAQ,MAAMmF,IAAI,CAAClF,KAAK,EAAE,GAAG,EAAE;EACzE;EAGA8H,YAAYA,CAACnG,KAAU;IACrB,OAAQA,KAAK,GAAE,GAAGA,KAAK,CAACC,QAAQ,EAAE,GAAG,EAAE;EACzC;EAMC6E,cAAcA,CAAA;IAGb,IAAI,CAAC9C,WAAW,CAACoE,iBAAiB,EAAE,CAACf,SAAS,CAACgB,IAAI,IAAG;MACtD,IAAI,CAACrC,YAAY,GAAGqC,IAAI;IAE1B,CAAC,CAAC;EAGA;EAYAC,oBAAoBA,CAAC/C,IAAiB;IACpC,IAAI,CAACvC,eAAe,CAAC,CAAC,CAAC;EAEzB;EAQFA,eAAeA,CAACuF,IAAY;IAC1B,IAAI,CAACpF,WAAW,GAAGoF,IAAI;IAEvB,MAAMC,OAAO,GAAG,IAAI,CAAC1D,UAAU,CAACoC,IAAI,EAAE;IACtC,MAAM5E,MAAM,GAAG,IAAI,CAAC8D,cAAc,CAACc,IAAI,EAAE;IAEzC,IAAIuB,QAAQ,GAAG,EAAE;IACjB,MAAMC,OAAO,GAAG,IAAI,CAACxC,qBAAqB,CAACe,KAAK;IAEhD,IAAI,OAAOyB,OAAO,KAAK,QAAQ,EAAE;MAC/BD,QAAQ,GAAGC,OAAO,CAACxB,IAAI,EAAE;KAC1B,MAAM,IAAIwB,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAI,UAAU,IAAIA,OAAO,EAAE;MAC1ED,QAAQ,GAAGC,OAAO,CAACD,QAAQ,CAACvB,IAAI,EAAE;;IAGpC;IACA,IAAIuB,QAAQ,KAAK,EAAE,EAAE;MACnB,IAAI,CAACzE,WAAW,CAACwD,iBAAiB,CAACgB,OAAO,IAAI,EAAE,EAAEC,QAAQ,EAAEF,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QACzFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAAC2E,kBAAkB,CAAC,EAAE,EAAErG,MAAM,EAAEiG,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAACwD,iBAAiB,CAACgB,OAAO,EAAE,EAAE,EAAED,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAC7EI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAIU,OAAO,KAAK,EAAE,IAAIlG,MAAM,KAAK,EAAE,EAAE;MACnC,IAAI,CAAC0B,WAAW,CAAC2E,kBAAkB,CAACH,OAAO,EAAElG,MAAM,EAAEiG,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;QAClFI,IAAI,EAAGC,GAAG,IAAI;UACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;UAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;UAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;QACzC,CAAC;QACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;OAClC,CAAC;MACF;;IAGF;IACA,IAAI,CAAC9D,WAAW,CAAC4E,iBAAiB,CAACL,IAAI,EAAE,IAAI,CAAC1D,QAAQ,CAAC,CAACwC,SAAS,CAAC;MAChEI,IAAI,EAAGC,GAAG,IAAI;QACZ,IAAI,CAACrD,UAAU,GAAGqD,GAAG,CAACC,OAAO;QAC7B,IAAI,CAAC7D,UAAU,GAAG4D,GAAG,CAAC5D,UAAU;QAChC,IAAI,CAAC8D,iBAAiB,CAAC,IAAI,CAACvD,UAAU,CAAC;MACzC,CAAC;MACDwD,KAAK,EAAGC,GAAG,IAAKC,OAAO,CAACF,KAAK,CAACC,GAAG;KAClC,CAAC;EACJ;EAGQF,iBAAiBA,CAACvD,UAAiB;IACzC0D,OAAO,CAACc,GAAG,CAACxE,UAAU,CAAC;IAEvB;IACA,IAAI,CAACqB,OAAO,GAAGrB,UAAU,CAACyE,GAAG,CAACC,EAAE,IAAIA,EAAE,CAAChI,MAAM,CAAC;IAC9CgH,OAAO,CAACc,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAACnD,OAAO,CAAC;IAEjD,IAAI,CAAC1B,WAAW,CAACgF,oBAAoB,CAAC,IAAI,CAACtD,OAAO,CAAC,CAAC2B,SAAS,CAACgB,IAAI,IAAG;MAEjEA,IAAI,CAACY,OAAO,CAACC,WAAW,IAAG;QACzB,IAAI,CAACvD,gBAAgB,CAACuD,WAAW,CAAC1D,UAAU,CAACzE,MAAM,CAAC,GAAGmI,WAAW;QAClE,IAAI,CAACrI,eAAe,CAACqI,WAAW,CAAC1D,UAAU,CAACzE,MAAM,CAAC,GAAGmI,WAAW,CAAC3D,IAAI,CAACpF,SAAS,GAAG,GAAG,GAAG+I,WAAW,CAAC3D,IAAI,CAACnF,QAAQ;MAEpH,CAAC,CAAC;IACJ,CAAC,CAAC;EAKN;EAGE+I,cAAcA,CAAA;IAEZ,IAAI,CAACnG,eAAe,CAAC,CAAC,CAAC;EACzB;EAuBFoG,cAAcA,CAACC,KAAU;IACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAElC,IAAIF,IAAI,EAAE;MACR,MAAMG,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEL,IAAI,CAAC;MAE7B,IAAI,CAACrF,IAAI,CAAC2F,IAAI,CAAM,8BAA8B,EAAEH,QAAQ,CAAC,CAACpC,SAAS,CACpEwC,QAAQ,IAAI;QACX,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;UACjC,MAAMC,OAAO,GAAG,wBAAwBF,QAAQ,CAACC,QAAQ,EAAE;UAC3D/B,OAAO,CAACc,GAAG,CAAC,mBAAmB,EAAEkB,OAAO,CAAC;UAGzC,IAAI,CAACC,IAAI,CAACC,UAAU,CAAC;YACnBpI,KAAK,EAAEkI;WACR,CAAC;SACH,MAAM;UACLhC,OAAO,CAACF,KAAK,CAAC,2BAA2B,CAAC;;MAE9C,CAAC,EACAA,KAAK,IAAI;QACRE,OAAO,CAACF,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD,CAAC,CACF;;EAEL;EAMEqC,WAAWA,CAAA;IACT,IAAI,CAAC7D,YAAY,GAAG,EAAE;EACxB;EAEIQ,YAAYA,CAAA;IAEZ,IAAI,CAAC7C,WAAW,CAACmG,WAAW,EAAE,CAAC9C,SAAS,CAACgB,IAAI,IAAG;MAChD,IAAI,CAACjE,MAAM,GAAGiE,IAAI;IAEpB,CAAC,CAAC;EACF;EAMF;EACA+B,gBAAgBA,CAAC5J,IAAyB,EAAEE,OAAe;IACzD,IAAI,CAACH,YAAY,GAAG;MAClBqE,IAAI,EAAE,IAAI;MACVpE,IAAI,EAAEA,IAAI;MACVE,OAAO,EAAEA;KACV;IAED;IACA2J,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,EAAE;IACzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAAC/J,YAAY,CAACqE,IAAI,GAAG,KAAK;EAChC;EAEA;EAUA;EACA2F,kBAAkBA,CAACC,IAAS;IAC1B,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IAEtB,IAAI;MACF,MAAMC,OAAO,GAAG,IAAIvF,IAAI,CAACsF,IAAI,CAAC;MAC9B,IAAIE,KAAK,CAACD,OAAO,CAACE,OAAO,EAAE,CAAC,EAAE,OAAO,IAAI;MAEzC;MACA,OAAOF,OAAO,CAACG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAC3C,CAAC,OAAOhD,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5D,OAAO,IAAI;;EAEf;EAEA;EACAiD,QAAQA,CAACvC,IAAY;IACnB,IAAIA,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAG,IAAI,CAACzE,UAAU,EAAE;MACvC,IAAI,CAACd,eAAe,CAACuF,IAAI,CAAC;;EAE9B;EAEAwC,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC5H,WAAW,GAAG,IAAI,CAACW,UAAU,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACd,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA6H,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC7H,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACH,eAAe,CAAC,IAAI,CAACG,WAAW,GAAG,CAAC,CAAC;;EAE9C;EAEA;EACA8H,cAAcA,CAAA;IACZ,MAAMC,KAAK,GAAa,EAAE;IAC1B,MAAMC,cAAc,GAAG,CAAC,CAAC,CAAC;IAE1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnI,WAAW,GAAGkI,IAAI,CAACE,KAAK,CAACJ,cAAc,GAAG,CAAC,CAAC,CAAC;IAC9E,IAAIK,OAAO,GAAGH,IAAI,CAACI,GAAG,CAAC,IAAI,CAAC3H,UAAU,GAAG,CAAC,EAAEsH,SAAS,GAAGD,cAAc,GAAG,CAAC,CAAC;IAE3E;IACA,IAAIK,OAAO,GAAGJ,SAAS,GAAGD,cAAc,GAAG,CAAC,EAAE;MAC5CC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEE,OAAO,GAAGL,cAAc,GAAG,CAAC,CAAC;;IAGvD,KAAK,IAAIO,CAAC,GAAGN,SAAS,EAAEM,CAAC,IAAIF,OAAO,EAAEE,CAAC,EAAE,EAAE;MACzCR,KAAK,CAACS,IAAI,CAACD,CAAC,CAAC;;IAGf,OAAOR,KAAK;EACd;EAEA;EAEA;;;EAGAtJ,cAAcA,CAAC4D,UAAiB;IAC9B,IAAI,CAACK,aAAa,GAAG,IAAInG,KAAK,EAAE;IAChC,IAAI,CAACmG,aAAa,CAACL,UAAU,GAAGA,UAAU;IAC1C,IAAI,CAACc,SAAS,CAACsF,KAAK,EAAE;IACtB,IAAI,CAACtF,SAAS,CAAC2D,UAAU,CAAC;MACxBtD,QAAQ,EAAE;KACX,CAAC;IACF,IAAI,CAACb,cAAc,GAAG,IAAI;EAC5B;EAGA+F,eAAeA,CAAA;IACb,IAAI,CAAC/F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACD,aAAa,GAAG,IAAInG,KAAK,EAAE;IAChC,IAAI,CAAC4G,SAAS,CAACsF,KAAK,EAAE;EACxB;EAEA;;;EAGAE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACxF,SAAS,CAACyF,KAAK,EAAE;MACxB,MAAMC,SAAS,GAAG;QAChBxF,KAAK,EAAE,IAAI,CAACF,SAAS,CAAC2F,GAAG,CAAC,OAAO,CAAC,EAAEhF,KAAK;QACzCzE,WAAW,EAAE,IAAI,CAAC8D,SAAS,CAAC2F,GAAG,CAAC,aAAa,CAAC,EAAEhF,KAAK;QACrDN,QAAQ,EAAE,IAAI,CAACL,SAAS,CAAC2F,GAAG,CAAC,UAAU,CAAC,EAAEhF,KAAK;QAC/CzB,UAAU,EAAE,IAAI,CAACK,aAAa,CAACL;OAChC;MAED,IAAI,CAACxB,WAAW,CAACkI,aAAa,CAACF,SAAS,CAAC,CAAC3E,SAAS,CAAC;QAClDI,IAAI,EAAGoC,QAAQ,IAAI;UACjB9B,OAAO,CAACc,GAAG,CAAC,6BAA6B,EAAEgB,QAAQ,CAAC;UACpD,IAAI,CAACgC,eAAe,EAAE;UACtB;UACA,IAAI,CAAC7I,eAAe,CAAC,IAAI,CAACG,WAAW,CAAC;QACxC,CAAC;QACD0E,KAAK,EAAGA,KAAK,IAAI;UACfE,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAC/D;QACF;OACD,CAAC;KACH,MAAM;MACL;MACAsE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9F,SAAS,CAAC+F,QAAQ,CAAC,CAACpD,OAAO,CAACqD,GAAG,IAAG;QACjD,IAAI,CAAChG,SAAS,CAAC2F,GAAG,CAACK,GAAG,CAAC,EAAEC,aAAa,EAAE;MAC1C,CAAC,CAAC;;EAEN;EAEA;;;EAGAC,QAAQA,CAACC,SAAiB,EAAEC,SAAiB;IAC3C,MAAMC,KAAK,GAAG,IAAI,CAACrG,SAAS,CAAC2F,GAAG,CAACQ,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAEE,KAAK,IAAIA,KAAK,CAACH,QAAQ,CAACE,SAAS,CAAC,IAAIC,KAAK,CAACC,OAAO,CAAC;EAChE;EAEA;;;EAGAC,eAAeA,CAACJ,SAAiB;IAC/B,MAAME,KAAK,GAAG,IAAI,CAACrG,SAAS,CAAC2F,GAAG,CAACQ,SAAS,CAAC;IAC3C,IAAIE,KAAK,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACG,MAAM,EAAE;MAC1C,IAAIH,KAAK,CAACG,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,MAAML,SAAS,aAAa;;MAErC,IAAIE,KAAK,CAACG,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,MAAMC,cAAc,GAAGJ,KAAK,CAACG,MAAM,CAAC,WAAW,CAAC,CAACC,cAAc;QAC/D,OAAO,MAAMN,SAAS,2BAA2BM,cAAc,aAAa;;;IAGhF,OAAO,EAAE;EACX;;;uBA7iBWhJ,oBAAoB,EAAApE,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvN,EAAA,CAAAqN,iBAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAzN,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3N,EAAA,CAAAqN,iBAAA,CAAAO,EAAA,CAAAC,kBAAA;IAAA;EAAA;;;YAApBzJ,oBAAoB;MAAA0J,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCzBjCpO,EAAA,CAAAC,cAAA,cAAgB;UAGdD,EAAA,CAAAqB,SAAA,cAAsB;UAEtBrB,EAAA,CAAAC,cAAA,YAAO;UAAAD,EAAA,CAAAE,MAAA,uDAAgD;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAIjEH,EAAA,CAAAC,cAAA,WAAM;UAQND,EAAA,CAAAqB,SAAA,iBAAyB;UAIrBrB,EAAA,CAAAC,cAAA,aAA0B;UAExBD,EAAA,CAAAqB,SAAA,iBAES;UAETrB,EAAA,CAAAC,cAAA,cAAgC;UAGhCD,EAAA,CAAAE,MAAA,mCAA2B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,6FAAsE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK/EH,EAAA,CAAAC,cAAA,cAA8B;UAEtBD,EAAA,CAAAE,MAAA,wBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,8EAEP;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEJH,EAAA,CAAAC,cAAA,kBAA8B;UACTD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAE,MAAA,qBAE7B;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAITH,EAAA,CAAAC,cAAA,eAA4B;UAEID,EAAA,CAAAE,MAAA,2CAAmC;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEpEH,EAAA,CAAAC,cAAA,eAAqB;UAIJD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAqB,SAAA,iBAK6C;UAE7CrB,EAAA,CAAAC,cAAA,gCAG+D;UAA7DD,EAAA,CAAAwB,UAAA,4BAAA8M,0EAAAC,MAAA;YAAA,OAAkBF,GAAA,CAAA1F,oBAAA,CAAA4F,MAAA,CAAAC,MAAA,CAAAlH,KAAA,CAAyC;UAAA,EAAC;UAC5DtH,EAAA,CAAAsB,UAAA,KAAAmN,2CAAA,yBAEa;UACfzO,EAAA,CAAAG,YAAA,EAAmB;UAU7BH,EAAA,CAAAC,cAAA,eAA4B;UAKtBD,EAAA,CAAAwB,UAAA,2BAAAkN,8DAAAH,MAAA;YAAA,OAAAF,GAAA,CAAAlJ,UAAA,GAAAoJ,MAAA;UAAA,EAAwB,mBAAAI,sDAAA;YAAA,OACfN,GAAA,CAAA7E,cAAA,EAAgB;UAAA,EADD;UAH1BxJ,EAAA,CAAAG,YAAA,EAME;UACFH,EAAA,CAAAqB,SAAA,gBAAiC;UACnCrB,EAAA,CAAAG,YAAA,EAAM;UAkIRH,EAAA,CAAAC,cAAA,gBAAuB;UAInBD,EAAA,CAAAsB,UAAA,KAAAsN,oCAAA,kBAEM;UAGN5O,EAAA,CAAAC,cAAA,cAA6B;UAS6BD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC/DH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,cAAwC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxDH,EAAA,CAAAC,cAAA,cAAiD;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGjEH,EAAA,CAAAC,cAAA,aAAO;UACLD,EAAA,CAAAsB,UAAA,KAAAuN,mCAAA,mBA8DK;UACP7O,EAAA,CAAAG,YAAA,EAAQ;UAW1BH,EAAA,CAAAsB,UAAA,KAAAwN,oCAAA,kBAgBM;UAEJ9O,EAAA,CAAAG,YAAA,EAAM;UAMEH,EAAA,CAAAC,cAAA,eAAiB;UAGMD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAC,cAAA,aACW;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAACH,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAC,cAAA,aAAkD;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAI;;;;UApSzJH,EAAA,CAAAM,SAAA,IAAqC;UAArCN,EAAA,CAAAI,UAAA,gBAAAiO,GAAA,CAAA9H,qBAAA,CAAqC,oBAAAwI,GAAA;UAMrC/O,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,gBAAAiO,GAAA,CAAA9F,kBAAA,CAAkC;UAELvI,EAAA,CAAAM,SAAA,GAA6B;UAA7BN,EAAA,CAAAI,UAAA,YAAAiO,GAAA,CAAAxJ,0BAAA,CAA6B;UAkBhE7E,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAI,UAAA,YAAAiO,GAAA,CAAAlJ,UAAA,CAAwB;UA2IpBnF,EAAA,CAAAM,SAAA,GAAuB;UAAvBN,EAAA,CAAAI,UAAA,SAAAiO,GAAA,CAAAzN,YAAA,CAAAqE,IAAA,CAAuB;UAyBSjF,EAAA,CAAAM,SAAA,IAAa;UAAbN,EAAA,CAAAI,UAAA,YAAAiO,GAAA,CAAA3J,UAAA,CAAa;UA0EN1E,EAAA,CAAAM,SAAA,GAAoB;UAApBN,EAAA,CAAAI,UAAA,SAAAiO,GAAA,CAAAlK,UAAA,KAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}